package com.dc.iceage.config;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 */
public class MetaDataContextHolder {

    public static final Map<String, ConcurrentMap<String, ConcurrentMap<String, Object>>> contextMap = new ConcurrentHashMap<>();

    public static final ThreadLocal<String> contextHolder = new ThreadLocal<>();

    public static ConcurrentMap<String, ConcurrentMap<String, Object>> getContext() {
        String key = contextHolder.get();
        if (key == null) {
            return null;
        }
        return contextMap.get(key);
    }

    public static void init() {
        String uuid = UUID.randomUUID().toString();
        contextHolder.set(uuid);
        contextMap.put(uuid, new ConcurrentHashMap<>());
    }

    public static void clear() {
        String uuid = contextHolder.get();
        contextHolder.remove();
        if (StringUtils.isBlank(uuid)){
            return;
        }
        contextMap.remove(uuid);
    }
}
