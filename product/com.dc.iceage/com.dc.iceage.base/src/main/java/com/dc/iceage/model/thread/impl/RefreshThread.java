package com.dc.iceage.model.thread.impl;

import com.dc.iceage.model.type.ExecuteType;
import com.dc.springboot.core.model.thread.AbstractExecuteThread;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Set;

@Service
public class RefreshThread extends AbstractExecuteThread<ExecuteType> {

    @Override
    public Collection<ExecuteType> getTypes() {
        return Set.of(ExecuteType.REFRESH_CACHE);
    }

    @Override
    public String getName() {
        return "refresh";
    }
}
