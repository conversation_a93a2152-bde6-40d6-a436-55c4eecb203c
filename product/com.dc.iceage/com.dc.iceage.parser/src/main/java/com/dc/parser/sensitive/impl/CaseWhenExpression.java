package com.dc.parser.sensitive.impl;

import com.dc.sqlparser.nodes.TCaseExpression;
import com.dc.sqlparser.nodes.TExpression;
import com.dc.sqlparser.nodes.TWhenClauseItem;
import com.dc.sqlparser.nodes.TWhenClauseItemList;

/**
 * @description case_t
 */
public class CaseWhenExpression extends AbstractExpression {

    @Override
    public void handle(TExpression expression) {

        if (null != expression && null != expression.getCaseExpression()) {

            TCaseExpression caseExpression = expression.getCaseExpression();
            TWhenClauseItemList whenClauseItemList = caseExpression.getWhenClauseItemList();

            StatementContext context = super.getContext().createContext();
            context.setUseAlias(false);
            context.addEps(new BaseExpression());

            //------- 
            for (TWhenClauseItem clauseItem : whenClauseItemList) {
                if (null != clauseItem && clauseItem.getReturn_expr() != null) {
                    context.addNode(clauseItem.getReturn_expr());
                }
            }

            context.search();
        }
    }
}
