package com.dc.parser.constants;

import java.util.Arrays;
import java.util.List;

public class AuthConstant {

    public static final String connector = "^";
    public static final String desensitization_not_instance = "desensitization_not_instance_auth";
    public static final String desensitization_half_instance = "desensitization_half_instance_auth";
    public static final String desensitization_instance = "desensitization_instance_auth";
    public static final String desensitization_not_schema = "desensitization_not_schema_auth";
    public static final String desensitization_half_schema = "desensitization_half_schema_auth";
    public static final String desensitization_schema = "desensitization_schema_auth";
    public static final String desensitization_half_table = "desensitization_half_table_auth";
    public static final String desensitization_table = "desensitization_table_auth";
    public static final String desensitization_half_column = "desensitization_half_column_auth";
    public static final String desensitization_column = "desensitization_column_auth";

    public static final List<String> column_mask_action_name_keys = Arrays.asList(desensitization_half_column, desensitization_column);
    public static final List<String> mask_action_name_keys = Arrays.asList(desensitization_half_instance, desensitization_instance,
            desensitization_half_schema, desensitization_schema,
            desensitization_half_table, desensitization_table,
            desensitization_half_column, desensitization_column);
    public static final List<String> mask_level_action_name_keys = Arrays.asList(desensitization_half_instance, desensitization_instance,
            desensitization_half_schema, desensitization_schema,
            desensitization_half_table, desensitization_table);

    public static final List<String> pa_mask_action_name_keys = Arrays.asList(desensitization_not_instance, desensitization_half_instance, desensitization_instance,
            desensitization_not_schema, desensitization_half_schema, desensitization_schema,
            desensitization_half_table, desensitization_table,
            desensitization_half_column, desensitization_column);

    public static final String desensitization_instance_copy = "desensitization_instance_copy_auth";
    public static final String desensitization_schema_copy = "desensitization_schema_copy_auth";
    public static final String desensitization_table_copy = "desensitization_table_copy_auth";
    public static final String desensitization_column_copy = "desensitization_column_copy_auth";

    public static final String PRIVATE_TABLE_INSTANCE_AUTH= "private_table_instance_auth";
}
