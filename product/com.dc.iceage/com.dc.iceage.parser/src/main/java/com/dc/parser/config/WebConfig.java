package com.dc.parser.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * <AUTHOR>
 */
//@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new BatchSqlInterceptor())
                .addPathPatterns("/preCheck/preCheckSqlBatch",
                        "/sql/preCheckSqlBatch",
                        "/preCheck/executeSqlBatch",
                        "/sql/executeSqlBatch"
                );
    }
}
