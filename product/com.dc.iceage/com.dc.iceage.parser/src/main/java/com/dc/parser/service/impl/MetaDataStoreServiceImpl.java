package com.dc.parser.service.impl;

import com.dc.annotation.SQL;
import com.dc.parser.config.annotation.MetaStore;
import com.dc.parser.service.MetaDataStoreService;
import com.dc.parser.util.ExecuteSqlUtil;
import com.dc.parser.util.HandleResultUtil;
import com.dc.parser.util.MapValueUtils;
import com.dc.repository.mysql.mapper.SchemaMapper;
import com.dc.repository.mysql.model.DcMetadata;
import com.dc.repository.mysql.model.Schema;
import com.dc.repository.mysql.service.DcMetadataService;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.summer.ext.dm.model.DmDataSource;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.sql.SynonymFieldData;
import com.dc.summer.parser.sql.model.TableColumnModel;
import com.dc.type.DatabaseType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MetaDataStoreServiceImpl implements MetaDataStoreService {

    @Resource
    private DcMetadataService dcMetadataService;

    @Resource
    private SchemaMapper schemaMapper;

    @Override
    @MetaStore
    public Optional<Schema> getRealSchema(String uniqueKey, String schemaName, String catalogName) {
        List<Schema> schemas = schemaMapper.getSchemasByNameIgnoreCase(uniqueKey, catalogName, schemaName);
        return schemas.stream().filter(schema -> schemaName.equals(schema.getSchema_name()))
                .findFirst()
                .or(() -> Optional.ofNullable(schemas.isEmpty() ? null : schemas.get(0)));
    }

    @Override
    @MetaStore
    public String getTableRealName(ParserParamDto paramDTO, String tableName, String schemaName) {

        final DBCExecutionContext executionContext = paramDTO.getExecutionContext();

        String firstName = "";

        if (executionContext == null) {
            return firstName;
        }

        try {

            if (DatabaseType.MYCAT.getValue().equals(paramDTO.getDbType())) {
                HandleResultUtil.buildDefaultSchema(executionContext, schemaName, null);
            }

            final DBPDataSource dataSource = executionContext.getDataSource();
            final DBPDataSourceInfo dataSourceInfo = dataSource.getInfo();
            // 优先获取同名表
            final Comparator<String> tableNameComparator = Comparator
                    .<String, Boolean>comparing(s -> !tableName.equals(s))
                    .thenComparing(Comparator.naturalOrder());

            // 1. DC 库获取
            List<DcMetadata> metadataList = dcMetadataService.findTableByName(paramDTO.getInstance().getId(), schemaName, tableName);
            List<String> metadataTableNames = metadataList.stream()
                    .map(DcMetadata::getObjectName)
                    .sorted(tableNameComparator)
                    .collect(Collectors.toList());

            for (String metadataTableName : metadataTableNames) {
                if (firstName.isBlank()) {
                    firstName = metadataTableName;
                }

                if (isExistTable(paramDTO, schemaName, metadataTableName, dataSourceInfo, dataSource, executionContext)) {
                    return metadataTableName;
                }
            }

            // 2. 用户库获取
            @SQL String query;
            if (dataSource instanceof DmDataSource) {
                String schemaNameUpper = schemaName.toUpperCase(Locale.ROOT);
                String tableNameUpper = tableName.toUpperCase(Locale.ROOT);
                query = dataSourceInfo.getSchemaIdSql(schemaNameUpper);
                List<Map<String, Object>> list = DBExecUtils.executeQuery(new VoidProgressMonitor(), executionContext, "Query Real Table Schema Id", query);
                List<Long> schemaId = dataSourceInfo.getSchemaId(list);
                query = dataSourceInfo.getTableRealNameSql(schemaId, schemaNameUpper, tableNameUpper);
            } else {
                query = dataSourceInfo.getTableRealNameSql(schemaName, tableName);
            }

            List<Map<String, Object>> list = DBExecUtils.executeQuery(new VoidProgressMonitor(), executionContext, "Query Real Table Name", query);

            Iterator<String> iterator = list.stream()
                    .map(result -> dataSourceInfo.getTableRealName(Collections.singletonList(result)))
                    .sorted(tableNameComparator)
                    .iterator();

            while (iterator.hasNext()) {
                String tableRealName = iterator.next();

                if (firstName.isBlank()) {
                    firstName = tableRealName;
                }

                if (isExistTable(paramDTO, schemaName, tableRealName, dataSourceInfo, dataSource, executionContext)) {
                    return tableRealName;
                }
            }

            return firstName;

        } catch (Exception e) {
            log.error("get table real name error!", e);
        } finally {
            if (DatabaseType.MYCAT.getValue().equals(paramDTO.getDbType())) {
                HandleResultUtil.buildDefaultSchema(executionContext, paramDTO.getSchemaName(), null);
            }
        }

        return firstName;
    }

    private static boolean isExistTable(ParserParamDto parserParamDto, String schemaName, String tableName, DBPDataSourceInfo dataSourceInfo, DBPDataSource dataSource, DBCExecutionContext executionContext) {

        final String structSeparator = String.valueOf(SQLUtils.getDialectFromDataSource(dataSource).getStructSeparator());

        String frameworkName = null;
        if (StringUtils.isNotBlank(parserParamDto.getFrameworkName()) && !DatabaseType.getCatalogDatabaseIntegerValueList().contains(parserParamDto.getDbType())) {
            frameworkName = parserParamDto.getFrameworkName();
            tableName = tableName.replaceFirst(frameworkName + structSeparator, "");
        }

        String name = DBUtils.getConcatName(structSeparator,
                StringUtils.isNotBlank(schemaName) ? DBUtils.getQuotedIdentifier(dataSource, schemaName) : null,
                StringUtils.isNotBlank(frameworkName) ? DBUtils.getQuotedIdentifier(dataSource, frameworkName) : null,
                StringUtils.isNotBlank(tableName) ? DBUtils.getQuotedIdentifier(dataSource, tableName) : null);

        String checkTableExistenceSql = dataSourceInfo.getCheckTableExistenceSql(name);

        if (checkTableExistenceSql == null) {
            return false;
        }

        try (DBCSession session = executionContext.openSession(new VoidProgressMonitor(), DBCExecutionPurpose.UTIL, "Check table existence");
             final DBCStatement dbStat = session.prepareStatement(DBCStatementType.SCRIPT, checkTableExistenceSql, false, false, false)) {
            DBExecUtils.setStatementFetchSize(dbStat, 0, 1, 1);
            dbStat.executeStatement();
            return true;
        } catch (Exception e) {
            log.warn("Check table existence error, [{}] is not find.", name);
        }
        return false;
    }

    /**
     * 查询主键
     */
    @Override
    @MetaStore
    public List<String> getColumnPrimaryKey(ParserParamDto paramDTO, String tableName, String schemaName) {
        try {

            if (DatabaseType.MYCAT.getValue().equals(paramDTO.getDbType())) {
                HandleResultUtil.buildDefaultSchema(paramDTO.getExecutionContext(), schemaName, null);
            }

            String query = paramDTO.getExecutionContext().getDataSource().getInfo().getPrimaryKeySql(schemaName, tableName);
            List<Map<String, Object>> list = ExecuteSqlUtil.executeSql(paramDTO.getExecutionContext(), query);
            List<String> columns = paramDTO.getExecutionContext().getDataSource().getInfo().getPrimaryKeyColumns(list);

            if (DatabaseType.MYCAT.getValue().equals(paramDTO.getDbType())) {
                HandleResultUtil.buildDefaultSchema(paramDTO.getExecutionContext(), paramDTO.getSchemaName(), null);
            }

            return columns;

        } catch (Exception e) {
            log.error("get column primary key error!", e);
        }

        return new ArrayList<>();
    }

    @Override
    @MetaStore
    public List<TableColumnModel> getTableColumns(ParserParamDto paramDTO, String tableName, String schemaName, boolean isTable) {
        List<TableColumnModel> columns = new ArrayList<>();

        try {
            List<Map<String, Object>> list;
            if (Arrays.asList(DatabaseType.HIVE.getValue(), DatabaseType.IMPALA.getValue(),
                    DatabaseType.INCEPTOR.getValue(), DatabaseType.SPARK.getValue()).contains(paramDTO.getDbType())) {
                list = ExecuteSqlUtil.getJDBCTableColumns(paramDTO, null, schemaName, tableName);
            } else {
                if (DatabaseType.MYCAT.getValue().equals(paramDTO.getDbType())) {
                    HandleResultUtil.buildDefaultSchema(paramDTO.getExecutionContext(), schemaName, null);
                }

                if (!isTable) {
                    SynonymFieldData synonymField = ExecuteSqlUtil.getSynonymField(paramDTO.getExecutionContext(), schemaName, tableName);
                    if (synonymField != null) {
                        schemaName = StringUtils.defaultString(synonymField.getSourceSchema(), schemaName);
                        tableName = StringUtils.defaultString(synonymField.getSourceTable(), tableName);
                    }
                }

                String query = paramDTO.getExecutionContext().getDataSource().getInfo().getTableColumnSql(schemaName, tableName);

                list = ExecuteSqlUtil.executeSql(paramDTO.getExecutionContext(), query, true);

                if (DatabaseType.MYCAT.getValue().equals(paramDTO.getDbType())) {
                    HandleResultUtil.buildDefaultSchema(paramDTO.getExecutionContext(), paramDTO.getSchemaName(), null);
                }
            }

            for (Map<String, Object> map : list) {
                TableColumnModel tableColumnModel = new TableColumnModel();
                if (Arrays.asList(DatabaseType.ADBMYSQL3.getValue(), DatabaseType.MYCAT.getValue(), DatabaseType.ADBMYSQL2.getValue()).contains(paramDTO.getDbType())) {
                    tableColumnModel.setColumnName(map.get("Field") == null ? null : map.get("Field").toString());
                    tableColumnModel.setColumnType(map.get("Type") == null ? null : map.get("Type").toString());
                    Object columnComment = map.get("Comment");
                    if (columnComment == null || columnComment.toString().isEmpty()) {
                        tableColumnModel.setColumnComment("--");
                    } else {
                        tableColumnModel.setColumnComment(columnComment.toString());
                    }
                } else {
                    tableColumnModel.setColumnName(MapValueUtils.getIgnoreCase(map, "COLUMN_NAME").toString());
                    tableColumnModel.setColumnType(MapValueUtils.getIgnoreCase(map, "COLUMN_TYPE").toString());
                    tableColumnModel.setDataType(MapValueUtils.getIgnoreCase(map, "DATA_TYPE").toString());
                    Object isPrimaryKey = MapValueUtils.getIgnoreCase(map, "IS_PRIMARY_KEY", false);
                    if (isPrimaryKey != null) {
                        tableColumnModel.setIsPrimaryKey(Integer.valueOf(isPrimaryKey.toString()));
                    }
                    Object columnComment = MapValueUtils.getIgnoreCase(map, "COMMENTS", false);
                    if (columnComment == null || columnComment.toString().isEmpty()) {
                        tableColumnModel.setColumnComment("--");
                    } else {
                        tableColumnModel.setColumnComment(columnComment.toString());
                    }

                    if (DatabaseType.OSCAR_CLUSTER.getValue().equals(paramDTO.getDbType())) {
                        paramDTO.setTableId(MapValueUtils.getIgnoreCase(map, "tableid").toString());
                    }
                }

                if (!paramDTO.getExecutionContext().getDataSource().getInfo().isQueryComment()) {
                    tableColumnModel.setColumnComment("");
                }

                columns.add(tableColumnModel);
            }

        } catch (Exception e) {
            log.error("get table columns error!", e);
        }

        return columns;
    }

}
