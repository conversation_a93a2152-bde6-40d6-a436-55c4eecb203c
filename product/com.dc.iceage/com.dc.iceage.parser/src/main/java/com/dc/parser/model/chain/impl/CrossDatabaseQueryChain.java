package com.dc.parser.model.chain.impl;

import com.dc.parser.model.chain.SqlCheckParserChain;
import com.dc.parser.service.sql.WebSQLParserInfo;
import com.dc.springboot.core.model.execution.CrossDatabaseQueryModel;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.springboot.core.model.result.WebSQLParserResult;
import com.dc.summer.parser.utils.model.SqlParseModel;

public class CrossDatabaseQueryChain extends SqlCheckParserChain {

    public CrossDatabaseQueryChain(ParserParamDto parserParamDto, SqlParseModel sqlParseModel, WebSQLParserInfo webSQLParserInfo) {
        super(parserParamDto, sqlParseModel, webSQLParserInfo);
    }

    @Override
    public boolean proceed(WebSQLParserResult webSQLParserResult) {

        final String dblinkName = webSQLParserInfo.getDblinkName();

        sqlParseModel.getSqlAuthModelList().get(0).setDblinkName(dblinkName);

        CrossDatabaseQueryModel crossDatabaseQueryModel = new CrossDatabaseQueryModel();
        crossDatabaseQueryModel.setCatalogName(parserParamDto.getCatalogName());
        crossDatabaseQueryModel.setConnectId(parserParamDto.getInstance().getUnique_key());
        crossDatabaseQueryModel.setSchemaName(parserParamDto.getSchemaName());
        crossDatabaseQueryModel.setTableName(sqlParseModel.getSqlAuthModelList().get(0).getName());
        crossDatabaseQueryModel.setDbLinkName(dblinkName);

        webSQLParserResult.getCrossDatabaseQueryModels().add(crossDatabaseQueryModel);
        return true;
    }

}
