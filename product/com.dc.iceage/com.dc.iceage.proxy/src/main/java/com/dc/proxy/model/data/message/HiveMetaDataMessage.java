package com.dc.proxy.model.data.message;

import com.dc.springboot.core.model.database.ConnectionMessage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("表消息")
public class HiveMetaDataMessage extends ConnectionMessage {

    @ApiModelProperty(value = "目录", example = "demo_catalog")
    private String catalog;

    @ApiModelProperty(value = "模式名", example = "demo_schema")
    private String schemaPattern;

    @ApiModelProperty(value = "表名", example = "demo_table")
    private String tableNamePattern;

    @ApiModelProperty(value = "表名列表", example = "[table1, table2]")
    private List<String> tableList;

    @ApiModelProperty(value = "pattern", example = "pattern")
    private String pattern;

    @ApiModelProperty(value = "表类型", example = "TABLE,VIEW")
    private List<String> types;

    @ApiModelProperty(value = "分页limit", example = "10")
    private Integer limit;

    @ApiModelProperty(value = "分页offset", example = "10")
    private Integer offset;

    @ApiModelProperty(value = "分级管理员", example = "a,b,c")
    private List<String> instances;

}
