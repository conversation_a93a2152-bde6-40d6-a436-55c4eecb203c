package com.dc.workorder.model;

import com.dc.summer.model.sql.SqlFieldData;
import lombok.Data;

import java.util.List;

@Data
public class OrderModel {

    private Long id;

    private Integer fileType;  //FileType 枚举

    private String applyContent;

//    private Integer charset;    //字符集 是否不一样 ，如果不一样 校验状态改为失败

    private String code = "utf-8";// 文件编码

    private Integer auditScope;

    private String redirectBackUrl;

    private String requestId;

    private List<SqlFieldData> fields;

}
