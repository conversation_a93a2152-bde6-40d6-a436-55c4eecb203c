package com.dc.summer.service.result;

import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.File;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class WorkOrderTransferResult {
    
    private File file;

    private String message;

    @JsonIgnore
    private Boolean sensitiveSelect;

    @JsonIgnore
    private String queryText;

    // 异步执行
    @JsonIgnore
    private String resultSetMetaData;

    @JsonIgnore
    private Throwable error;

    @JsonIgnore
    private WebSQLQueryResult queryResult;

}
