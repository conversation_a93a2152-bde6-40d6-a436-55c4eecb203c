package com.dc.summer.model.chain.impl;

import com.dc.springboot.core.model.execution.CrossDatabaseQueryModel;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.summer.DBException;
import com.dc.summer.exec.unity.model.UnityUtils;
import com.dc.springboot.core.model.chain.AbstractChain;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.service.sql.WebSQLContextInfo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class CrossDatabaseQueryCloseChain extends AbstractChain<WebSQLQueryResult> {

    private final List<CrossDatabaseQueryModel> crossDatabaseQueryModels;

    private final WebSQLContextInfo contextInfo;

    private final DBRProgressMonitor monitor;

    public CrossDatabaseQueryCloseChain(List<CrossDatabaseQueryModel> crossDatabaseQueryModels,
                                        WebSQLContextInfo contextInfo, DBRProgressMonitor monitor) {
        this.crossDatabaseQueryModels = crossDatabaseQueryModels;
        this.contextInfo = contextInfo;
        this.monitor = monitor;
    }

    @SneakyThrows(DBException.class)
    @Override
    public boolean proceed(WebSQLQueryResult queryResult) {

        if (!crossDatabaseQueryModels.isEmpty()) {
            UnityUtils.transformNestedConfig(contextInfo.getExecutionContext().getConfiguration(),
                    nestedConnectionConfiguration -> nestedConnectionConfiguration.getCloseableObject().close());
        }

        return true;
    }
}
