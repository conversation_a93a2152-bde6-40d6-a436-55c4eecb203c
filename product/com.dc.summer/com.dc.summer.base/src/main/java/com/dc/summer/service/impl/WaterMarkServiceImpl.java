package com.dc.summer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dc.repository.mysql.mapper.OrganizationMapper;
import com.dc.repository.mysql.mapper.UserMapper;
import com.dc.repository.mysql.mapper.WaterMarkMapper;
import com.dc.repository.mysql.model.Organization;
import com.dc.repository.mysql.model.User;
import com.dc.repository.mysql.model.WaterMark;
import com.dc.repository.mysql.utils.UserUtils;
import com.dc.summer.service.WaterMarkService;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.PropertyPlaceholderHelper;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WaterMarkServiceImpl extends ServiceImpl<WaterMarkMapper, WaterMark> implements WaterMarkService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private OrganizationMapper organizationMapper;

    protected final Gson gson = new GsonBuilder().serializeNulls().create();

    /**
     * 用户的真实姓名
     */
    private static final String USER_REAL_NAME_VAR ="user.realName";
    /**
     * 用户的联系电话
     */
    private static final String USER_PHONE_VAR ="user.phone";
    /**
     * 用户的所属组织
     */
    private static final String USER_ORGANIZATION_VAR ="user.organization";
    /**
     * 用户的用户名
     */
    private static final String USER_USERNAME_VAR ="user.username";
    /**
     * 用户的联系电话后四位
     */
    private static final String USER_PHONELASTFOUR_VAR ="user.phoneLastFour";
    /**
     * 当前日期
     */
    private static final String DATE_VAR = "date";


    @Override
    public WaterMark getRealWaterMark(String userId) {
        log.info("Method getRealWaterMark called with userId: {}", userId);
        WaterMark waterMark = getOne(new LambdaQueryWrapper<WaterMark>()
                .eq(WaterMark::getIsActive, 1)
                .eq(WaterMark::getIsDelete, 0)
        );
        if (waterMark == null) {
            log.warn("No active WaterMark found for userId: {}", userId);
            return null;
        }

        String waterMarkTemplate = waterMark.getContent();
        log.debug("WaterMark template retrieved: {}", waterMarkTemplate);

        User user = userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getUniqueKey, userId));
        if (user == null) {
            log.warn("No user found for userId: {}", userId);
            return null;
        }

        log.debug("User retrieved: {}", user);

        List<String> orgIds = Arrays.asList(user.getROrgIds().split(","));
        List<Organization> organizations = organizationMapper.selectList(new LambdaQueryWrapper<Organization>()
                .in(Organization::getUniqueKey, orgIds)
        );

        String organizationName = "";
        if (CollectionUtils.isNotEmpty(organizations)) {
            organizationName = organizations.stream()
                    .map(Organization::getOrgNames)
                    .map(orgNames -> {
                        List<String> list = gson.fromJson(orgNames, List.class);
                        return StringUtils.join(list, "/");
                    })
                    .collect(Collectors.joining(","));
            log.debug("Organizations found: {}", organizationName);
        } else {
            log.warn("No organizations found for orgIds: {}", orgIds);
        }

        String phone = UserUtils.getDecryptedPhone(user);
        Map<String, String> variables = Maps.newHashMapWithExpectedSize(6);
        variables.put(USER_REAL_NAME_VAR, StringUtils.defaultString(user.getRealName()));
        variables.put(USER_PHONE_VAR, StringUtils.defaultString(phone));
        variables.put(USER_PHONELASTFOUR_VAR, StringUtils.defaultString(StringUtils.right(phone, 4)));
        variables.put(USER_USERNAME_VAR, StringUtils.defaultString(user.getUsername()));
        variables.put(USER_ORGANIZATION_VAR, StringUtils.defaultString(organizationName));
        variables.put(DATE_VAR, DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(new Date()));

        PropertyPlaceholderHelper placeholderHelper = new PropertyPlaceholderHelper("${", "}");
        String realWaterMark = placeholderHelper.replacePlaceholders(waterMarkTemplate, variables::get);

        log.debug("Real WaterMark generated: {}", realWaterMark);

        waterMark.setContent(realWaterMark);
        log.info("WaterMark generation complete for userId: {}", userId);
        return waterMark;
    }

}
