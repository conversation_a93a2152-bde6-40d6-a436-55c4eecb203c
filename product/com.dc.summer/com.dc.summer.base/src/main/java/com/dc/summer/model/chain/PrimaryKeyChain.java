package com.dc.summer.model.chain;

import com.dc.springboot.core.model.chain.AbstractChain;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public abstract class Primary<PERSON>eyChain extends AbstractChain<WebSQLQueryResult> {

    protected final List<String> primaryKeyColumns = new ArrayList<>();

    public PrimaryKeyChain(List<String> primaryKeyColumns) {
        if (primaryKeyColumns != null && primaryKeyColumns.size() > 0) {
            for (String pkColumn : primaryKeyColumns) {
                if (pkColumn.equalsIgnoreCase("ROWID")) {
                    continue;
                }
                this.primaryKeyColumns.add(pkColumn);
            }
        }
    }
}
