package com.dc.summer.model.counter;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;

import java.sql.SQLException;

@Slf4j
public class SqlSessionCounter {

    private static final ThreadLocal<SqlSession> SQL_SESSION_THREAD_LOCAL = new ThreadLocal<>();

    public static SqlSession getSqlSession() {
        return SQL_SESSION_THREAD_LOCAL.get();
    }

    public static void setSqlSession(SqlSession sqlSession) {
        SQL_SESSION_THREAD_LOCAL.set(sqlSession);
    }

    public static void close() {

        SqlSession sqlSession = SQL_SESSION_THREAD_LOCAL.get();

        try {
            sqlSession.getConnection().close();
            sqlSession.close();
        } catch (SQLException e) {
            log.error("SqlSession 关闭失败！", e);
        } finally {
            SQL_SESSION_THREAD_LOCAL.remove();
        }
    }
}
