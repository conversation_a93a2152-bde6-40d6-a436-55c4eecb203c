package com.dc.summer.model.chain.impl;

import com.dc.repository.mysql.mapper.DatabaseConnectionMapper;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.execution.CrossDatabaseQueryModel;
import com.dc.springboot.core.model.message.ExecuteEvent;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.springboot.core.model.result.WebSQLQueryResult;
import com.dc.summer.DBException;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.exec.unity.model.data.NestedConnectionConfiguration;
import com.dc.springboot.core.model.chain.AbstractChain;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.runtime.jobs.InvalidateJob;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.type.DatabaseType;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class CrossDatabaseQueryOpenChain extends AbstractChain<WebSQLQueryResult> {

    private final List<CrossDatabaseQueryModel> crossDatabaseQueryModels;

    private final WebSQLContextInfo contextInfo;

    private final DBRProgressMonitor monitor;

    private final List<ExecuteEvent> executeEvents;

    public CrossDatabaseQueryOpenChain(List<CrossDatabaseQueryModel> crossDatabaseQueryModels,
                                       WebSQLContextInfo contextInfo, DBRProgressMonitor monitor, List<ExecuteEvent> executeEvents) {
        this.crossDatabaseQueryModels = crossDatabaseQueryModels;
        this.contextInfo = contextInfo;
        this.monitor = monitor;
        this.executeEvents = executeEvents;
    }

    @SneakyThrows(DBException.class)
    @Override
    public boolean proceed(WebSQLQueryResult queryResult) {

        if (contextInfo.getConfiguration().getDatabaseType() == DatabaseType.CALCITE) {
            executeEvents.clear();
        }

        if (!crossDatabaseQueryModels.isEmpty()) {
            final DatabaseConnectionMapper databaseConnectionMapper = Objects.requireNonNull(Resource.getBean(DatabaseConnectionMapper.class));

            Map<String, DatabaseConnectionDto> cache = crossDatabaseQueryModels
                    .stream()
                    .map(CrossDatabaseQueryModel::getConnectId)
                    .distinct()
                    .collect(Collectors.toMap(s -> s, connectId ->
                            SummerMapper.INSTANCE.toDatabaseConnectionDto(databaseConnectionMapper.getConnectionByUniqueKey(connectId))));

            NestedConnectionConfiguration configuration = new NestedConnectionConfiguration();
            NestedConnectionConfiguration nestedConfiguration = configuration;

            for (CrossDatabaseQueryModel crossDatabaseQueryModel : crossDatabaseQueryModels) {
                DatabaseConnectionDto databaseConnectionDto = cache.get(crossDatabaseQueryModel.getConnectId());

                ConnectionConfig connectionConfig = databaseConnectionDto
                        .buildConnectionConfig(crossDatabaseQueryModel.getSchemaName(), crossDatabaseQueryModel.getCatalogName());
                ConnectionConfiguration connectionConfiguration = connectionConfig.getConnectionConfiguration();

                nestedConfiguration.setNext(SummerMapper.INSTANCE.toNestedConnectionConfiguration(crossDatabaseQueryModel, connectionConfiguration));
                nestedConfiguration = nestedConfiguration.getNext();
                final WebSQLContextInfo isolatedContext = WebSQLContextInfo.openIsolatedContext(connectionConfig);
                nestedConfiguration.setCloseableObject(isolatedContext);
                nestedConfiguration.setExecutionContext(isolatedContext.getExecutionContext());
                nestedConfiguration.setProgressMonitor(monitor);

                executeEvents.add(SummerMapper.INSTANCE.toExecuteEvent(
                        contextInfo.getUser(),
                        DBUtils.getConcatName(isolatedContext.getDataSource(), crossDatabaseQueryModel.getSchemaName(), crossDatabaseQueryModel.getCatalogName()),
                        databaseConnectionDto,
                        isolatedContext.getConfiguration().getDatabaseType()));
            }

            InvalidateJob.invalidateDataSource(
                    monitor,
                    contextInfo.getDataSource(),
                    false,
                    true,
                    contextInfo.getExecutionContext(),
                    configuration);
        }

        return true;
    }
}
