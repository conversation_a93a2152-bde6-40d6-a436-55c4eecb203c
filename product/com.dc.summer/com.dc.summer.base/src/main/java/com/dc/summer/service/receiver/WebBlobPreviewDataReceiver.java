package com.dc.summer.service.receiver;

import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.summer.model.type.WebDataFormat;
import com.dc.springboot.core.model.sensitive.DataDesensitizeProcessor;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.summer.model.struct.DBSDataContainer;


public class WebBlobPreviewDataReceiver extends WebDataReceiver {

    private final ResultFormat resultFormat = new ResultFormat();

    private final DataDesensitizeProcessor dataDesensitize;

    public WebBlobPreviewDataReceiver(WebSQLContextInfo contextInfo,
                               DBSDataContainer dataContainer,
                               WebDataFormat dataFormat) {
        super(contextInfo, dataContainer, dataFormat);
        this.dataDesensitize = new DataDesensitizeProcessor();
    }

    @Override
    protected ResultFormat getResultFormat() {
        return this.resultFormat;
    }

    @Override
    protected DataDesensitizeProcessor getDataDesensitizeProcessor() {
        return this.dataDesensitize;
    }


    @Override
    protected boolean needCacheResult() {
        return true;
    }

}
