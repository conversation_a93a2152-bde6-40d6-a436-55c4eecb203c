package com.dc.summer.service;

import com.dc.springboot.core.model.exception.ResultException;
import com.dc.springboot.core.model.result.WebSQLTransferResult;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.WebAsyncTaskReact;
import com.dc.summer.model.data.message.CombineFileMessage;
import com.dc.summer.model.data.message.TaskInfoMessage;
import com.dc.summer.model.data.message.TaskResultMessage;
import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.summer.model.exec.DBCException;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.List;

public interface ResultService {

    void saveStringValue(String taskId, WebSQLExecuteInfo value, Long expirationTime) throws JsonProcessingException;

    void hsetExportResult(String batchId, String token, WebSQLTransferResult transferResult, Long expirationTime) throws JsonProcessingException;

    void addListValue(String taskId, WebSQLExecuteInfo value, Long expirationTime, int index);

    WebSQLExecuteInfo getStringValue(TaskInfoMessage message) throws ResultException;

    WebSQLExecuteInfo getStringValue(TaskResultMessage message) throws ResultException;

    WebAsyncTaskInfo getAsyncTaskInfo(TaskInfoMessage message) throws DBCException;

    WebSQLExecuteInfo asyncSqlExecuteResults(TaskResultMessage message) throws ResultException;

    WebAsyncTaskReact syncSqlExecuteResults(TaskInfoMessage message) throws DBCException, ResultException, InterruptedException;

    List<WebSQLTransferResult> hgetExportResult(CombineFileMessage combineFileMessage);
}
