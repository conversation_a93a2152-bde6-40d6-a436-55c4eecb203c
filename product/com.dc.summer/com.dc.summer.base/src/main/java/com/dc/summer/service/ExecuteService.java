package com.dc.summer.service;

import com.dc.springboot.core.model.execution.*;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.summer.model.data.DynamicSqlInfo;
import com.dc.summer.model.data.message.*;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.springboot.core.model.result.WebSQLExecuteInfo;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;

/**
 * 执行业务层
 * 与 SQL 执行相关，或者与会话相关
 */
public interface ExecuteService {

    WebSQLExecuteInfo executeBindingStatement(BindingExecuteMessage message);

    WebSQLExecuteInfo syncExecuteSingleQuery(SingleSyncExecuteMessage message,
                                             boolean autoOpenSession);

    WebSQLExecuteInfo syncExecuteBatchQuery(BatchSyncExecuteMessage message);

    WebAsyncTaskInfo asyncExecuteSingleQuery(SingleAsyncExecuteMessage message);

    WebAsyncTaskInfo asyncExecuteBatchQuery(BatchAsyncExecuteMessage message);

    WebAsyncTaskInfo asyncExecuteUpdate(SqlUpdateMessage message);

    WebAsyncTaskInfo asyncJobExport(JobExportMessage message);

    @Deprecated
    WebAsyncTaskInfo asyncSqlExport(SqlExportMessage message);

    WebAsyncTaskInfo asyncPreviewBlob(PreviewBlobMessage message);

    WebAsyncTaskInfo asyncCheckRecycle(CheckRecycleMessage message);

    void killConnection(WebSQLContextInfo contextInfo);

    void interruptConnection(WebSQLContextInfo contextInfo);

    String sqlGenerate(SqlGenerateMessage message);

    WebSQLScriptInfo parseScript(ParseScriptMessage message);

}
