package com.dc.summer.model.utils;

import com.alibaba.nacos.common.utils.ConcurrentHashSet;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.github.pjfanning.xlsx.StreamingReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.CaseInsensitiveMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import java.io.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class ExcelUtils {


    /**
     * 合并多个Excel文件为一个文件，每个文件作为一个sheet页
     *
     * @param exportFileName 最终生成的文件路径
     * @param filePaths      Map<源文件路径, sheet页名称>
     * @return 合并后的文件
     * @throws IOException 文件操作异常
     */
    public static File combineExcel(String exportFileName, Map<File, String> filePaths, WebSQLContextInfo.TaskProgressMonitor monitor) throws IOException {

        SXSSFWorkbook workbook = null;
        FileOutputStream outputStream = null;
        try {
            File outputFile = new File(exportFileName);
            File parentDir = outputFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            workbook = new SXSSFWorkbook(1000);

            outputStream = new FileOutputStream(outputFile);

            // 记录已处理的sheet名称，避免重复
            CaseInsensitiveMap<String,String> processedSheetNames = new CaseInsensitiveMap<>();
            AtomicInteger sheetIndex = new AtomicInteger(0);
            for (Map.Entry<File, String> entry : filePaths.entrySet()) {
                // 检查中断
                if (monitor.isCanceled()) {
                    throw new InterruptedException("【批量合并】任务被中断！");
                }
                if (sheetIndex.intValue() >= 255) { // 最多合并255个sheet
                    break;
                }
                String sheetName = entry.getValue();
                File sourceFile = entry.getKey();
                String filePath = sourceFile.getAbsolutePath();
                if (!sourceFile.exists() || !sourceFile.getName().endsWith(".xlsx")) {
                    log.debug("【批量合并】源文件不存在，跳过: {}", filePath);
                    continue;
                }
                try {
                    // 使用流式读取源文件
                    copySheetFromFile(workbook, sourceFile, sheetName, processedSheetNames,sheetIndex);

                } catch (Exception e) {
                    log.error("【批量合并】合并文件失败: {}, sheet: {}, 错误: {}", filePath, sheetName, e.getMessage());
                    // 继续处理其他文件，不中断整个合并过程
                }
            }
            // 写入合并后的文件
            workbook.write(outputStream);
            log.debug("Excel文件合并完成: {}, 共处理 {} 个文件", exportFileName, filePaths.size());
            return outputFile;

        } catch (Exception e) {
            log.error("【批量合并】合并Excel文件失败!", e);
            throw new IOException(e);
        } finally {
            try {
                if (outputStream != null) outputStream.close();
            } catch (IOException ex) {
                log.error("【批量合并】outputStream close failed！", ex);
            }
            // 必须释放临时文件
            if (workbook != null) {
                workbook.dispose(); // 清理临时文件
                try {
                    workbook.close();
                } catch (IOException ex) {
                    log.error("【批量合并】workbook close failed！", ex);
                }
            }
        }
    }

    /**
     * 从源文件中复制sheet到目标workbook
     * 使用流式处理减少内存占用
     */
    private static void copySheetFromFile(SXSSFWorkbook targetWorkbook, File sourceFile, String
            sheetName, CaseInsensitiveMap<String,String> processedSheetNames, AtomicInteger sheetIndex) throws IOException {
        // 创建样式池
        Map<String, CellStyle> stylePool = new HashMap<>();
        try (FileInputStream fis = new FileInputStream(sourceFile);
             Workbook sourceWorkbook = StreamingReader.builder()
                     .rowCacheSize(1000)
                     .open(fis)) {

            int numberOfSheets = sourceWorkbook.getNumberOfSheets();
            if (numberOfSheets == 0) {
                log.debug("【批量合并】源文件没有sheet页，跳过: {}", sourceFile.getName());
                return;
            }
            sourceWorkbook.sheetIterator().forEachRemaining(sourceSheet -> {
                String uniqueSheetName = getUniqueSheetName(sheetName, processedSheetNames);
                Sheet targetSheet = targetWorkbook.createSheet(uniqueSheetName);
                copyRowsStreaming(sourceSheet, targetSheet, stylePool);
                // 记录处理了多少个sheet页
                sheetIndex.incrementAndGet();
            });
            // 清空样式池
            stylePool.clear();
        } catch (Exception e) {
            log.error("【批量合并】复制sheet失败: {}, 错误: {}", sourceFile.getName(), e.getMessage());
            throw new IOException("【批量合并】复制sheet失败!", e);
        }
    }


    /**
     * 流式复制行数据，减少内存占用
     */
    private static void copyRowsStreaming(Sheet sourceSheet, Sheet targetSheet, Map<String, CellStyle> stylePool) {
        int rowCount = 0;
        Iterator<Row> rowIterator = sourceSheet.rowIterator();
        while (rowIterator.hasNext()) {
            Row sourceRow = rowIterator.next();
            if (sourceRow == null) {
                continue;
            }
            Row targetRow = targetSheet.createRow(rowCount++);
            copyRowData(sourceRow, targetRow, stylePool);
        }
    }

    /**
     * 复制行数据
     */
    private static void copyRowData(Row sourceRow, Row targetRow, Map<String, CellStyle> stylePool) {
        // 复制单元格数据
        sourceRow.cellIterator().forEachRemaining(sourceCell -> {
            int columnIndex = sourceCell.getColumnIndex();
            Cell targetCell = targetRow.createCell(columnIndex);
            copyCellData(sourceCell, targetCell, stylePool);
        });
    }

    /**
     * 复制单元格数据
     */
    private static void copyCellData(Cell sourceCell, Cell targetCell, Map<String, CellStyle> stylePool) {

        // 特别处理数字、日期/时间格式
        if (sourceCell.getCellType() == CellType.NUMERIC) {
            // 获取格式化类型
            String originalFormatString = sourceCell.getCellStyle().getDataFormatString();
            if (StringUtils.isNotBlank(originalFormatString)){
                targetCell.setCellStyle(getOrCreateStyle(sourceCell, targetCell.getSheet().getWorkbook(), stylePool));
            }
        }
        // 根据单元格类型复制数据
        switch (sourceCell.getCellType()) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(sourceCell)) {
                    targetCell.setCellValue(sourceCell.getDateCellValue());
                } else {
                    targetCell.setCellValue(sourceCell.getNumericCellValue());
                }
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellValue(sourceCell.getCellFormula());
                break;
            case BLANK:
                targetCell.setCellValue("");
                break;
            default:
                targetCell.setCellValue("");
                break;
        }
    }
    // 样式池，避免频繁创建样式，提高导出效率
    private static CellStyle getOrCreateStyle(Cell sourceCell, Workbook workbook, Map<String, CellStyle> stylePool) {
        String dataFormatString = sourceCell.getCellStyle().getDataFormatString();
        short dataFormat = sourceCell.getCellStyle().getDataFormat();
        String key = dataFormat +"_"+ dataFormatString;
        if (stylePool.containsKey(key)){
            return stylePool.get(key);
        }else{
            CellStyle newStyle = workbook.createCellStyle();
            DataFormat format = workbook.createDataFormat();
            short formatIndex = format.getFormat(dataFormatString);
            newStyle.setDataFormat(formatIndex);
            stylePool.put(key, newStyle);
            return newStyle;
        }
    }

    /**
     * 获取唯一的sheet名称，避免重复
     */
    private static String getUniqueSheetName(String originalName, CaseInsensitiveMap<String,String> existingNames) {
        if (StringUtils.isBlank(originalName)) {
            originalName = "Sheet";
        }
        // 清理sheet名称中的非法字符
        String cleanName = originalName.replaceAll("[\\[\\]\\*\\?/\\\\]", "_");

        // 限制sheet名称长度（Excel限制31个字符）
        if (cleanName.length() > 27) {
            cleanName = cleanName.substring(0, 27);
        }

        String finalName = cleanName;
        int counter = 1;

        while (existingNames.containsKey(finalName)) {
            String suffix = "_" + counter;
            // 确保总长度不超过31个字符
            if (cleanName.length() + suffix.length() > 31) {
                cleanName = cleanName.substring(0, 31 - suffix.length());
            }
            finalName = cleanName + suffix;
            counter++;
        }
        existingNames.put(finalName,"");
        return finalName;
    }
}
