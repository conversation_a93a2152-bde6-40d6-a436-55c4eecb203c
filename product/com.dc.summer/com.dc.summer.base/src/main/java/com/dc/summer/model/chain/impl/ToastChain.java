package com.dc.summer.model.chain.impl;

import com.dc.springboot.core.model.chain.AbstractChain;
import com.dc.springboot.core.model.execution.ExportExecuteModel;
import com.dc.springboot.core.model.result.WebSQLTransferResult;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class ToastChain extends AbstractChain<WebSQLTransferResult> {

    private final List<WebSQLQueryResultSet> resultSets;

    private final List<ExportExecuteModel> exportExecuteModels;

    public ToastChain(List<WebSQLQueryResultSet> resultSets, List<ExportExecuteModel> exportExecuteModels) {
        this.resultSets = resultSets;
        this.exportExecuteModels = exportExecuteModels;
    }

    @Override
    public boolean proceed(WebSQLTransferResult transferResult) {

        long realExportRowNum = 0;

        List<String> authToast = new ArrayList<>();

        // 处理 resultSets 的导出限制（保持原有逻辑）
        for (int i = 0; i < resultSets.size(); i++) {
            WebSQLQueryResultSet resultSet = resultSets.get(i);
            String maxNumToast = resultSets.size() > 1 ? String.format("[执行结果(%s)]", i + 1) : "";
            //判断是否本地导出的行数已被限制
//            if (resultSet.isExportLimit() || (resultSet.getRowsLimit() != 0 && resultSet.getExportRowsCount() >= resultSet.getRowsLimit())) {
            if (resultSet.isExportLimit() && resultSet.getExportRowsLimit() != 0) {
                authToast.add(maxNumToast);
                realExportRowNum = resultSet.getExportRowsLimit();
            }
        }

        // 处理 exportExecuteModels 的导出限制
        if (resultSets.isEmpty() && CollectionUtils.isNotEmpty(exportExecuteModels)) {
            int resultSetSize = resultSets.size();
            int totalSize = resultSetSize + exportExecuteModels.size();

            for (int i = 0; i < exportExecuteModels.size(); i++) {
                ExportExecuteModel executeModel = exportExecuteModels.get(i);
                // 编号从 resultSets 的数量开始继续编号
                int currentIndex = resultSetSize + i + 1;
                String maxNumToast = totalSize > 1 ? String.format("[执行结果(%s)]", currentIndex) : "";

                // 检查导出限制
                if (executeModel.isHasExportLimit() && executeModel.getExportLimit() > 0) {
                    authToast.add(maxNumToast);
                    realExportRowNum = executeModel.getExportLimit();
                }
            }
        }

        String toast = "";
        //是否需要超过限制数量提示
        if (!authToast.isEmpty()) {
            toast = String.join("、", authToast);
            toast = toast + String.format("未完全导出，您的权限最多只可导出%s条数据", realExportRowNum);
        }
        transferResult.setToast(toast);

        return false;
    }
}
