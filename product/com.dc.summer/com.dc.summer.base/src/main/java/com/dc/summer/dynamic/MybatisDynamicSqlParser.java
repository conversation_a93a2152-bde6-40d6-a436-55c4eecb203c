package com.dc.summer.dynamic;

import com.dc.repository.mysql.utils.MyBatisUtils;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.utils.StringUtils;
import lombok.Getter;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.ognl.ExpressionSyntaxException;
import org.apache.ibatis.session.Configuration;

import java.util.Map;

public class MybatisDynamicSqlParser {

    /**
     * 支持解析的的标签
     * mybatis 有很多标签，但是 DC 目前只用到以下标签
     */
    private static final String SUPPORTS_TAGS = "if|where";

    @Getter
    private final Configuration configuration;
    private final CustomXMLLanguageDriver languageDriver; // 使用自定义的Driver

    public MybatisDynamicSqlParser() {
        this.configuration = new Configuration();
        this.languageDriver = new CustomXMLLanguageDriver();
    }

    /**
     * 解析动态SQL，并应用安全策略
     *
     * @param script 动态SQL模板
     * @param params 参数
     * @param allowDollar 是否允许 ${...} 语法
     * @param isSave 是否保存
     * @return BoundSql
     */
    public BoundSql parse(String script, Map<String, Object> params, boolean allowDollar, boolean isSave) {
        // 在解析前，设置我们自定义Driver的策略
        languageDriver.setAllowDollarParam(allowDollar);

        String regex = "</?(?=" + SUPPORTS_TAGS + ")[^>]+>";  // 正则匹配非 <if> 和 <where> 标签及其内容

        script = removeSingleLineComments(script);
        script = removeMultiLineComments(script);

        if (isSave) {
            script = removeIfTags(script);
        }

        script = StringUtils.reverseReplace(script, regex, MyBatisUtils::escapeSpecialCharacters);

        // 将用户传入的SQL包裹在 <script> 标签内，以强制启动 Mybatis 的 XML 动态标签解析引擎。
        String scriptWithTags = "<script>" + script + "</script>";

        // Mybatis内部会调用重写的 createSqlSource 方法
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, scriptWithTags, Map.class);

        try {
            return sqlSource.getBoundSql(params);
        } catch (Exception e) {
            if (e.getCause() instanceof ExpressionSyntaxException) {
                throw new ServiceException("参数表达式格式错误");
            }
            throw e;
        }
    }

    /**
     * 移除SQL中的单行注释
     * @param sql 输入的SQL字符串。
     * @return 移除了单行注释的SQL字符串。
     */
    public static String removeSingleLineComments(String sql) {
        if (sql == null) return null;
        // 正则表达式匹配 -- 开头的注释
        return sql.replaceAll("--.*", "");
    }

    /**
     * 移除SQL中的多行注释
     * @param sql 输入的SQL字符串。
     * @return 移除了多行注释的SQL字符串。
     */
    public static String removeMultiLineComments(String sql) {
        if (sql == null) return null;
        // 正则表达式匹配 /* ... */
        // 使用非贪婪模式 '.*?' 来确保只匹配到最近的 '*/'
        // 使用 Pattern.DOTALL 标志 (通过内联标记 (?s)) 让 '.' 可以匹配换行符
        return sql.replaceAll("(?s)/\\*.*?\\*/", "");
    }

    /**
     * 移除 Mybatis-style 的 <if> 和 </if> 标签。
     * @param sql 输入的SQL字符串。
     * @return 移除了 <if> 标签的SQL字符串。
     */
    public static String removeIfTags(String sql) {
        if (sql == null) return null;
        // 正则表达式匹配 <if ...> 和 </if>
        String ifTagRegex = "<if[^>]*>|</if>";
        return sql.replaceAll(ifTagRegex, "");
    }
}
