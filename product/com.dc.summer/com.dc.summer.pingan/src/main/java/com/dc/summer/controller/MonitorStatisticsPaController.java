package com.dc.summer.controller;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.dc.springboot.auth.AuthIgnored;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.component.Resource;
import com.dc.springboot.core.model.data.Result;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.model.ResponseExecuteSuccessModel;
import com.dc.summer.model.ResponsePingAnModel;
import com.dc.summer.service.impl.PaMonitorPercentServiceImpl;
import com.dc.utils.http.HttpClientUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Api(tags = "平安监控告警系统请求数据")
@Slf4j
@RequestMapping("/monitor-statistics")
@RestController
@AuthIgnored
public class MonitorStatisticsPaController {

    @Autowired
    PaMonitorPercentServiceImpl service;

    /**
     * 连接成功率
     *
     * @return
     * @throws Exception
     */
    @ApiOperation("连接成功率")
    @GetMapping("/connection-success")
    public Result<Map<String, Object>> connectionSuccess(@RequestParam(value = "time_start", required = false) Long timeStart,
                                                         @RequestParam(value = "time_end", required = false) Long timeEnd,
                                                         @RequestParam(value = "instance_uuid", required = false) String instanceUuid) throws Exception {
        if (timeStart == null) {
            timeStart = System.currentTimeMillis() - 3600 * 1000 * 24;
        }
        if (timeEnd == null) {
            timeEnd = System.currentTimeMillis();
        }
        if (StringUtils.isBlank(instanceUuid)) {
            instanceUuid = null;
        }
        try {
            Map<String, Object> result = service.getPercent(timeStart, timeEnd, instanceUuid);
            return Result.success(result);
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.fail(e.getMessage());
        }
    }


    /**
     * 执行成功率
     *
     * @return
     * @throws Exception
     */
    @ApiOperation("执行成功率")
    @GetMapping("/execute-success")
    public Result<ResponsePingAnModel> executeSuccess(
            @RequestParam(value = "time_start", required = false) Long timeStart,
            @RequestParam(value = "time_end", required = false) Long timeEnd,
            @RequestParam(value = "instance_uuid", required = false) String instanceUuid) throws Exception {

        if (timeStart == null) {
            timeStart = System.currentTimeMillis() - 3600 * 1000 * 24;
        }

        if (timeEnd == null) {
            timeEnd = System.currentTimeMillis();
        }

        try {
            SummerConfig bean = Resource.getBean(SummerConfig.class);
            String url = bean.getPath().getDcBackend() + "/api/dams/sql/audits/search";
            HashMap<String, String> params = new HashMap<>();
            params.put("time_start", String.valueOf(timeStart));
            params.put("time_end", String.valueOf(timeEnd));
            if (StringUtils.isNotBlank(instanceUuid)) {
                params.put("instance_uuid", instanceUuid);
            }
            String response = HttpClientUtils.doPost(url, new StringEntity(JSON.toJSONString(params), "UTF-8"));
            if (StringUtils.isBlank(response)) {
                return Result.fail("请求失败..." + url);
            }
            ResponseExecuteSuccessModel strategyMap = JSON.parseObject(response, ResponseExecuteSuccessModel.class);

            ResponsePingAnModel data = strategyMap.getData();
            if (data.getExecute() > 0) {
                BigDecimal original = new BigDecimal((data.getSuccess() + data.getIntercept()) * 1.0 / data.getExecute() * 100);
                data.setSuccess_percent(original.setScale(3, BigDecimal.ROUND_DOWN).doubleValue());
            }
            if (StringUtils.isNotBlank(instanceUuid)) {
                data.setInstance_uuid(instanceUuid);
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            data.setTime_start(sdf.format(new Date(timeStart)));
            data.setTime_end(sdf.format(new Date(timeEnd)));

            /**
             *                 "intercept": 88,
             *                 "success": 8681,
             *                 "error": 654,
             *                 "execute": 9423
             */
            return Result.success(data);
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.fail(e.getMessage());
        }
    }

    /**
     * hello
     *
     * @return
     * @throws Exception
     */
    @ApiOperation("hello")
    @GetMapping("/hello")
    @AuthIgnored
    public Result<ResponsePingAnModel> hello() throws Exception {
        return Result.success();
    }
}
