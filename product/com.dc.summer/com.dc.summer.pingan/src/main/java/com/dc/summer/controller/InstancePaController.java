package com.dc.summer.controller;

import com.dc.springboot.auth.AuthIgnored;
import com.dc.springboot.core.model.data.Result;
import com.dc.summer.model.EntityMessage;
import com.dc.summer.service.DbConnectionService;
import com.dc.summer.service.PaInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;

@Api(tags = "单点登录模块")
@Slf4j
@RequestMapping("/pa/instance")
@RestController
@AuthIgnored
public class InstancePaController {
    // TODO
//    @Resource
//    private SsoAuthService ssoAuthService;

    @Resource
    private DbConnectionService dbConnectionService;

    @Resource
    private PaInstanceService paInstanceService;

    /**
     * pa实例同步
     */
    @ApiOperation("平安实例同步")
    @GetMapping("/sync")
    public SseEmitter syncInstance() throws Exception {
        SseEmitter emitter = null;
        try {
            long DEFAULT_TIMEOUT = 0L;
            emitter = new SseEmitter(DEFAULT_TIMEOUT);
            emitter = paInstanceService.syncInstance(emitter);      //优化版本
            //emitter = dbConnectionService.syncInstance(emitter);   //洪钊版本, mongodb bug1：重复数据.bug2:无端口非法资产创建实例
        } catch (Exception e) {
            emitter.send("error:" + e.getMessage());
            emitter.complete();
        }
        return emitter;

    }

    /**
     * pa 平安业务同步
     */
    @ApiOperation("平安业务同步")
    @GetMapping("/sync-buss")
    public Result<Object> syncBuss() throws Exception {
        dbConnectionService.syncBuss(null);
        return Result.success(null, "同步成功");
    }

    /**
     * pa 平安实体同步
     */
    @ApiOperation("平安实体同步")
    @GetMapping("/sync-entity-info")
    public SseEmitter syncEntity() throws Exception {
        SseEmitter emitter = null;
        try {
            long DEFAULT_TIMEOUT = 0L;
            emitter = new SseEmitter(DEFAULT_TIMEOUT);
            emitter = dbConnectionService.syncEntity(emitter);
        } catch (Exception e) {
            emitter.send("error:" + e.getMessage());
            emitter.complete();
        }
        return emitter;

    }

    /**
     * pa 同步单个实体
     */
    @ApiOperation("同步单个实体")
    @PostMapping("/sync-entity")
    public Result syncByEntity(@RequestBody EntityMessage entityMessage) throws Exception {
        paInstanceService.syncInstanceByEntity(entityMessage); //优化版本
        //dbConnectionService.syncByEntityName(entityMessage); //洪钊版本
        return Result.success(null, "同步成功");

    }

    @ApiOperation("平安历史实体")
    @GetMapping("/sync/entity-history")
    public Result syncEntityHistory() throws Exception {
        dbConnectionService.syncEntityHistory();
        return Result.success(null, "同步成功");
    }

    @ApiOperation("平安实例版本")
    @GetMapping("/sync/entity-version")
    public Result syncEntityVersion() throws Exception {
        dbConnectionService.syncEntityVersion();
        return Result.success(null, "同步成功");
    }

}
