package com.dc.job.log;

import com.dc.job.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.helpers.FormattingTuple;
import org.slf4j.helpers.MessageFormatter;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by xuxuel<PERSON> on 17/4/28.
 */
public class XxlJobLogger {
    private static Logger logger = LoggerFactory.getLogger("xxl-job logger");

    /**
     *  平安日志脱敏需要使用的日志过滤
     * @param value 日志文本信息
     * @return
     */
    private static String logDetailDesense(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        try {
            Class<?> ruleSetDesensTemplateBuilderClazz = XxlJobLogger.class.getClassLoader().loadClass("com.paic.isic.sdmp.sdk.extension.template.RuleSetDesensTemplateBuilder");
            Object ruleSetDesensTemplateBuilderClazzObj = ruleSetDesensTemplateBuilderClazz.newInstance();

            Method ruleSetDesensTemplateBuilderClazzLoad = ruleSetDesensTemplateBuilderClazz.getDeclaredMethod("load", String.class);
            Object desensTemplateBuilderObj = ruleSetDesensTemplateBuilderClazzLoad.invoke(ruleSetDesensTemplateBuilderClazzObj, "sensitive-sdk-system-strategy");

            Class<?> desensTemplateBuilderClazz = XxlJobLogger.class.getClassLoader().loadClass("com.paic.isic.sdmp.sdk.extension.template.DesensTemplateBuilder");
//                Object desensTemplateBuilderObj = desensTemplateBuilderClazz.newInstance();


            Method desensTemplateBuilderRemove = desensTemplateBuilderClazz.getDeclaredMethod("remove", String.class);
            desensTemplateBuilderRemove.invoke(desensTemplateBuilderObj, "SDA0021");

            Method desensTemplateBuilderBuild = desensTemplateBuilderClazz.getDeclaredMethod("build");
            Object desenTemplateObj = desensTemplateBuilderBuild.invoke(desensTemplateBuilderObj);

            Class<?> MsgDesensor = XxlJobLogger.class.getClassLoader().loadClass("com.paic.isic.sensitive.sdk.logdesens.internal.MsgDesensor");

            Class<?> desensTemplateClazz = XxlJobLogger.class.getClassLoader().loadClass("com.paic.isic.sdmp.sdk.extension.template.DesensTemplate");
            Method desensWithPlaceHolderParam = MsgDesensor.getDeclaredMethod("desensWithPlaceHolderParam", String.class, Object[].class, Set.class, desensTemplateClazz);

            value = (String) desensWithPlaceHolderParam.invoke(null, value, null, new HashSet<>(), desenTemplateObj);

        } catch (Exception e) {
            if (!(e instanceof ClassNotFoundException)) {
                logger.error("调用直接脱敏失败，请检查脱敏方法。", e);
            }
        } catch (Throwable t) {
//            logger.error("调用直接脱敏失败，请检查扩展程序：" + t.getMessage());
        }
        return value;
    }

        /**
         * append log
         *
         * @param callInfo
         * @param appendLog
         */
    private static void logDetail(StackTraceElement callInfo, String appendLog) {


        /*// "yyyy-MM-dd HH:mm:ss [ClassName]-[MethodName]-[LineNumber]-[ThreadName] log";
        StackTraceElement[] stackTraceElements = new Throwable().getStackTrace();
        StackTraceElement callInfo = stackTraceElements[1];*/

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(DateUtil.formatDateTime(new Date())).append(" ")
//            .append("["+ callInfo.getClassName() + "#" + callInfo.getMethodName() +"]").append("-")
//            .append("["+ callInfo.getLineNumber() +"]").append("-")
//            .append("["+ Thread.currentThread().getName() +"]").append(" ")
            .append(appendLog!=null?appendLog:"");
        String formatAppendLog = stringBuffer.toString();

        // appendlog
        String logFileName = XxlJobFileAppender.contextHolder.get();
        if (logFileName!=null && logFileName.trim().length()>0) {
            XxlJobFileAppender.appendLog(logFileName, logDetailDesense(formatAppendLog));
        } else {
            logger.info(">>>>>>>>>>> {}", formatAppendLog);
        }
    }

    /**
     * append log with pattern
     *
     * @param appendLogPattern  like "aaa {} bbb {} ccc"
     * @param appendLogArguments    like "111, true"
     */
    public static void log(String appendLogPattern, Object ... appendLogArguments) {

    	FormattingTuple ft = MessageFormatter.arrayFormat(appendLogPattern, appendLogArguments);
        String appendLog = ft.getMessage();

        /*appendLog = appendLogPattern;
        if (appendLogArguments!=null && appendLogArguments.length>0) {
            appendLog = MessageFormat.format(appendLogPattern, appendLogArguments);
        }*/

        StackTraceElement callInfo = new Throwable().getStackTrace()[1];
        logDetail(callInfo, appendLog);
    }

    /**
     * append exception stack
     *
     * @param e
     */
    public static void log(Throwable e) {

        StringWriter stringWriter = new StringWriter();
        e.printStackTrace(new PrintWriter(stringWriter));
        String appendLog = stringWriter.toString();

        StackTraceElement callInfo = new Throwable().getStackTrace()[1];
        logDetail(callInfo, appendLog);
    }

}
