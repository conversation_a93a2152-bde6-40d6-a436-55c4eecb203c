package com.dc.broker.command.executor;

import com.dc.broker.command.model.CommandCursorCreateMessage;
import com.dc.broker.protocol.MessageBase;
import com.dc.broker.session.BrokerConnectionContext;
import com.dc.springboot.core.model.data.Result;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class CommandCursorCreateExecutor extends AbstractCommandExecutor {

    private final MessageBase.Message message;

    private final BrokerConnectionContext connectionContext;

    @Override
    public MessageBase.Message execute() {
        String content = message.getContent();
        CommandCursorCreateMessage createCursorMessage = gson.fromJson(content, CommandCursorCreateMessage.class);
        String id = connectionContext.getStatementProcessor().createSQLCursor(createCursorMessage.getMaxRows());
        return MessageBase.Message.getDefaultInstance()
                .toBuilder().setCmd(message.getCmd())
                .setRequestId(message.getRequestId()).setContent(gson.toJson(Result.success(id))).build();
    }

}
