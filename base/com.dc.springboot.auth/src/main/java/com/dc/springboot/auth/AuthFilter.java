package com.dc.springboot.auth;

import com.dc.utils.aksk.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.StreamUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.condition.PatternsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.servlet.*;
import javax.servlet.http.HttpFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
public class AuthFilter extends HttpFilter {

    private static final Map<String, Map.Entry<RequestMappingInfo, HandlerMethod>> URL_CACHE = new HashMap<>();

    @Override
    protected void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain) {

        try {
            Map.Entry<RequestMappingInfo, HandlerMethod> entry = getUrlCache().get(request.getRequestURI());
            if (entry != null) {
                AuthUtils.verify(request::getHeader, StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8));
            }
            chain.doFilter(request, response);
        } catch (Exception e) {
            log.warn("auth filter error: {}", e.getMessage());

            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json; charset=utf-8");
            try (PrintWriter out = response.getWriter()) {
                String responseJson = "{\"msg\":\"接口鉴权失败，" + e.getMessage() + "。\",\"body\": null, \"success\": false,\"code\": 403}";
                out.print(responseJson);
            } catch (IOException ioe) {
                throw new AuthException("系统错误：接口鉴权异常。");
            }
        }

    }

    public static Map<String, Map.Entry<RequestMappingInfo, HandlerMethod>> getUrlCache() {

        if (URL_CACHE.isEmpty()) {
            RequestMappingHandlerMapping mapping = WebApplicationContext.getInstance().getBean("requestMappingHandlerMapping", RequestMappingHandlerMapping.class);
            Map<RequestMappingInfo, HandlerMethod> handlerMethods = mapping.getHandlerMethods();
            for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet()) {

                String className = entry.getValue().getMethod().getDeclaringClass().getName();

                PatternsRequestCondition patternsCondition = entry.getKey().getPatternsCondition();

                if (StringUtils.isBlank(className) || patternsCondition == null) {
                    continue;
                }

                for (String url : patternsCondition.getPatterns()) {
                    String requestUrl = getRequestUrl(url);
                    if (className.contains("com.dc") && !isAuthIgnored(entry.getValue())) {
                        URL_CACHE.put(requestUrl, entry);
                    }
                }

            }
        }
        return URL_CACHE;
    }

    private static boolean isAuthIgnored(HandlerMethod handlerMethod) {

        boolean classAuthIgnored = handlerMethod.getMethod().getDeclaringClass().isAnnotationPresent(AuthIgnored.class) &&
                handlerMethod.getMethod().getDeclaringClass().getAnnotation(AuthIgnored.class) != null;

        boolean methodAuthIgnored = handlerMethod.hasMethodAnnotation(AuthIgnored.class) &&
                handlerMethod.getMethodAnnotation(AuthIgnored.class) != null;

        return classAuthIgnored || methodAuthIgnored;
    }

    private static String getRequestUrl(String url) {
        String separator = "/";
        String separator2 = "//";
        String separator3 = "///";
        String contextPath = WebApplicationContext.getInstance().getEnvironment().getProperty("server.servlet.context-path");
        String requestUrl;
        if (!ObjectUtils.isEmpty(contextPath) && !separator.equals(contextPath)) {
            requestUrl = contextPath + url;
            if (requestUrl.contains(separator3) || requestUrl.contains(separator2)) {
                requestUrl = requestUrl.replace(separator3, separator);
                requestUrl = requestUrl.replace(separator2, separator);
            }
        } else {
            requestUrl = url;
        }
        return requestUrl;
    }


}
