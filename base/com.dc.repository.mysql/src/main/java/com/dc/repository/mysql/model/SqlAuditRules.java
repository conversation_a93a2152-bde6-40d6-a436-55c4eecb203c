package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_sql_audit_rules")
public class SqlAuditRules {

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    @TableField("rule_set_id")
    private Integer ruleSetId;

    @TableField("name")
    private String name;

    @TableField("name_key")
    private String nameKey;

    @TableField("db_type")
    private int dbType;

    @TableField("level")
    private int level;

    @TableField("`desc`")
    private String desc;

    @TableField("params")
    private String params;

    @TableField("type")
    private int type;

    @TableField("status")
    private int status;

    @TableField("is_alert")
    private int isAlert;

    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

}
