package com.dc.repository.mysql.model;

import java.util.Date;

/**
 * xxl-dc-job
 *
 * <AUTHOR>  2021-12-17 14:57:49
 */
public class DCJob {
	
	private int id;		         // 主键ID dc任务id
	
	private int jobId;		     // job任务id
	private int logId;		     // log_id
	private String jobName;		// job名称
	private int status;		     // 0待执行，1执行中，2成功，3失败
	private int type;		     // 任务类型 1导出 2导入
	private String code;		 // 任务编号
	private String executorParams;		// 任务参数
	private Date endTime;		 // 结束时间
	private Date createTime;	 // 创建时间

	private String extraInfo;	//额外信息：调用接口返回信息

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getJobId() {
		return jobId;
	}

	public void setJobId(int jobId) {
		this.jobId = jobId;
	}

	public int getLogId() {
		return logId;
	}

	public void setLogId(int logId) {
		this.logId = logId;
	}

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getExecutorParams() {
		return executorParams;
	}

	public void setExecutorParams(String executorParams) {
		this.executorParams = executorParams;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getExtraInfo() {
		return extraInfo;
	}
	public void setExtraInfo (String extraInfo) {
		this.extraInfo = extraInfo;
	}

}
