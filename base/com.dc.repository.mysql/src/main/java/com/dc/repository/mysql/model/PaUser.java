package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("pa_user")
public class PaUser {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("um")
    private String um;

    @TableField("emp_id")
    private String empId;

    @TableField("bu_id")
    private String buId;

    @TableField("bu_id_short_desc")
    private String buIdShortDesc;

    @TableField("chr_out_date")
    private String chrOutDate;

    @TableField("emp_status")
    private String empStatus;


    @TableField("emp_status_desc")
    private String empStatusDesc;

    @TableField("paic_leave_date")
    private String paicLeaveDate;


    @TableField("gmt_create")
    private Date gmtCreate;

    @TableField("gmt_modified")
    private Date gmtModified;

    @TableField("is_delete")
    private Integer isDelete;

//    @TableField("page_no")
//    private Integer page_no;

}
