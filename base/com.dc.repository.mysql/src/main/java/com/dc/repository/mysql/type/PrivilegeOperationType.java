package com.dc.repository.mysql.type;

import lombok.Getter;

@Getter
public enum PrivilegeOperationType {

    GRANT(1,"GRANT"),
    ALTER_USER(2,"ALTER_USER"),
    REVOKE(3,"REVOKE"),
    CREATE_USER(4, "CREATE_USER"),
    ;

    private final Integer code;

    private final String desc;

    PrivilegeOperationType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PrivilegeOperationType fromCode(int code) {
        for (PrivilegeOperationType type : PrivilegeOperationType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

}
