package com.dc.repository.mysql.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dc.repository.mysql.column.OrderApplyContent;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_work_order")
public class Order {

    private Integer id;
    private String code;
    private Integer bpm_id;
    private String flow_service_id;
    private String apply_user;
    private String commit_user;
    private Date commit_time;
    private String apply_reason;
    private OrderApplyContent apply_content;
    private Integer audit_node;
    private String carbon_copy;
    private Integer current_status;
    private String third_order_uuid;
    private String third_order_sn;
    private String callback_uuid;
    private String sub_order;
    private Integer is_delete;
    private Date gmt_modified;
    private Date gmt_create;
    private Integer audit_status;
    private String eoa_code;
    private String check_fail_reason;
    private String execute_fail_reason;
    private String rollback_fail_reason;

    /**
     * 用于工单中断
     */
    private String consumer_token_list;
    private String producer_token;

    public void setApply_user(String apply_user) {
        this.apply_user = apply_user;
    }

}
