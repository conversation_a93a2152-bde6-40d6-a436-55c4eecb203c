package com.dc.repository.mysql.model;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dc.repository.mysql.column.ID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * SQL 解析之后，SQL 元数据信息
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderSqlParse extends ID {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "is_delete", fill = FieldFill.INSERT)
    private Integer isDelete;

    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Long gmtCreate;

    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Long gmtModified;

    private Integer order_id;

    private String script_name;

    private String sql_content;

    private String sql_type;

    //FIXME nullValue = 1
    private Integer verify_status = 1;

    private String verify_reason;

    //FIXME nullValue = 1
    private Integer execute_status = 1;

    private Integer is_skip = 0;

    private String log;

    //TODO 多线程处理用到执行顺序
    private Long line_number;

    private Long affected_rows;

    private String sql_operation;

    private String susceptible_column;

    private String file_path;

    /**
     * SQL 语句类型：
     * 1: 正常 SQL
     * 2: 回退 SQL
     */
    private Integer type;

    // 备份模型。
    private String backup_model;

    // 备份告警
    private String backup_warning;

    //权限纳管模型
    private String privilege_model;

    private String privilege_expire;

    private Integer order_schema_task_id;

}
