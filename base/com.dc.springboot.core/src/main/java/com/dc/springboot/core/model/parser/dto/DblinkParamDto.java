package com.dc.springboot.core.model.parser.dto;

import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import com.dc.summer.model.exec.DBCExecutionContext;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
public class DblinkParamDto {

    private String dblinkName;
    private String token;
    private DatabaseConnectionDto instance;
    private DBCExecutionContext executionContext;
    private DataSourceConnectionHandler handle;

    private Map<String, DblinkParamDto> catalogDblinkParamMap = new HashMap<>();

}
