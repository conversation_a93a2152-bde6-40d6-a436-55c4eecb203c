package com.dc.springboot.core.model.thread;


import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

public abstract class BaseThreadScheduler<T, R> {

    @Resource
    private Set<AbstractExecuteThread<T>> threads;

    private final Map<T, AbstractExecuteThread<T>> threadMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        threads.forEach(thread -> thread.getTypes().forEach(type -> threadMap.put(type, thread)));
    }

    public void exec(T executeType, Runnable runnable) {
        R result = getResult();
        threadMap.get(executeType).execute(() -> {
            setResult(result);
            try {
                runnable.run();
            } finally {
                delResult(result);
            }
        });
    }

    public <C> Future<C> submit(T executeType, Callable<C> callable) {
        R result = getResult();
        return threadMap.get(executeType).submit(() -> {
            setResult(result);
            try {
                return callable.call();
            } finally {
                delResult(result);
            }
        });
    }

    protected void delResult(R result) {
    }

    protected void setResult(R result) {
    }

    protected R getResult() {
        return null;
    }



}
