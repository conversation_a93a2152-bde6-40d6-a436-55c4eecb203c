
package com.dc.infra.exception.type.kernel.category;

import com.dc.infra.exception.sqlstate.SQLState;
import com.dc.infra.exception.type.kernel.KernelSQLException;

/**
 * Data SQL exception.
 */
public abstract class DataSQLException extends KernelSQLException {
    
    private static final long serialVersionUID = -3442297815489000345L;
    
    private static final int KERNEL_CODE = 1;
    
    protected DataSQLException(final SQLState sqlState, final int errorCode, final String reason, final Object... messageArgs) {
        super(sqlState, KERNEL_CODE, errorCode, reason, messageArgs);
    }
}
