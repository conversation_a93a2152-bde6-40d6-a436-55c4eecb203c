
package com.dc.infra.exception.type.kernel;

import com.dc.infra.exception.ShardingSphereSQLException;
import com.google.common.base.Preconditions;
import com.dc.infra.exception.sqlstate.SQLState;

/**
 * Kernel SQL exception.
 */
public abstract class Kernel<PERSON>QLException extends ShardingSphereSQLException {
    
    private static final long serialVersionUID = -6554922589499988153L;
    
    private static final int TYPE_OFFSET = 1;
    
    protected KernelSQLException(final SQLState sqlState, final int kernelCode, final int errorCode, final String reason, final Object... messageArgs) {
        super(sqlState, TYPE_OFFSET, getErrorCode(kernelCode, errorCode), reason, messageArgs);
    }
    
    protected KernelSQLException(final SQLState sqlState, final int kernelCode, final int errorCode, final String reason, final Exception cause) {
        super(sqlState.getValue(), TYPE_OFFSET, getErrorCode(kernelCode, errorCode), reason, cause);
    }
    
    private static int getErrorCode(final int kernelCode, final int errorCode) {
        Preconditions.checkArgument(kernelCode >= 0 && kernelCode < 10, "The value range of kernel code should be [0, 10).");
        Preconditions.checkArgument(errorCode >= 0 && errorCode < 1000, "The value range of error code should be [0, 1000).");
        return kernelCode * 1000 + errorCode;
    }
}
