package com.dc.mockdata.engine.generator;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.io.IOException;

public class BooleanRandomGenerator extends AbstractMockValueGenerator {

    public BooleanRandomGenerator() {
    }

    @Override
    public Object generateOneValue(DBRProgressMonitor monitor) throws DBException, IOException {
        return this.isGenerateNULL() ? null : random.nextBoolean();
    }
}
