package com.dc.mockdata.engine.generator;

import com.dc.summer.DBException;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataManipulator;

import java.util.Map;

public abstract class AbstractStringValueGenerator extends AbstractMockValueGenerator {

    private boolean lowercase;
    private boolean uppercase;

    public AbstractStringValueGenerator() {
    }

    @Override
    public void init(DBSDataManipulator container, DBSAttributeBase attribute, Map<String, Object> properties) throws DBException {
        super.init(container, attribute, properties);
        Boolean lowercase = (Boolean) properties.get("lowercase");
        if (lowercase != null) {
            this.lowercase = lowercase;
        }

        Boolean uppercase = (Boolean) properties.get("uppercase");
        if (uppercase != null) {
            this.uppercase = uppercase;
        }
    }

    protected String tune(String value) {
        if (value == null) {
            return null;
        } else if (this.uppercase) {
            return value.toUpperCase();
        } else {
            return this.lowercase ? value.toLowerCase() : value;
        }
    }
}
