package com.dc.mockdata.engine.generator.advanced.regex;

import dk.brics.automaton.Automaton;
import dk.brics.automaton.RegExp;
import dk.brics.automaton.State;
import dk.brics.automaton.Transition;

import java.util.List;
import java.util.Random;

public class Xeger {

    private final Automaton automaton;
    private final Random random;

    public Xeger(String regex, Random random) {
        assert regex != null;

        assert random != null;

        this.automaton = new RegExp(regex).toAutomaton();
        this.random = random;
    }

    public Xeger(String regex) {
        this(regex, new Random());
    }

    public String generate() {
        StringBuilder builder = new StringBuilder();
        this.generate(builder, this.automaton.getInitialState());
        return builder.toString();
    }

    private void generate(StringBuilder builder, State state) {
        List<Transition> transitions = state.getSortedTransitions(true);
        if (transitions.size() == 0) {
            assert state.isAccept();
        } else {
            int nroptions = state.isAccept() ? transitions.size() : transitions.size() - 1;
            int option = XegerUtils.getRandomInt(0, nroptions, this.random);
            if (!state.isAccept() || option != 0) {
                Transition transition = transitions.get(option - (state.isAccept() ? 1 : 0));
                this.appendChoice(builder, transition);
                this.generate(builder, transition.getDest());
            }
        }
    }

    private void appendChoice(StringBuilder builder, Transition transition) {
        char c = (char) XegerUtils.getRandomInt(transition.getMin(), transition.getMax(), this.random);
        builder.append(c);
    }
}
