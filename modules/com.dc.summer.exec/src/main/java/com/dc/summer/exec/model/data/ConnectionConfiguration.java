package com.dc.summer.exec.model.data;

import com.dc.annotation.NotBlank;
import com.dc.annotation.NotNull;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.type.AuthSourceType;
import com.dc.type.DatabaseType;
import com.dc.utils.CommonUtils;
import lombok.Data;
import lombok.ToString;

@ToString(callSuper = true)
@Data
public class ConnectionConfiguration extends DBPConnectionConfiguration {

    @NotBlank
    private String connectionId;

    private String connectionDesc;

    @NotNull
    private DatabaseType databaseType;

    @NotBlank
    private String driverId;

    private String schemaName;

    private String catalogName;

    private String dbName;

    @Override
    public boolean equals(Object obj) {
        if (getAuthSourceType() == AuthSourceType.SYSTEM) {
            return super.equals(obj);
        } else {
            if (!(obj instanceof ConnectionConfiguration)) {
                return false;
            }
            ConnectionConfiguration source = (ConnectionConfiguration) obj;
            return CommonUtils.equalOrEmptyStrings(this.getHostName(), source.getHostName()) &&
                    CommonUtils.equalOrEmptyStrings(this.getHostPort(), source.getHostPort());
        }
    }

}
