
package com.dc.summer.ext.mysql.model.session;

import com.dc.summer.model.admin.sessions.AbstractServerSession;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;

import java.sql.ResultSet;
import java.util.Objects;

/**
 * MySQL session
 */
public class MySQLSession extends AbstractServerSession {
    private final long pid;
    private String user;
    private String host;
    private final String db;
    private String command;
    private long time;
    private String state;
    private String info;

    public MySQLSession(ResultSet dbResult) {
        this.pid = JDBCUtils.safeGetLong(dbResult, "id");
        this.user = JDBCUtils.safeGetString(dbResult, "user");
        this.host = JDBCUtils.safeGetString(dbResult, "host");
        this.db = JDBCUtils.safeGetString(dbResult, "db");
        this.command = JDBCUtils.safeGetString(dbResult, "command");
        this.time = JDBCUtils.safeGetLong(dbResult, "time");
        this.state = JDBCUtils.safeGetString(dbResult, "state");
        this.info = JDBCUtils.safeGetString(dbResult, "info");
    }

    @Property(viewable = true, order = 1)
    public long getPid()
    {
        return pid;
    }

    @Property(viewable = true, order = 2)
    public String getUser()
    {
        return user;
    }

    @Property(viewable = true, order = 3)
    public String getHost()
    {
        return host;
    }

    @Property(viewable = true, order = 4)
    public String getDb()
    {
        return db;
    }

    @Property(viewable = true, order = 5)
    public String getCommand()
    {
        return command;
    }

    @Property(viewable = true, order = 6)
    public long getTime()
    {
        return time;
    }

    @Property(viewable = true, order = 7)
    public String getState()
    {
        return state;
    }

    @Override
    @Property(viewable = true, order = 8)
    public String getActiveQuery()
    {
        return info;
    }

    @Override
    public String toString()
    {
        return pid + "@" + db;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MySQLSession that = (MySQLSession) o;
        return pid == that.pid && Objects.equals(db, that.db);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pid, db);
    }
}
