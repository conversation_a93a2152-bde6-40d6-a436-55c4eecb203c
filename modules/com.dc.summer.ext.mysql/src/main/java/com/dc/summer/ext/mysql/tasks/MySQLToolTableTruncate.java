
package com.dc.summer.ext.mysql.tasks;

import com.dc.summer.ext.mysql.model.MySQLTableBase;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.code.NotNull;
import com.dc.summer.model.exec.DBCSession;

import java.util.List;

public class MySQLToolTableTruncate extends MySQLToolWithStatus<MySQLTableBase, MySQLToolTableTruncateSettings> {
    @NotNull
    @Override
    public MySQLToolTableTruncateSettings createToolSettings() {
        return new MySQLToolTableTruncateSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, MySQLToolTableTruncateSettings settings, List<DBEPersistAction> queries, MySQLTableBase object) throws DBCException {
        String sql = "";
        boolean force = settings.isForce();
        if (force) {
            queries.add(new SQLDatabasePersistAction("Disable foreign key checks", "SET FOREIGN_KEY_CHECKS = 0", DBEPersistAction.ActionType.INITIALIZER));
        }
        sql += "TRUNCATE TABLE " + object.getFullyQualifiedName(DBPEvaluationContext.DDL);
        queries.add(new SQLDatabasePersistAction(sql));
        if (force) {
            queries.add(new SQLDatabasePersistAction("Enable foreign key checks", "SET FOREIGN_KEY_CHECKS = 1", DBEPersistAction.ActionType.FINALIZER));
        }

    }

    @Override
    public boolean isNeedConfirmation() {
        return true;
    }

    public boolean needsRefreshOnFinish() {
        return true;
    }
}
