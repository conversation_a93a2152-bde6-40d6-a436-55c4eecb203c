<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="refresh_mat_view_concurrently" value="REFRESH MATERIALIZED VIEW CONCURRENTLY mvtest_boxmv;"
              db-types="PostgreSQL"/>
    <sql-case id="refresh_mat_view_concurrently_no_data"
              value="REFRESH MATERIALIZED VIEW CONCURRENTLY mvtest_tvmm WITH NO DATA;" db-types="PostgreSQL"/>
    <sql-case id="refresh_mat_view_point" value="REFRESH MATERIALIZED VIEW matview_schema.mv_nodata2;"
              db-types="PostgreSQL"/>
    <sql-case id="refresh_mat_view" value="REFRESH MATERIALIZED VIEW mvtest_mv;" db-types="PostgreSQL"/>
</sql-cases>
