<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="reindex_concurrently" value="REINDEX (CONCURRENTLY) TABLE concur_reindex_tab;" db-types="PostgreSQL"/>
    <sql-case id="reindex_verbose" value="REINDEX (VERBOSE) TABLE reindex_verbose;" db-types="PostgreSQL"/>
    <sql-case id="reindex_index_concurrently" value="REINDEX INDEX CONCURRENTLY  concur_reindex_tab3_c2_excl;"
              db-types="PostgreSQL"/>
    <sql-case id="reindex_index_concurrently_point" value="REINDEX INDEX CONCURRENTLY pg_toast.pg_toast_1260_index;"
              db-types="PostgreSQL"/>
    <sql-case id="reindex_index" value="REINDEX INDEX concur_reindex_part;" db-types="PostgreSQL"/>
    <sql-case id="reindex_schema" value="REINDEX SCHEMA CONCURRENTLY pg_catalog;" db-types="PostgreSQL"/>
    <sql-case id="reindex_system" value="REINDEX SYSTEM CONCURRENTLY postgres;" db-types="PostgreSQL"/>
    <sql-case id="reindex_table" value="REINDEX TABLE CONCURRENTLY concur_appclass_tab;" db-types="PostgreSQL"/>
</sql-cases>
