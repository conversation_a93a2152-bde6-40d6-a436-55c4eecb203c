<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="alter_subscription_set_publication"
              value="ALTER SUBSCRIPTION regress_testsub SET PUBLICATION testpub2, testpub3 WITH (refresh = false);"
              db-types="PostgreSQL"/>
    <sql-case id="alter_subscription_add_publication"
              value="ALTER SUBSCRIPTION regress_testsub ADD PUBLICATION testpub WITH (refresh = false);"
              db-types="PostgreSQL"/>
    <sql-case id="alter_subscription_drop_publication"
              value="ALTER SUBSCRIPTION regress_testsub DROP PUBLICATION testpub1, testpub1 WITH (refresh = false);"
              db-types="PostgreSQL"/>
    <sql-case id="alter_subscription_connection"
              value="ALTER SUBSCRIPTION regress_test CONNECTION 'host=************ port=5432 user=foo dbname=foodb';"
              db-types="PostgreSQL"/>
    <sql-case id="alter_subscription_disable" value="ALTER SUBSCRIPTION regress_testsub DISABLE;"
              db-types="PostgreSQL"/>
    <sql-case id="alter_subscription_enable" value="ALTER SUBSCRIPTION regress_testsub ENABLE;" db-types="PostgreSQL"/>
    <sql-case id="alter_subscription_transfer_ownership"
              value="ALTER SUBSCRIPTION regress_testsub OWNER TO regress_subscription_user2;" db-types="PostgreSQL"/>
    <sql-case id="alter_subscription_rename" value="ALTER SUBSCRIPTION regress_testsub RENAME TO regress_testsub_foo;"
              db-types="PostgreSQL"/>
    <sql-case id="alter_subscription_set" value="ALTER SUBSCRIPTION regress_testsub SET (binary = false);"
              db-types="PostgreSQL"/>
</sql-cases>
