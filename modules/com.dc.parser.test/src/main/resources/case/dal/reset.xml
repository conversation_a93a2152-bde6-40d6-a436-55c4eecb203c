<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <reset-parameter sql-case-id="reset_all" configuration-parameter="ALL"/>
    <reset-parameter sql-case-id="reset_timezone" configuration-parameter="timezone"/>
    <reset sql-case-id="reset_master">
        <option master="true" start-index="6" stop-index="11"/>
    </reset>
    <reset sql-case-id="reset_slave">
        <option master="false" start-index="6" stop-index="10"/>
    </reset>
    <reset sql-case-id="reset_master_slave">
        <option master="true" start-index="6" stop-index="11"/>
        <option master="false" start-index="14" stop-index="18"/>
    </reset>
    <reset sql-case-id="reset_master_with_binlog">
        <option master="true" binary-log-file-index-number="10" start-index="6" stop-index="17"/>
    </reset>
    <reset sql-case-id="reset_slave_with_all">
        <option master="false" all="true" start-index="6" stop-index="14"/>
    </reset>
    <reset sql-case-id="reset_slave_with_channel">
        <option master="false" channel="TEST_CHANNEL" start-index="6" stop-index="37"/>
    </reset>
    <reset sql-case-id="reset_slave_with_all_channel">
        <option master="false" all="true" channel="TEST_CHANNEL" start-index="6" stop-index="41"/>
    </reset>
    <reset sql-case-id="reset_query_cache"/>
    <reset sql-case-id="reset_replica"/>
    <reset-persist sql-case-id="reset_persist"/>
    <reset-persist sql-case-id="reset_persist_identifier" identifier="TEST_ID"/>
    <reset-persist sql-case-id="reset_persist_exist_identifier" if-exists="true" identifier="TEST_ID"/>
</sql-parser-test-cases>
