<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-role sql-case-id="create_role"/>
    <create-role sql-case-id="create_roles"/>
    <create-role sql-case-id="create_role_if_not_exists"/>
    <create-role sql-case-id="create_no_identified_role"/>
    <create-role sql-case-id="create_external_role"/>
    <create-role sql-case-id="create_global_role"/>
    <create-role sql-case-id="create_role_with_identified_by_password"/>
    <create-role sql-case-id="create_role_with_container"/>
    <create-role sql-case-id="create_role_with_role"/>
    <create-role sql-case-id="create_role_with_roles"/>
    <create-role sql-case-id="create_role_with_option"/>
    <create-role sql-case-id="create_role_with_options"/>
    <create-role sql-case-id="create_role_with_authorization"/>
    <create-role sql-case-id="create_role_identified_by"/>
    <create-role sql-case-id="create_role_with_password"/>
    <create-role sql-case-id="create_role_with_valid_until"/>
    <create-role sql-case-id="create_role_with_createdb"/>
</sql-parser-test-cases>
