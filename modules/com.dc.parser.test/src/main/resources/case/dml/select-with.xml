<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="select_with_subquery_factoring">
        <with start-index="0" stop-index="110">
            <common-table-expression name="dept_costs" start-index="5" stop-index="110">
                <subquery-expression start-index="20" stop-index="109">
                    <select>
                        <from>
                            <simple-table name="departments" alias="d" start-index="72" stop-index="84"/>
                        </from>
                        <projections start-index="27" stop-index="65">
                            <column-projection name="department_name" start-index="27" stop-index="41"/>
                            <aggregation-projection type="SUM" alias="dept_total" expression="SUM(salary)"
                                                    start-index="44" stop-index="54"/>
                        </projections>
                        <group-by>
                            <column-item name="department_name" start-index="95" stop-index="109"/>
                        </group-by>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <from>
            <simple-table name="dept_costs" start-index="126" stop-index="135"/>
        </from>
        <projections start-index="119" stop-index="119">
            <shorthand-projection start-index="119" stop-index="119"/>
        </projections>
        <where start-index="137" stop-index="161">
            <expr>
                <binary-operation-expression start-index="143" stop-index="161">
                    <left>
                        <column name="dept_total" start-index="143" stop-index="152"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="304500" start-index="156" stop-index="161"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="department_name" start-index="172" stop-index="186"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_subquery_factoring_with_binding_tables_without_join">
        <with start-index="0" stop-index="230">
            <common-table-expression name="dept_costs" start-index="5" stop-index="163">
                <subquery-expression start-index="20" stop-index="162">
                    <select>
                        <from>
                            <join-table join-type="COMMA">
                                <left>
                                    <simple-table name="employees" alias="e" start-index="72" stop-index="82"/>
                                </left>
                                <right>
                                    <simple-table name="departments" alias="d" start-index="85" stop-index="97"/>
                                </right>
                            </join-table>
                        </from>
                        <projections start-index="27" stop-index="65">
                            <column-projection name="department_name" start-index="27" stop-index="41"/>
                            <aggregation-projection type="SUM" alias="dept_total" expression="SUM(salary)"
                                                    start-index="44" stop-index="54"/>
                        </projections>
                        <where start-index="99" stop-index="137">
                            <expr>
                                <binary-operation-expression start-index="105" stop-index="137">
                                    <left>
                                        <column name="department_id" start-index="105" stop-index="119">
                                            <owner name="e" start-index="105" stop-index="105"/>
                                        </column>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <column name="department_id" start-index="123" stop-index="137">
                                            <owner name="d" start-index="123" stop-index="123"/>
                                        </column>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                        <group-by>
                            <column-item name="department_name" start-index="148" stop-index="162"/>
                        </group-by>
                    </select>
                </subquery-expression>
            </common-table-expression>
            <common-table-expression name="avg_cost" start-index="166" stop-index="230">
                <subquery-expression start-index="179" stop-index="229">
                    <select>
                        <from>
                            <simple-table name="dept_costs" start-index="220" stop-index="229"/>
                        </from>
                        <projections start-index="186" stop-index="213">
                            <expression-projection text="SUM(dept_total)/COUNT(*)" alias="avg" start-index="186"
                                                   stop-index="213"/>
                        </projections>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <from>
            <simple-table name="dept_costs" start-index="246" stop-index="255"/>
        </from>
        <projections start-index="239" stop-index="239">
            <shorthand-projection start-index="239" stop-index="239"/>
        </projections>
        <where start-index="257" stop-index="301">
            <expr>
                <binary-operation-expression start-index="263" stop-index="301">
                    <left>
                        <column name="dept_total" start-index="263" stop-index="272"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <subquery start-index="276" stop-index="301">
                            <select>
                                <from start-index="293" stop-index="300">
                                    <simple-table name="avg_cost" start-index="293" stop-index="300"/>
                                </from>
                                <projections start-index="284" stop-index="286">
                                    <column-projection name="avg" start-index="284" stop-index="286"/>
                                </projections>
                            </select>
                        </subquery>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="department_name" start-index="312" stop-index="326"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_subquery_factoring_with_search_depth_first">
        <with start-index="0" stop-index="224">
            <common-table-expression name="org_chart" start-index="5" stop-index="224">
                <subquery-expression start-index="72" stop-index="181">
                    <select>
                        <from>
                            <simple-table name="employees" start-index="148" stop-index="156"/>
                        </from>
                        <projections start-index="79" stop-index="141">
                            <column-projection name="employee_id" start-index="79" stop-index="89"/>
                            <column-projection name="last_name" start-index="92" stop-index="100"/>
                            <column-projection name="manager_id" start-index="103" stop-index="112"/>
                            <column-projection name="reportLevel" start-index="115" stop-index="125"/>
                            <column-projection name="salary" start-index="128" stop-index="133"/>
                            <column-projection name="job_id" start-index="136" stop-index="141"/>
                        </projections>
                        <where start-index="158" stop-index="181">
                            <expr>
                                <binary-operation-expression start-index="164" stop-index="181">
                                    <left>
                                        <column name="manager_id" start-index="164" stop-index="173"/>
                                    </left>
                                    <operator>IS</operator>
                                    <right>
                                        <literal-expression value="NULL" start-index="178" stop-index="181"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery-expression>
                <column name="order1" start-index="219" stop-index="224"/>
            </common-table-expression>
        </with>
        <from>
            <simple-table name="org_chart" start-index="276" stop-index="284"/>
        </from>
        <projections start-index="233" stop-index="269">
            <column-projection name="emp_name" start-index="233" stop-index="240"/>
            <column-projection name="eid" start-index="243" stop-index="245"/>
            <column-projection name="mgr_id" start-index="248" stop-index="253"/>
            <column-projection name="salary" start-index="256" stop-index="261"/>
            <column-projection name="job_id" start-index="264" stop-index="269"/>
        </projections>
        <order-by>
            <column-item name="order1" start-index="295" stop-index="300"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_subquery_factoring_with_search_depth_first_with_cycle">
        <with start-index="0" stop-index="282">
            <common-table-expression name="dup_hiredate" start-index="5" stop-index="282">
                <subquery-expression start-index="78" stop-index="190">
                    <select>
                        <from>
                            <simple-table name="employees" start-index="157" stop-index="165"/>
                        </from>
                        <projections start-index="85" stop-index="150">
                            <column-projection name="employee_id" start-index="85" stop-index="95"/>
                            <column-projection name="last_name" start-index="98" stop-index="106"/>
                            <column-projection name="manager_id" start-index="109" stop-index="118"/>
                            <column-projection name="reportLevel" start-index="121" stop-index="131"/>
                            <column-projection name="hire_date" start-index="134" stop-index="142"/>
                            <column-projection name="job_id" start-index="145" stop-index="150"/>
                        </projections>
                        <where start-index="167" stop-index="190">
                            <expr>
                                <binary-operation-expression start-index="173" stop-index="190">
                                    <left>
                                        <column name="manager_id" start-index="173" stop-index="182"/>
                                    </left>
                                    <operator>IS</operator>
                                    <right>
                                        <literal-expression value="NULL" start-index="187" stop-index="190"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery-expression>
                <column name="order1" start-index="229" stop-index="234"/>
            </common-table-expression>
        </with>
        <from>
            <simple-table name="dup_hiredate" start-index="381" stop-index="392"/>
        </from>
        <projections start-index="291" stop-index="374">
            <expression-projection text="lpad(' ',2*reportLevel)||emp_last" alias="emp_name" start-index="291"
                                   stop-index="332"/>
            <column-projection name="eid" start-index="335" stop-index="337"/>
            <column-projection name="mgr_id" start-index="340" stop-index="345"/>
            <column-projection name="hire_date" start-index="348" stop-index="356"/>
            <column-projection name="job_id" start-index="359" stop-index="364"/>
            <column-projection name="is_cycle" start-index="367" stop-index="374"/>
        </projections>
        <order-by>
            <column-item name="order1" start-index="403" stop-index="408"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_subquery_factoring_with_search_depth_first_with_having">
        <with start-index="0" stop-index="207">
            <common-table-expression name="emp_count" start-index="5" stop-index="207">
                <subquery-expression start-index="76" stop-index="164">
                    <select>
                        <from>
                            <simple-table name="employees" start-index="156" stop-index="164"/>
                        </from>
                        <projections start-index="83" stop-index="149">
                            <column-projection name="employee_id" start-index="83" stop-index="93"/>
                            <column-projection name="last_name" start-index="96" stop-index="104"/>
                            <column-projection name="manager_id" start-index="107" stop-index="116"/>
                            <column-projection name="mgrLevel" start-index="119" stop-index="126"/>
                            <column-projection name="salary" start-index="129" stop-index="134"/>
                            <column-projection name="cnt_employees" start-index="137" stop-index="149"/>
                        </projections>
                    </select>
                </subquery-expression>
                <column name="order1" start-index="202" stop-index="207"/>
            </common-table-expression>
        </with>
        <from>
            <simple-table name="emp_count" start-index="251" stop-index="259"/>
        </from>
        <projections start-index="216" stop-index="244">
            <column-projection name="emp_last" start-index="216" stop-index="223"/>
            <column-projection name="eid" start-index="226" stop-index="228"/>
            <column-projection name="mgr_id" start-index="231" stop-index="236"/>
            <column-projection name="salary" start-index="239" stop-index="244"/>
        </projections>
        <group-by>
            <column-item name="emp_last" start-index="270" stop-index="277"/>
            <column-item name="eid" start-index="280" stop-index="282"/>
            <column-item name="mgr_id" start-index="285" stop-index="290"/>
            <column-item name="salary" start-index="293" stop-index="298"/>
        </group-by>
        <having start-index="300" stop-index="320">
            <expr>
                <binary-operation-expression start-index="307" stop-index="320">
                    <left>
                        <column name="salary" start-index="307" stop-index="312"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression start-index="316" stop-index="320" value="24000"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </having>
        <order-by>
            <column-item name="mgr_id" start-index="331" stop-index="336"/>
            <column-item name="emp_last" start-index="351" stop-index="358"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_multiple_cte_definitions">
        <with start-index="0" stop-index="119">
            <common-table-expression name="cte1" start-index="5" stop-index="66">
                <subquery-expression start-index="31" stop-index="65">
                    <select>
                        <from>
                            <simple-table name="t_order" start-index="59" stop-index="65"/>
                        </from>
                        <projections start-index="38" stop-index="52">
                            <column-projection name="status" start-index="38" stop-index="43"/>
                            <column-projection name="user_id" start-index="46" stop-index="52"/>
                        </projections>
                    </select>
                </subquery-expression>
                <column name="status" start-index="10" stop-index="15"/>
                <column name="user_id" start-index="18" stop-index="24"/>
            </common-table-expression>
            <common-table-expression name="cte2" start-index="69" stop-index="119">
                <subquery-expression start-index="87" stop-index="118">
                    <select>
                        <from>
                            <simple-table name="t_order_item" start-index="107" stop-index="118"/>
                        </from>
                        <projections start-index="94" stop-index="100">
                            <column-projection name="item_id" start-index="94" stop-index="100"/>
                        </projections>
                    </select>
                </subquery-expression>
                <column name="item_id" start-index="74" stop-index="80"/>
            </common-table-expression>
        </with>
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="cte1" start-index="158" stop-index="161"/>
                </left>
                <right>
                    <simple-table name="cte2" start-index="174" stop-index="177"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="182" stop-index="208">
                        <left>
                            <column name="user_id" start-index="182" stop-index="193">
                                <owner name="cte1" start-index="182" stop-index="185"/>
                            </column>
                        </left>
                        <operator>=</operator>
                        <right>
                            <column name="user_id" start-index="197" stop-index="208">
                                <owner name="cte2" start-index="197" stop-index="200"/>
                            </column>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="128" stop-index="151">
            <column-projection name="status" start-index="128" stop-index="133"/>
            <column-projection name="user_id" start-index="136" stop-index="142"/>
            <column-projection name="item_id" start-index="145" stop-index="151"/>
        </projections>
    </select>

    <select sql-case-id="select_with_single_subquery">
        <with start-index="0" stop-index="33">
            <common-table-expression name="t" start-index="5" stop-index="33">
                <subquery-expression start-index="5" stop-index="33">
                    <select>
                        <from>
                            <simple-table name="t1" start-index="31" stop-index="32"/>
                        </from>
                        <projections start-index="18" stop-index="24">
                            <expression-projection text="a+2" alias="c" start-index="18" stop-index="22">
                                <expr>
                                    <binary-operation-expression start-index="18" stop-index="20">
                                        <left>
                                            <column name="a" start-index="18" stop-index="18"/>
                                        </left>
                                        <operator>+</operator>
                                        <right>
                                            <literal-expression value="2" start-index="20" stop-index="20"/>
                                        </right>
                                    </binary-operation-expression>
                                </expr>
                            </expression-projection>
                            <column-projection name="b" start-index="24" stop-index="24"/>
                        </projections>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <projections start-index="42" stop-index="44">
            <column-projection name="c" start-index="42" stop-index="42"/>
            <column-projection name="b" start-index="44" stop-index="44"/>
        </projections>
        <from>
            <simple-table name="t" start-index="51" stop-index="51"/>
        </from>
    </select>

    <select sql-case-id="select_with_oracle_single_subquery">
        <with start-index="0" stop-index="33">
            <common-table-expression name="t" start-index="5" stop-index="33">
                <subquery-expression start-index="11" stop-index="32">
                    <select>
                        <from>
                            <simple-table name="t1" start-index="31" stop-index="32"/>
                        </from>
                        <projections start-index="18" stop-index="24">
                            <expression-projection text="a+2" alias="c" start-index="18" stop-index="22">
                                <expr>
                                    <binary-operation-expression start-index="18" stop-index="20">
                                        <left>
                                            <column name="a" start-index="18" stop-index="18"/>
                                        </left>
                                        <operator>+</operator>
                                        <right>
                                            <literal-expression value="2" start-index="20" stop-index="20"/>
                                        </right>
                                    </binary-operation-expression>
                                </expr>
                            </expression-projection>
                            <column-projection name="b" start-index="24" stop-index="24"/>
                        </projections>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <projections start-index="42" stop-index="44">
            <column-projection name="c" start-index="42" stop-index="42"/>
            <column-projection name="b" start-index="44" stop-index="44"/>
        </projections>
        <from>
            <simple-table name="t" start-index="51" stop-index="51"/>
        </from>
    </select>

    <select sql-case-id="select_with_multiple_subquery">
        <with start-index="0" stop-index="198">
            <common-table-expression name="cte1" start-index="5" stop-index="100">
                <subquery-expression start-index="5" stop-index="100">
                    <select>
                        <from>
                            <simple-table name="employees" start-index="72" stop-index="80"/>
                        </from>
                        <projections start-index="39" stop-index="65">
                            <column-projection name="emp_no" start-index="39" stop-index="44"/>
                            <column-projection name="first_name" start-index="46" stop-index="55"/>
                            <column-projection name="last_name" start-index="57" stop-index="65"/>
                        </projections>
                        <where start-index="82" stop-index="99">
                            <expr>
                                <binary-operation-expression start-index="88" stop-index="99">
                                    <left>
                                        <column name="emp_no" start-index="88" stop-index="93"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="10012" start-index="95" stop-index="99"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery-expression>
                <column name="col1" start-index="10" stop-index="13"/>
                <column name="col2" start-index="16" stop-index="19"/>
                <column name="col3" start-index="22" stop-index="25"/>
            </common-table-expression>
            <common-table-expression name="cte2" start-index="103" stop-index="198">
                <subquery-expression start-index="103" stop-index="198">
                    <select>
                        <from>
                            <simple-table name="employees" start-index="170" stop-index="178"/>
                        </from>
                        <projections start-index="137" stop-index="163">
                            <column-projection name="emp_no" start-index="137" stop-index="142"/>
                            <column-projection name="first_name" start-index="144" stop-index="153"/>
                            <column-projection name="last_name" start-index="155" stop-index="163"/>
                        </projections>
                        <where start-index="180" stop-index="197">
                            <expr>
                                <binary-operation-expression start-index="186" stop-index="197">
                                    <left>
                                        <column name="emp_no" start-index="186" stop-index="191"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="10012" start-index="193" stop-index="197"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery-expression>
                <column name="col1" start-index="108" stop-index="111"/>
                <column name="col2" start-index="114" stop-index="117"/>
                <column name="col3" start-index="120" stop-index="123"/>
            </common-table-expression>
        </with>
        <projections start-index="207" stop-index="222">
            <column-projection name="col1" start-index="207" stop-index="210"/>
            <column-projection name="col2" start-index="213" stop-index="216"/>
            <column-projection name="col3" start-index="219" stop-index="222"/>
        </projections>
        <from>
            <simple-table name="cte1" start-index="229" stop-index="232"/>
        </from>
    </select>

    <select sql-case-id="select_with_recursive_union_all1">
        <with start-index="0" stop-index="217">
            <common-table-expression name="DirectoryCTE" start-index="15" stop-index="217">
                <subquery-expression start-index="15" stop-index="217">
                    <select>
                        <from>
                            <simple-table name="table1" start-index="46" stop-index="51"/>
                        </from>
                        <projections start-index="39" stop-index="39">
                            <shorthand-projection start-index="39" stop-index="39"/>
                        </projections>
                        <combine combine-type="UNION_ALL" start-index="85" stop-index="216">
                            <left>
                                <projections start-index="39" stop-index="39">
                                    <shorthand-projection start-index="39" stop-index="39"/>
                                </projections>
                                <from>
                                    <simple-table name="table1" start-index="46" stop-index="51"/>
                                </from>
                                <where start-index="53" stop-index="83">
                                    <expr>
                                        <binary-operation-expression start-index="59" stop-index="83">
                                            <left>
                                                <binary-operation-expression start-index="59" stop-index="64">
                                                    <left>
                                                        <column name="id" start-index="59" stop-index="60"/>
                                                    </left>
                                                    <operator>=</operator>
                                                    <right>
                                                        <literal-expression value="1" start-index="64" stop-index="64"/>
                                                    </right>
                                                </binary-operation-expression>
                                            </left>
                                            <operator>AND</operator>
                                            <right>
                                                <binary-operation-expression start-index="70" stop-index="83">
                                                    <left>
                                                        <column name="project_id" start-index="70" stop-index="79"/>
                                                    </left>
                                                    <operator>=</operator>
                                                    <right>
                                                        <literal-expression value="2" start-index="83" stop-index="83"/>
                                                    </right>
                                                </binary-operation-expression>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </left>
                            <right>
                                <from>
                                    <join-table join-type="INNER">
                                        <left>
                                            <simple-table name="project_file_catalog" alias="t" start-index="109"
                                                          stop-index="130"/>
                                        </left>
                                        <right>
                                            <simple-table name="DirectoryCTE" alias="cte" start-index="143"
                                                          stop-index="158"/>
                                        </right>
                                        <on-condition>
                                            <binary-operation-expression start-index="163" stop-index="216">
                                                <left>
                                                    <binary-operation-expression start-index="163" stop-index="191">
                                                        <left>
                                                            <column name="project_id" start-index="163"
                                                                    stop-index="174">
                                                                <owner name="t" start-index="163" stop-index="163"/>
                                                            </column>
                                                        </left>
                                                        <right>
                                                            <column name="project_id" start-index="178"
                                                                    stop-index="191">
                                                                <owner name="cte" start-index="178" stop-index="180"/>
                                                            </column>
                                                        </right>
                                                        <operator>=</operator>
                                                    </binary-operation-expression>
                                                </left>
                                                <operator>AND</operator>
                                                <right>
                                                    <binary-operation-expression start-index="197" stop-index="216">
                                                        <left>
                                                            <column name="parent_id" start-index="197" stop-index="207">
                                                                <owner name="t" start-index="197" stop-index="197"/>
                                                            </column>
                                                        </left>
                                                        <operator>=</operator>
                                                        <right>
                                                            <column name="id" start-index="211" stop-index="216">
                                                                <owner name="cte" start-index="211" stop-index="213"/>
                                                            </column>
                                                        </right>
                                                    </binary-operation-expression>
                                                </right>
                                            </binary-operation-expression>
                                        </on-condition>
                                    </join-table>
                                </from>
                                <projections start-index="102" stop-index="102">
                                    <shorthand-projection start-index="102" stop-index="102"/>
                                </projections>
                            </right>
                        </combine>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <projections start-index="226" stop-index="226">
            <shorthand-projection start-index="226" stop-index="226"/>
        </projections>
        <from>
            <simple-table name="DirectoryCTE" start-index="233" stop-index="244"/>
        </from>
        <order-by>
            <column-item name="level" order-direction="ASC" start-index="255" stop-index="259"/>
        </order-by>
    </select>
    <select sql-case-id="select_with_oracle_recursive_union_all1">
        <with start-index="0" stop-index="243">
            <common-table-expression name="DirectoryCTE" start-index="5" stop-index="243">
                <subquery-expression start-index="22" stop-index="242">
                    <select>
                        <from>
                            <simple-table name="table1" start-index="59" stop-index="64"/>
                        </from>
                        <projections start-index="29" stop-index="52">
                            <column-projection name="col1" start-index="29" stop-index="39">
                                <owner name="table1" start-index="29" stop-index="34"/>
                            </column-projection>
                            <column-projection name="col2" start-index="42" stop-index="52">
                                <owner name="table1" start-index="42" stop-index="47"/>
                            </column-projection>
                        </projections>
                        <combine combine-type="UNION_ALL" start-index="22" stop-index="242">
                            <left>
                                <projections start-index="29" stop-index="52">
                                    <column-projection name="col1" start-index="29" stop-index="39">
                                        <owner name="table1" start-index="29" stop-index="34"/>
                                    </column-projection>
                                    <column-projection name="col2" start-index="42" stop-index="52">
                                        <owner name="table1" start-index="42" stop-index="47"/>
                                    </column-projection>
                                </projections>
                                <from>
                                    <simple-table name="table1" start-index="59" stop-index="64"/>
                                </from>
                                <where start-index="66" stop-index="96">
                                    <expr>
                                        <binary-operation-expression start-index="72" stop-index="96">
                                            <left>
                                                <binary-operation-expression start-index="72" stop-index="77">
                                                    <left>
                                                        <column name="id" start-index="72" stop-index="73"/>
                                                    </left>
                                                    <operator>=</operator>
                                                    <right>
                                                        <literal-expression value="1" start-index="77" stop-index="77"/>
                                                    </right>
                                                </binary-operation-expression>
                                            </left>
                                            <operator>AND</operator>
                                            <right>
                                                <binary-operation-expression start-index="83" stop-index="96">
                                                    <left>
                                                        <column name="project_id" start-index="83" stop-index="92"/>
                                                    </left>
                                                    <operator>=</operator>
                                                    <right>
                                                        <literal-expression value="2" start-index="96" stop-index="96"/>
                                                    </right>
                                                </binary-operation-expression>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </left>
                            <right>
                                <from>
                                    <join-table join-type="INNER">
                                        <left>
                                            <simple-table name="project_file_catalog" alias="t" start-index="135"
                                                          stop-index="156"/>
                                        </left>
                                        <right>
                                            <simple-table name="DirectoryCTE" alias="cte" start-index="169"
                                                          stop-index="184"/>
                                        </right>
                                        <on-condition>
                                            <binary-operation-expression start-index="189" stop-index="242">
                                                <left>
                                                    <binary-operation-expression start-index="189" stop-index="217">
                                                        <left>
                                                            <column name="project_id" start-index="189"
                                                                    stop-index="200">
                                                                <owner name="t" start-index="189" stop-index="189"/>
                                                            </column>
                                                        </left>
                                                        <right>
                                                            <column name="project_id" start-index="204"
                                                                    stop-index="217">
                                                                <owner name="cte" start-index="204" stop-index="206"/>
                                                            </column>
                                                        </right>
                                                        <operator>=</operator>
                                                    </binary-operation-expression>
                                                </left>
                                                <operator>AND</operator>
                                                <right>
                                                    <binary-operation-expression start-index="223" stop-index="242">
                                                        <left>
                                                            <column name="parent_id" start-index="223" stop-index="233">
                                                                <owner name="t" start-index="223" stop-index="223"/>
                                                            </column>
                                                        </left>
                                                        <operator>=</operator>
                                                        <right>
                                                            <column name="id" start-index="237" stop-index="242">
                                                                <owner name="cte" start-index="237" stop-index="239"/>
                                                            </column>
                                                        </right>
                                                    </binary-operation-expression>
                                                </right>
                                            </binary-operation-expression>
                                        </on-condition>
                                    </join-table>
                                </from>
                                <projections start-index="115" stop-index="128">
                                    <column-projection name="col1" start-index="115" stop-index="120">
                                        <owner name="t" start-index="115" stop-index="115"/>
                                    </column-projection>
                                    <column-projection name="col2" start-index="123" stop-index="128">
                                        <owner name="t" start-index="123" stop-index="123"/>
                                    </column-projection>
                                </projections>
                            </right>
                        </combine>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <projections start-index="252" stop-index="287">
            <column-projection name="col1" start-index="252" stop-index="268">
                <owner name="DirectoryCTE" start-index="252" stop-index="263"/>
            </column-projection>
            <column-projection name="col2" start-index="271" stop-index="287">
                <owner name="DirectoryCTE" start-index="271" stop-index="282"/>
            </column-projection>
        </projections>
        <from>
            <simple-table name="DirectoryCTE" start-index="294" stop-index="305"/>
        </from>
        <order-by>
            <column-item name="level" order-direction="ASC" start-index="316" stop-index="320"/>
        </order-by>
    </select>
    <select sql-case-id="select_with_recursive_union_all2">
        <with start-index="0" stop-index="62">
            <common-table-expression name="cte" start-index="5" stop-index="62">
                <subquery-expression start-index="5" stop-index="62">
                    <select>
                        <projections start-index="20" stop-index="39">
                            <expression-projection text="1" alias="col1" start-index="20" stop-index="28">
                                <expr>
                                    <literal-expression value="1" start-index="20" stop-index="20"/>
                                </expr>
                            </expression-projection>
                            <expression-projection text="2" alias="col2" start-index="31" stop-index="39">
                                <literal-expression value="2" start-index="31" stop-index="31"/>
                            </expression-projection>
                        </projections>
                        <combine combine-type="UNION_ALL" start-index="41" stop-index="61">
                            <left>
                                <projections start-index="20" stop-index="39">
                                    <expression-projection text="1" alias="col1" start-index="20" stop-index="28">
                                        <expr>
                                            <literal-expression value="1" start-index="20" stop-index="20"/>
                                        </expr>
                                    </expression-projection>
                                    <expression-projection text="2" alias="col2" start-index="31" stop-index="39">
                                        <literal-expression value="2" start-index="31" stop-index="31"/>
                                    </expression-projection>
                                </projections>
                            </left>
                            <right>
                                <projections start-index="58" stop-index="61">
                                    <expression-projection text="3" start-index="58" stop-index="58">
                                        <expr>
                                            <literal-expression value="3" start-index="58" stop-index="58"/>
                                        </expr>
                                    </expression-projection>
                                    <expression-projection text="4" start-index="61" stop-index="61">
                                        <literal-expression value="4" start-index="61" stop-index="61"/>
                                    </expression-projection>
                                </projections>
                            </right>
                        </combine>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <projections start-index="71" stop-index="80">
            <column-projection name="col1" start-index="71" stop-index="74"/>
            <column-projection name="col2" start-index="77" stop-index="80"/>
        </projections>
        <from>
            <simple-table name="cte" start-index="87" stop-index="89"/>
        </from>
    </select>

</sql-parser-test-cases>
