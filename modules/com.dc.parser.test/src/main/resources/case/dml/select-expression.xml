<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="select_with_performance_schema_global_status">
        <from>
            <simple-table name="global_status" start-index="24" stop-index="55">
                <owner name="performance_schema" start-index="24" stop-index="41"/>
            </simple-table>
        </from>
        <projections start-index="7" stop-index="17">
            <expression-projection text="1" alias="STATUS" start-index="7" stop-index="17">
                <expr>
                    <literal-expression start-index="7" stop-index="7" value="1"/>
                </expr>
            </expression-projection>
        </projections>
        <where start-index="57" stop-index="165">
            <expr>
                <binary-operation-expression start-index="63" stop-index="165">
                    <left>
                        <binary-operation-expression start-index="63" stop-index="108">
                            <left>
                                <column name="VARIABLE_NAME" start-index="63" stop-index="75"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression start-index="78" stop-index="108"
                                                    value="MAX_EXECUTION_TIME_SET_FAILED"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="114" stop-index="165">
                            <left>
                                <function function-name="CONVERT" start-index="114" stop-index="146"
                                          text="CONVERT(VARIABLE_VALUE, UNSIGNED)">
                                    <parameter>
                                        <column name="VARIABLE_VALUE" start-index="122" stop-index="135"/>
                                    </parameter>
                                </function>
                            </left>
                            <operator>&gt;</operator>
                            <right>
                                <variable-segment start-index="150" stop-index="165" variable="time_set_failed"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_udf_function_mysqltest1_f1">
        <projections start-index="7" stop-index="21">
            <expression-projection text="mysqltest1.f1()" start-index="7" stop-index="21">
                <function function-name="mysqltest1.f1()" alias="creation_date" start-index="7" stop-index="21"
                          text="mysqltest1.f1()"/>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_case_when_with_mul_condition">
        <projections start-index="7" stop-index="52">
            <expression-projection text="case 1*0 when &quot;a&quot; then &quot;true&quot; else &quot;false&quot; END"
                                   start-index="7" stop-index="52">
                <expr>
                    <case-when-expression>
                        <case-expr start-index="12" stop-index="14">
                            <binary-operation-expression start-index="12" stop-index="14">
                                <left>
                                    <literal-expression value="1" start-index="12" stop-index="12"/>
                                </left>
                                <operator>*</operator>
                                <right>
                                    <literal-expression value="0" start-index="14" stop-index="14"/>
                                </right>
                            </binary-operation-expression>
                        </case-expr>
                        <when-exprs>
                            <literal-expression value="a" start-index="21" stop-index="23"/>
                        </when-exprs>
                        <then-exprs>
                            <literal-expression value="true" start-index="30" stop-index="35"/>
                        </then-exprs>
                        <else-expr>
                            <literal-expression value="false" start-index="42" stop-index="48"/>
                        </else-expr>
                    </case-when-expression>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_case_when_with_sub_condition">
        <projections start-index="7" stop-index="52">
            <expression-projection text="case 1-0 when &quot;a&quot; then &quot;true&quot; else &quot;false&quot; END"
                                   start-index="7" stop-index="52">
                <expr>
                    <case-when-expression>
                        <case-expr start-index="12" stop-index="14">
                            <binary-operation-expression start-index="12" stop-index="14">
                                <left>
                                    <literal-expression value="1" start-index="12" stop-index="12"/>
                                </left>
                                <operator>-</operator>
                                <right>
                                    <literal-expression value="0" start-index="14" stop-index="14"/>
                                </right>
                            </binary-operation-expression>
                        </case-expr>
                        <when-exprs>
                            <literal-expression value="a" start-index="21" stop-index="23"/>
                        </when-exprs>
                        <then-exprs>
                            <literal-expression value="true" start-index="30" stop-index="35"/>
                        </then-exprs>
                        <else-expr>
                            <literal-expression value="false" start-index="42" stop-index="48"/>
                        </else-expr>
                    </case-when-expression>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_case_when_with_add_condition">
        <projections start-index="7" stop-index="52">
            <expression-projection text="case 1+0 when &quot;a&quot; then &quot;true&quot; else &quot;false&quot; END"
                                   start-index="7" stop-index="52">
                <expr>
                    <case-when-expression>
                        <case-expr start-index="12" stop-index="14">
                            <binary-operation-expression start-index="12" stop-index="14">
                                <left>
                                    <literal-expression value="1" start-index="12" stop-index="12"/>
                                </left>
                                <operator>+</operator>
                                <right>
                                    <literal-expression value="0" start-index="14" stop-index="14"/>
                                </right>
                            </binary-operation-expression>
                        </case-expr>
                        <when-exprs>
                            <literal-expression value="a" start-index="21" stop-index="23"/>
                        </when-exprs>
                        <then-exprs>
                            <literal-expression value="true" start-index="30" stop-index="35"/>
                        </then-exprs>
                        <else-expr>
                            <literal-expression value="false" start-index="42" stop-index="48"/>
                        </else-expr>
                    </case-when-expression>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_case_when_with_div_condition">
        <projections start-index="7" stop-index="52">
            <expression-projection text="case 1/0 when &quot;a&quot; then &quot;true&quot; else &quot;false&quot; END"
                                   start-index="7" stop-index="52">
                <expr>
                    <case-when-expression>
                        <case-expr start-index="12" stop-index="14">
                            <binary-operation-expression start-index="12" stop-index="14">
                                <left>
                                    <literal-expression value="1" start-index="12" stop-index="12"/>
                                </left>
                                <operator>/</operator>
                                <right>
                                    <literal-expression value="0" start-index="14" stop-index="14"/>
                                </right>
                            </binary-operation-expression>
                        </case-expr>
                        <when-exprs>
                            <literal-expression value="a" start-index="21" stop-index="23"/>
                        </when-exprs>
                        <then-exprs>
                            <literal-expression value="true" start-index="30" stop-index="35"/>
                        </then-exprs>
                        <else-expr>
                            <literal-expression value="false" start-index="42" stop-index="48"/>
                        </else-expr>
                    </case-when-expression>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_expression">
        <from>
            <simple-table name="t_order" alias="o" start-index="38" stop-index="49"/>
        </from>
        <projections start-index="7" stop-index="31">
            <!-- TODO check expression-projection's stop-index whether include alias -->
            <expression-projection text="o.order_id + 1 * 2" alias="exp" start-index="7" stop-index="31"/>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="60" stop-index="69">
                <owner name="o" start-index="60" stop-index="60"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_with_expression_for_postgresql">
        <from>
            <simple-table name="t_order" alias="o" start-index="38" stop-index="49"/>
        </from>
        <projections start-index="7" stop-index="31">
            <!-- 'text' is different from other database -->
            <expression-projection text="o.order_id + 1 * 2" alias="exp" start-index="7" stop-index="31"/>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="60" stop-index="69">
                <owner name="o" start-index="60" stop-index="60"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_with_encode_function">
        <from>
            <simple-table name="test_bytea" start-index="54" stop-index="63"/>
        </from>
        <projections start-index="7" stop-index="47">
            <expression-projection text="ENCODE(test_datetype_col::bytea,'escape')" start-index="7" stop-index="47">
                <expr>
                    <function function-name="ENCODE" start-index="7" stop-index="47"
                              text="ENCODE(test_datetype_col::bytea,'escape')">
                        <parameter>
                            <type-cast-expression>
                                <expression>
                                    <column name="test_datetype_col" start-index="14" stop-index="30">
                                    </column>
                                </expression>
                                <data-type>bytea</data-type>
                            </type-cast-expression>
                        </parameter>
                        <parameter>
                            <literal-expression value="escape" start-index="39" stop-index="46"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_date_function">
        <from>
            <simple-table name="t_order_item" alias="i" start-delimiter="`" end-delimiter="`" start-index="51"
                          stop-index="69"/>
        </from>
        <projections start-index="7" stop-index="44">
            <expression-projection text="DATE(i.creation_date)" alias="creation_date" start-index="7" stop-index="44">
                <expr>
                    <function function-name="DATE" alias="creation_date" start-index="7" stop-index="27"
                              text="DATE(i.creation_date)">
                        <parameter>
                            <column name="creation_date" start-index="12" stop-index="26">
                                <owner name="i" start-index="12" stop-index="12"/>
                            </column>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <order-by>
            <expression-item expression="DATE(i.creation_date)" order-direction="DESC" start-index="80"
                             stop-index="100"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_regexp" parameters="'init', 1, 2">
        <from>
            <simple-table name="t_order_item" alias="t" start-index="14" stop-index="27"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="29" stop-index="75" literal-stop-index="80">
            <expr>
                <binary-operation-expression start-index="35" stop-index="75" literal-stop-index="80">
                    <left>
                        <binary-operation-expression start-index="35" stop-index="51" literal-stop-index="56">
                            <left>
                                <column name="status" start-index="35" stop-index="42">
                                    <owner name="t" start-index="35" stop-index="35"/>
                                </column>
                            </left>
                            <operator>REGEXP</operator>
                            <right>
                                <literal-expression value="init" start-index="51" stop-index="56"/>
                                <parameter-marker-expression parameter-index="0" start-index="51" stop-index="51"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <in-expression start-index="57" stop-index="75" literal-start-index="62"
                                       literal-stop-index="80">
                            <not>false</not>
                            <left>
                                <column name="item_id" start-index="57" stop-index="65" literal-start-index="62"
                                        literal-stop-index="70">
                                    <owner name="t" start-index="57" stop-index="57" literal-start-index="62"
                                           literal-stop-index="62"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="70" stop-index="75" literal-start-index="75"
                                                 literal-stop-index="80">
                                    <items>
                                        <literal-expression value="1" start-index="76" stop-index="76"/>
                                        <parameter-marker-expression parameter-index="1" start-index="71"
                                                                     stop-index="71"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="79" stop-index="79"/>
                                        <parameter-marker-expression parameter-index="2" start-index="74"
                                                                     stop-index="74"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_rlike" parameters="'init', 1, 2">
        <from>
            <simple-table name="t_order_item" alias="t" start-index="14" stop-index="27"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="29" stop-index="74" literal-stop-index="79">
            <expr>
                <binary-operation-expression start-index="35" stop-index="74" literal-stop-index="79">
                    <left>
                        <binary-operation-expression start-index="35" stop-index="50" literal-stop-index="55">
                            <left>
                                <column name="status" start-index="35" stop-index="42">
                                    <owner name="t" start-index="35" stop-index="35"/>
                                </column>
                            </left>
                            <operator>RLIKE</operator>
                            <right>
                                <literal-expression value="init" start-index="50" stop-index="55"/>
                                <parameter-marker-expression parameter-index="0" start-index="50" stop-index="50"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <in-expression start-index="56" stop-index="74" literal-start-index="61"
                                       literal-stop-index="79">
                            <not>false</not>
                            <left>
                                <column name="item_id" start-index="56" stop-index="64" literal-start-index="61"
                                        literal-stop-index="69">
                                    <owner name="t" start-index="56" stop-index="56" literal-start-index="61"
                                           literal-stop-index="61"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="69" stop-index="74" literal-start-index="74"
                                                 literal-stop-index="79">
                                    <items>
                                        <literal-expression value="1" start-index="75" stop-index="75"/>
                                        <parameter-marker-expression parameter-index="1" start-index="70"
                                                                     stop-index="70"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="78" stop-index="78"/>
                                        <parameter-marker-expression parameter-index="2" start-index="73"
                                                                     stop-index="73"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_case_expression">
        <projections start-index="7" stop-index="124">
            <shorthand-projection start-index="7" stop-index="9">
                <owner start-index="7" stop-index="7" name="t"/>
            </shorthand-projection>
            <column-projection start-index="11" stop-index="30" name="item_id" alias="item_id">
                <owner start-index="11" stop-index="11" name="o"/>
            </column-projection>
            <expression-projection
                    text="case when t.status = 'init' then '已启用' when t.status = 'failed' then '已停用' end"
                    start-index="32" stop-index="124" alias="stateName">
                <expr>
                    <case-when-expression>
                        <when-exprs>
                            <binary-operation-expression start-index="43" stop-index="59">
                                <left>
                                    <column name="status" start-index="43" stop-index="50">
                                        <owner name="t" start-index="43" stop-index="43"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <literal-expression value="init" start-index="54" stop-index="59"/>
                                </right>
                            </binary-operation-expression>
                        </when-exprs>
                        <when-exprs>
                            <binary-operation-expression start-index="77" stop-index="95">
                                <left>
                                    <column name="status" start-index="77" stop-index="84">
                                        <owner name="t" start-index="77" stop-index="77"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <literal-expression value="failed" start-index="88" stop-index="95"/>
                                </right>
                            </binary-operation-expression>
                        </when-exprs>
                        <then-exprs>
                            <literal-expression value="已启用" start-index="66" stop-index="70"/>
                        </then-exprs>
                        <then-exprs>
                            <literal-expression value="已停用" start-index="102" stop-index="106"/>
                        </then-exprs>
                    </case-when-expression>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <join-table join-type="LEFT">
                <left>
                    <simple-table start-index="131" stop-index="139" name="t_order" alias="t"/>
                </left>
                <right>
                    <simple-table start-index="151" stop-index="167" name="t_order_item" alias="o"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="172" stop-index="193">
                        <left>
                            <column name="order_id" start-index="172" stop-index="181">
                                <owner name="o" start-index="172" stop-index="172"/>
                            </column>
                        </left>
                        <operator>=</operator>
                        <right>
                            <column name="order_id" start-index="184" stop-index="193">
                                <owner name="t" start-index="184" stop-index="184"/>
                            </column>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <where start-index="195" stop-index="215">
            <expr>
                <binary-operation-expression start-index="201" stop-index="215">
                    <left>
                        <column name="order_id" start-index="201" stop-index="210">
                            <owner name="t" start-index="201" stop-index="201"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1000" start-index="212" stop-index="215"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <limit start-index="217" stop-index="223">
            <row-count value="1" start-index="223" stop-index="223"/>
        </limit>
    </select>

    <select sql-case-id="select_where_with_expr_with_or" parameters="1,2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="71">
            <expr>
                <binary-operation-expression start-index="28" stop-index="71">
                    <left>
                        <binary-operation-expression start-index="28" stop-index="47">
                            <left>
                                <column name="order_id" start-index="28" stop-index="43">
                                    <owner name="t_order" start-index="28" stop-index="34"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="47" stop-index="47"/>
                                <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>OR</operator>
                    <right>
                        <binary-operation-expression start-index="52" stop-index="71">
                            <left>
                                <literal-expression value="2" start-index="52" stop-index="52"/>
                                <parameter-marker-expression parameter-index="1" start-index="52" stop-index="52"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="56" stop-index="71">
                                    <owner name="t_order" start-index="56" stop-index="62"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_expr_with_or_sign" parameters="1, 2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="71">
            <expr>
                <binary-operation-expression start-index="28" stop-index="71">
                    <left>
                        <binary-operation-expression start-index="28" stop-index="47">
                            <left>
                                <column name="order_id" start-index="28" stop-index="43">
                                    <owner name="t_order" start-index="28" stop-index="34"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                                <literal-expression value="1" start-index="47" stop-index="47"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>||</operator>
                    <right>
                        <binary-operation-expression start-index="52" stop-index="71">
                            <left>
                                <parameter-marker-expression parameter-index="1" start-index="52" stop-index="52"/>
                                <literal-expression value="2" start-index="52" stop-index="52"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="56" stop-index="71">
                                    <owner name="t_order" start-index="56" stop-index="62"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_expr_with_xor" parameters="1,2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="72">
            <expr>
                <binary-operation-expression start-index="28" stop-index="72">
                    <left>
                        <binary-operation-expression start-index="28" stop-index="47">
                            <left>
                                <column name="order_id" start-index="28" stop-index="43">
                                    <owner name="t_order" start-index="28" stop-index="34"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="47" stop-index="47"/>
                                <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>XOR</operator>
                    <right>
                        <binary-operation-expression start-index="53" stop-index="72">
                            <left>
                                <literal-expression value="2" start-index="53" stop-index="53"/>
                                <parameter-marker-expression parameter-index="1" start-index="53" stop-index="53"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="57" stop-index="72">
                                    <owner name="t_order" start-index="57" stop-index="63"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_expr_with_and" parameters="1,2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="72">
            <expr>
                <binary-operation-expression start-index="28" stop-index="72">
                    <left>
                        <binary-operation-expression start-index="28" stop-index="47">
                            <left>
                                <column name="order_id" start-index="28" stop-index="43">
                                    <owner name="t_order" start-index="28" stop-index="34"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="47" stop-index="47"/>
                                <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="53" stop-index="72">
                            <left>
                                <literal-expression value="2" start-index="53" stop-index="53"/>
                                <parameter-marker-expression parameter-index="1" start-index="53" stop-index="53"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="57" stop-index="72">
                                    <owner name="t_order" start-index="57" stop-index="63"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_expr_with_and_or" parameters="1,2,2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="126">
            <expr>
                <binary-operation-expression start-index="28" stop-index="126">
                    <left>
                        <binary-operation-expression start-index="28" stop-index="72">
                            <left>
                                <binary-operation-expression start-index="28" stop-index="47">
                                    <left>
                                        <column name="order_id" start-index="28" stop-index="43">
                                            <owner name="t_order" start-index="28" stop-index="34"/>
                                        </column>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="1" start-index="47" stop-index="47"/>
                                        <parameter-marker-expression parameter-index="0" start-index="47"
                                                                     stop-index="47"/>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>AND</operator>
                            <right>
                                <binary-operation-expression start-index="53" stop-index="72">
                                    <left>
                                        <literal-expression value="2" start-index="53" stop-index="53"/>
                                        <parameter-marker-expression parameter-index="1" start-index="53"
                                                                     stop-index="53"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <column name="order_id" start-index="57" stop-index="72">
                                            <owner name="t_order" start-index="57" stop-index="63"/>
                                        </column>
                                    </right>
                                </binary-operation-expression>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>OR</operator>
                    <right>
                        <binary-operation-expression start-index="77" stop-index="126">
                            <left>
                                <binary-operation-expression start-index="77" stop-index="101">
                                    <left>
                                        <column name="status" start-index="77" stop-index="90">
                                            <owner name="t_order" start-index="77" stop-index="83"/>
                                        </column>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="failed" start-index="94" stop-index="101"/>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>AND</operator>
                            <right>
                                <binary-operation-expression start-index="107" stop-index="126">
                                    <left>
                                        <literal-expression value="2" start-index="107" stop-index="107"/>
                                        <parameter-marker-expression parameter-index="2" start-index="107"
                                                                     stop-index="107"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <column name="order_id" start-index="111" stop-index="126">
                                            <owner name="t_order" start-index="111" stop-index="117"/>
                                        </column>
                                    </right>
                                </binary-operation-expression>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_expr_with_and_sign" parameters="1,2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="71">
            <expr>
                <binary-operation-expression start-index="28" stop-index="71">
                    <left>
                        <binary-operation-expression start-index="28" stop-index="47">
                            <left>
                                <column name="order_id" start-index="28" stop-index="43">
                                    <owner name="t_order" start-index="28" stop-index="34"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="47" stop-index="47"/>
                                <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>&amp;&amp;</operator>
                    <right>
                        <binary-operation-expression start-index="52" stop-index="71">
                            <left>
                                <literal-expression value="2" start-index="52" stop-index="52"/>
                                <parameter-marker-expression parameter-index="1" start-index="52" stop-index="52"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="56" stop-index="71">
                                    <owner name="t_order" start-index="56" stop-index="62"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_expr_with_not" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="53">
            <expr>
                <not-expression start-index="28" stop-index="53">
                    <expr>
                        <binary-operation-expression start-index="33" stop-index="52">
                            <left>
                                <literal-expression value="1" start-index="33" stop-index="33"/>
                                <parameter-marker-expression parameter-index="0" start-index="33" stop-index="33"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="37" stop-index="52">
                                    <owner name="t_order" start-index="37" stop-index="43"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </expr>
                </not-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_expr_with_not_sign" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="52">
            <expr>
                <not-expression start-index="28" stop-index="52">
                    <expr>
                        <binary-operation-expression start-index="32" stop-index="51">
                            <left>
                                <literal-expression value="1" start-index="32" stop-index="32"/>
                                <parameter-marker-expression parameter-index="0" start-index="32" stop-index="32"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="36" stop-index="51">
                                    <owner name="t_order" start-index="36" stop-index="42"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </expr>
                </not-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_expr_with_is" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="56">
            <expr>
                <binary-operation-expression start-index="28" stop-index="56">
                    <left>
                        <binary-operation-expression start-index="28" stop-index="47">
                            <left>
                                <literal-expression value="1" start-index="28" stop-index="28"/>
                                <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="32" stop-index="47">
                                    <owner name="t_order" start-index="32" stop-index="38"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>IS</operator>
                    <right>
                        <literal-expression value="FALSE" start-index="52" stop-index="56"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_expr_with_is_not" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="60">
            <expr>
                <binary-operation-expression start-index="28" stop-index="60">
                    <left>
                        <binary-operation-expression start-index="28" stop-index="47">
                            <left>
                                <literal-expression value="1" start-index="28" stop-index="28"/>
                                <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="32" stop-index="47">
                                    <owner name="t_order" start-index="32" stop-index="38"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>IS</operator>
                    <right>
                        <literal-expression value="NOT FALSE" start-index="52" stop-index="60"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_boolean_primary_with_is">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="49">
            <expr>
                <binary-operation-expression start-index="28" stop-index="49">
                    <left>
                        <column name="status" start-index="28" stop-index="41">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>IS</operator>
                    <right>
                        <literal-expression value="NULL" start-index="46" stop-index="49"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_boolean_primary_with_is_not">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="53">
            <expr>
                <binary-operation-expression start-index="28" stop-index="53">
                    <left>
                        <column name="status" start-index="28" stop-index="41">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>IS</operator>
                    <right>
                        <literal-expression value="NOT NULL" start-index="46" stop-index="53"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_boolean_primary_with_null_safe">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="62">
            <expr>
                <binary-operation-expression start-index="28" stop-index="62">
                    <left>
                        <column name="status" start-index="28" stop-index="41">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>&lt;=&gt;</operator>
                    <right>
                        <column name="order_id" start-index="47" stop-index="62">
                            <owner name="t_order" start-index="47" stop-index="53"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_boolean_primary_with_comparison_predicate">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="61">
            <expr>
                <binary-operation-expression start-index="28" stop-index="61">
                    <left>
                        <column name="status" start-index="28" stop-index="41">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>&gt;=</operator>
                    <right>
                        <column name="order_id" start-index="46" stop-index="61">
                            <owner name="t_order" start-index="46" stop-index="52"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_boolean_primary_with_comparison_subquery" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="98">
            <expr>
                <binary-operation-expression start-index="28" stop-index="98">
                    <left>
                        <column name="status" start-index="28" stop-index="41">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <subquery start-index="49" stop-index="98">
                            <select>
                                <from start-index="69" stop-index="80">
                                    <simple-table name="t_order_item" start-index="69" stop-index="80"/>
                                </from>
                                <projections distinct-row="false" start-index="57" stop-index="62">
                                    <column-projection name="status" start-index="57" stop-index="62"/>
                                </projections>
                                <where start-index="82" stop-index="97">
                                    <expr>
                                        <binary-operation-expression start-index="88" stop-index="97">
                                            <left>
                                                <column name="status" start-index="88" stop-index="93"/>
                                            </left>
                                            <operator>&gt;</operator>
                                            <right>
                                                <literal-expression value="1" start-index="97" stop-index="97"/>
                                                <parameter-marker-expression parameter-index="0" start-index="97"
                                                                             stop-index="97"/>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_predicate_with_in_subquery" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="103">
            <expr>
                <in-expression start-index="28" stop-index="103">
                    <not>true</not>
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <right>
                        <subquery start-index="52" stop-index="103">
                            <select>
                                <from start-index="74" stop-index="85">
                                    <simple-table name="t_order_item" start-index="74" stop-index="85"/>
                                </from>
                                <projections distinct-row="false" start-index="60" stop-index="67">
                                    <column-projection name="order_id" start-index="60" stop-index="67"/>
                                </projections>
                                <where start-index="87" stop-index="102">
                                    <expr>
                                        <binary-operation-expression start-index="93" stop-index="102">
                                            <left>
                                                <column name="status" start-index="93" stop-index="98"/>
                                            </left>
                                            <operator>&gt;</operator>
                                            <right>
                                                <literal-expression value="1" start-index="102" stop-index="102"/>
                                                <parameter-marker-expression parameter-index="0" start-index="102"
                                                                             stop-index="102"/>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </right>
                </in-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_predicate_with_in_expr" parameters="1,2,3">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="56">
            <expr>
                <in-expression start-index="28" stop-index="56">
                    <not>false</not>
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <right>
                        <list-expression start-index="48" stop-index="56">
                            <items>
                                <literal-expression value="1" start-index="49" stop-index="49"/>
                                <parameter-marker-expression parameter-index="0" start-index="49" stop-index="49"/>
                            </items>
                            <items>
                                <literal-expression value="2" start-index="52" stop-index="52"/>
                                <parameter-marker-expression parameter-index="1" start-index="52" stop-index="52"/>
                            </items>
                            <items>
                                <literal-expression value="3" start-index="55" stop-index="55"/>
                                <parameter-marker-expression parameter-index="2" start-index="55" stop-index="55"/>
                            </items>
                        </list-expression>
                    </right>
                </in-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_predicate_with_between" parameters="1,2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="59">
            <expr>
                <between-expression start-index="28" stop-index="59">
                    <not>false</not>
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <between-expr>
                        <literal-expression value="1" start-index="53" stop-index="53"/>
                        <parameter-marker-expression parameter-index="0" start-index="53" stop-index="53"/>
                    </between-expr>
                    <and-expr>
                        <literal-expression value="2" start-index="59" stop-index="59"/>
                        <parameter-marker-expression parameter-index="1" start-index="59" stop-index="59"/>
                    </and-expr>
                </between-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_predicate_with_sounds_like">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="60">
            <expr>
                <binary-operation-expression start-index="28" stop-index="60">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>SOUNDS LIKE</operator>
                    <right>
                        <literal-expression value="1%" start-index="57" stop-index="60"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_predicate_with_like">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="68">
            <expr>
                <binary-operation-expression start-index="28" stop-index="68">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>NOT LIKE</operator>
                    <right>
                        <list-expression start-index="54" stop-index="68">
                            <items>
                                <literal-expression value="1%" start-index="54" stop-index="57"/>
                            </items>
                            <items>
                                <literal-expression value="$" start-index="66" stop-index="68"/>
                            </items>
                        </list-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_predicate_with_not_like">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="55">
            <expr>
                <binary-operation-expression start-index="28" stop-index="55">
                    <left>
                        <column name="status" start-index="28" stop-index="41">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>NOT LIKE</operator>
                    <right>
                        <list-expression start-index="52" stop-index="55">
                            <items>
                                <literal-expression value="1%" start-index="52" stop-index="55"/>
                            </items>
                        </list-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_predicate_with_regexp">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="62">
            <expr>
                <binary-operation-expression start-index="28" stop-index="62">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>NOT REGEXP</operator>
                    <right>
                        <literal-expression value="[123]" start-index="56" stop-index="62"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_predicate_with_rlike">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="61">
            <expr>
                <binary-operation-expression start-index="28" stop-index="61">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>NOT RLIKE</operator>
                    <right>
                        <literal-expression value="[123]" start-index="55" stop-index="61"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_vertical_bar" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="47">
            <expr>
                <binary-operation-expression start-index="28" stop-index="47">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>|</operator>
                    <right>
                        <literal-expression value="1" start-index="47" stop-index="47"/>
                        <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_ampersand" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="47">
            <expr>
                <binary-operation-expression start-index="28" stop-index="47">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>&amp;</operator>
                    <right>
                        <literal-expression value="1" start-index="47" stop-index="47"/>
                        <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_signed_left_shift" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="48">
            <expr>
                <binary-operation-expression start-index="28" stop-index="48">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>&lt;&lt;</operator>
                    <right>
                        <literal-expression value="1" start-index="48" stop-index="48"/>
                        <parameter-marker-expression parameter-index="0" start-index="48" stop-index="48"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_signed_right_shift" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="48">
            <expr>
                <binary-operation-expression start-index="28" stop-index="48">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>&gt;&gt;</operator>
                    <right>
                        <literal-expression value="1" start-index="48" stop-index="48"/>
                        <parameter-marker-expression parameter-index="0" start-index="48" stop-index="48"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_plus" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="47">
            <expr>
                <binary-operation-expression start-index="28" stop-index="47">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>+</operator>
                    <right>
                        <literal-expression value="1" start-index="47" stop-index="47"/>
                        <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_minus" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="47">
            <expr>
                <binary-operation-expression start-index="28" stop-index="47">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>-</operator>
                    <right>
                        <literal-expression value="1" start-index="47" stop-index="47"/>
                        <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_asterisk" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="47">
            <expr>
                <binary-operation-expression start-index="28" stop-index="47">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>*</operator>
                    <right>
                        <literal-expression value="1" start-index="47" stop-index="47"/>
                        <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_slash" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="47">
            <expr>
                <binary-operation-expression start-index="28" stop-index="47">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>/</operator>
                    <right>
                        <literal-expression value="1" start-index="47" stop-index="47"/>
                        <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_div" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="49">
            <expr>
                <binary-operation-expression start-index="28" stop-index="49">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>DIV</operator>
                    <right>
                        <literal-expression value="1" start-index="49" stop-index="49"/>
                        <parameter-marker-expression parameter-index="0" start-index="49" stop-index="49"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_mod" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="49">
            <expr>
                <binary-operation-expression start-index="28" stop-index="49">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>MOD</operator>
                    <right>
                        <literal-expression value="1" start-index="49" stop-index="49"/>
                        <parameter-marker-expression parameter-index="0" start-index="49" stop-index="49"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_mod_sign" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="47">
            <expr>
                <binary-operation-expression start-index="28" stop-index="47">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>%</operator>
                    <right>
                        <literal-expression value="1" start-index="47" stop-index="47"/>
                        <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_caret" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="47">
            <expr>
                <binary-operation-expression start-index="28" stop-index="47">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>^</operator>
                    <right>
                        <literal-expression value="1" start-index="47" stop-index="47"/>
                        <parameter-marker-expression parameter-index="0" start-index="47" stop-index="47"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_plus_interval">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="63">
            <expr>
                <binary-operation-expression start-index="28" stop-index="63">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>+</operator>
                    <right>
                        <function function-name="INTERVAL" text="INTERVAL" start-index="47" stop-index="54">
                            <parameter>
                                <literal-expression value="1" start-index="56" stop-index="56"/>
                            </parameter>
                            <parameter>
                                <literal-expression value="SECOND" start-index="58" stop-index="63"/>
                            </parameter>
                        </function>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_bit_expr_with_minus_interval">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="63">
            <expr>
                <binary-operation-expression start-index="28" stop-index="63">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>-</operator>
                    <right>
                        <function function-name="INTERVAL" text="INTERVAL" start-index="47" stop-index="54">
                            <parameter>
                                <literal-expression value="1" start-index="56" stop-index="56"/>
                            </parameter>
                            <parameter>
                                <literal-expression value="SECOND" start-index="58" stop-index="63"/>
                            </parameter>
                        </function>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_literals" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="39">
            <expr>
                <binary-operation-expression start-index="28" stop-index="39">
                    <left>
                        <literal-expression value="1" start-index="28" stop-index="28"/>
                        <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                    </left>
                    <operator>&lt;</operator>
                    <right>
                        <column name="order_id" start-index="32" stop-index="39"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_column">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="43">
            <expr>
                <column name="order_id" start-index="28" stop-index="43">
                    <owner name="t_order" start-index="28" stop-index="34"/>
                </column>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_function_call">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="43">
            <expr>
                <binary-operation-expression start-index="28" stop-index="43">
                    <left>
                        <function function-name="now" start-index="28" stop-index="32" text="now()"/>
                    </left>
                    <operator>&lt;</operator>
                    <right>
                        <column name="order_id" start-index="36" stop-index="43"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_collate">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="62" literal-stop-index="62">
            <expr>
                <collate-expression start-index="28" stop-index="62" literal-stop-index="62">
                    <collate-name>
                        <literal-expression value="utf8mb4_0900_ai_ci" start-index="37" stop-index="62"/>
                        <parameter-marker-expression parameter-index="0" start-index="37" stop-index="45"/>
                    </collate-name>
                </collate-expression>
                <expr>
                    <column name="order_id" start-index="28" stop-index="35"/>
                </expr>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_variable">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="55">
            <expr>
                <binary-operation-expression start-index="28" stop-index="55">
                    <left>
                        <variable-segment text="@@max_connections" start-index="28" stop-index="44"
                                          variable="max_connections"/>
                    </left>
                    <operator>&lt;</operator>
                    <right>
                        <column name="order_id" start-index="48" stop-index="55"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_plus" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="47">
            <expr>
                <binary-operation-expression start-index="28" stop-index="47">
                    <left>
                        <literal-expression value="1" start-index="28" stop-index="28"/>
                        <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                    </left>
                    <operator>+</operator>
                    <right>
                        <column name="order_id" start-index="32" stop-index="47">
                            <owner name="t_order" start-index="32" stop-index="38"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_minus" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="47">
            <expr>
                <binary-operation-expression start-index="28" stop-index="47">
                    <left>
                        <literal-expression value="1" start-index="28" stop-index="28"/>
                        <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                    </left>
                    <operator>-</operator>
                    <right>
                        <column name="order_id" start-index="32" stop-index="47">
                            <owner name="t_order" start-index="32" stop-index="38"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_tilde">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="44">
            <expr>
                <unary-operation-expression text="~t_order.order_id" start-index="28" stop-index="44">
                    <operator>~</operator>
                    <expr>
                        <column name="order_id" start-index="29" stop-index="44">
                            <owner name="t_order" start-index="29" stop-index="35"/>
                        </column>
                    </expr>
                </unary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_not">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="44">
            <expr>
                <not-expression start-index="28" stop-index="44">
                    <expr>
                        <column name="order_id" start-index="29" stop-index="44">
                            <owner name="t_order" start-index="29" stop-index="35"/>
                        </column>
                    </expr>
                </not-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_binary">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="50">
            <expr>
                <column name="order_id" start-index="35" stop-index="50">
                    <owner name="t_order" start-index="35" stop-index="41"/>
                </column>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_binary_value" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="54">
            <expr>
                <binary-operation-expression start-index="28" stop-index="54">
                    <left>
                        <column name="order_id" start-index="28" stop-index="43">
                            <owner name="t_order" start-index="28" stop-index="34"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <parameter-marker-expression parameter-index="0" start-index="54" stop-index="54"/>
                        <literal-expression value="1" start-index="54" stop-index="54"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_row" parameters="'abc'">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="61" literal-stop-index="65">
            <expr>
                <binary-operation-expression start-index="28" stop-index="61" literal-stop-index="65">
                    <left>
                        <row-expression text="ROW(?, now())" literal-text="ROW('abc', now())" start-index="28"
                                        stop-index="40" literal-stop-index="44">
                            <items>
                                <parameter-marker-expression start-index="32" stop-index="32" parameter-index="0"/>
                                <literal-expression start-index="32" stop-index="36" value="abc"/>
                            </items>
                            <items>
                                <function start-index="35" stop-index="39" text="now()" function-name="now"
                                          literal-start-index="39" literal-stop-index="43"/>
                            </items>
                        </row-expression>
                    </left>
                    <operator>=</operator>
                    <right>
                        <row-expression text="(order_id, status)" start-index="44" stop-index="61"
                                        literal-start-index="48" literal-stop-index="65">
                            <items>
                                <column start-index="45" stop-index="52" literal-start-index="49"
                                        literal-stop-index="56" name="order_id"/>
                            </items>
                            <items>
                                <column start-index="55" stop-index="60" literal-start-index="59"
                                        literal-stop-index="64" name="status"/>
                            </items>
                        </row-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_subquery" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="79">
            <expr>
                <subquery start-index="28" stop-index="79">
                    <select>
                        <from start-index="50" stop-index="61">
                            <simple-table name="t_order_item" start-index="50" stop-index="61"/>
                        </from>
                        <projections distinct-row="false" start-index="36" stop-index="43">
                            <column-projection name="order_id" start-index="36" stop-index="43"/>
                        </projections>
                        <where start-index="63" stop-index="78">
                            <expr>
                                <binary-operation-expression start-index="69" stop-index="78">
                                    <left>
                                        <column name="status" start-index="69" stop-index="74"/>
                                    </left>
                                    <operator>&gt;</operator>
                                    <right>
                                        <literal-expression value="1" start-index="78" stop-index="78"/>
                                        <parameter-marker-expression parameter-index="0" start-index="78"
                                                                     stop-index="78"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_exists_subquery" parameters="1">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="87">
            <expr>
                <exists-subquery start-index="28" stop-index="87">
                    <not>false</not>
                    <subquery start-index="35" stop-index="87">
                        <select>
                            <from start-index="57" stop-index="68">
                                <simple-table name="t_order_item" start-index="57" stop-index="68"/>
                            </from>
                            <projections distinct-row="false" start-index="43" stop-index="50">
                                <column-projection name="order_id" start-index="43" stop-index="50"/>
                            </projections>
                            <where start-index="70" stop-index="85">
                                <expr>
                                    <binary-operation-expression start-index="76" stop-index="85">
                                        <left>
                                            <column name="status" start-index="76" stop-index="81"/>
                                        </left>
                                        <operator>&gt;</operator>
                                        <right>
                                            <literal-expression value="1" start-index="85" stop-index="85"/>
                                            <parameter-marker-expression parameter-index="0" start-index="85"
                                                                         stop-index="85"/>
                                        </right>
                                    </binary-operation-expression>
                                </expr>
                            </where>
                        </select>
                    </subquery>
                </exists-subquery>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_odbc_escape_syntax" parameters="'1994-02-04 12:23:00'">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="33" literal-stop-index="53">
            <expr>
                <parameter-marker-expression start-index="32" stop-index="32" parameter-index="0"/>
                <literal-expression start-index="32" stop-index="52" value="1994-02-04 12:23:00"/>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_json_extract_sign">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="44">
            <expr>
                <function function-name="-&gt;" text="order_id -&gt;&quot;$[1]&quot;" start-index="28" stop-index="44">
                    <parameter>
                        <column name="order_id" start-index="28" stop-index="35"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="&quot;$[1]&quot;" start-index="39" stop-index="44"/>
                    </parameter>
                </function>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_json_member_of">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="56">
            <expr>
                <binary-operation-expression start-index="28" stop-index="56">
                    <left>
                        <column name="order_id" start-index="28" stop-index="35"/>
                    </left>
                    <operator>MEMBER OF</operator>
                    <right>
                        <expression-projection text="&quot;[1,2,3]&quot;" start-index="47" stop-index="55"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_json_unquote_extract_sign">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="46">
            <expr>
                <function function-name="-&gt;&gt;" text="order_id -&gt;&gt; &quot;$[1]&quot;" start-index="28"
                          stop-index="46">
                    <parameter>
                        <column name="order_id" start-index="28" stop-index="35"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="&quot;$[1]&quot;" start-index="41" stop-index="46"/>
                    </parameter>
                </function>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_json_contains">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="76">
            <expr>
                <function function-name="JSON_CONTAINS" text="JSON_CONTAINS(order_msg -> '$[*].code', 'x', '$')"
                          start-index="28" stop-index="76">
                    <parameter>
                        <function start-index="42" stop-index="65" function-name="->" text="order_msg -> '$[*].code'">
                            <parameter>
                                <column start-index="42" stop-index="50" name="order_msg"/>
                            </parameter>
                            <parameter>
                                <literal-expression start-index="55" stop-index="65" value="'$[*].code'"/>
                            </parameter>
                        </function>
                    </parameter>
                    <parameter>
                        <literal-expression start-index="68" stop-index="70" value="x"/>
                    </parameter>
                    <parameter>
                        <literal-expression start-index="73" stop-index="75" value="$"/>
                    </parameter>
                </function>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_json_contains_and_limit" parameters="1, 0, 1">
        <projections start-index="7" stop-index="28">
            <column-projection name="id" start-index="7" stop-index="8"/>
            <expression-projection text="order_info->'$.id'" start-index="11" stop-index="28"/>
        </projections>
        <from>
            <simple-table name="t_order" start-index="35" stop-index="41"/>
        </from>
        <where start-index="43" stop-index="84">
            <expr>
                <function function-name="json_contains" text="json_contains(order_info, ?, '$.id')"
                          literal-text="json_contains(order_info, 1, '$.id')" start-index="49" stop-index="84">
                    <parameter>
                        <column start-index="63" stop-index="72" name="order_info"/>
                    </parameter>
                    <parameter>
                        <parameter-marker-expression start-index="75" stop-index="75" parameter-index="0"/>
                        <literal-expression start-index="75" stop-index="75" value="1"/>
                    </parameter>
                    <parameter>
                        <literal-expression start-index="78" stop-index="83" value="$.id"/>
                    </parameter>
                </function>
            </expr>
        </where>
        <limit start-index="86" stop-index="95">
            <offset value="0" parameter-index="1" start-index="92" stop-index="92"/>
            <row-count value="1" parameter-index="2" start-index="95" stop-index="95"/>
        </limit>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_match" parameters="'keyword'">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="80" literal-stop-index="88">
            <expr>
                <match-expression start-index="28" stop-index="88">
                    <expr>
                        <literal-expression value="keyword" start-index="54" stop-index="62"/>
                        <parameter-marker-expression parameter-index="0" start-index="54" stop-index="54"/>
                    </expr>
                </match-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_simple_expr_with_case" parameters="1,'true','false'">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <where start-index="22" stop-index="67" literal-stop-index="78">
            <expr>
                <case-when-expression>
                    <when-exprs>
                        <binary-operation-expression start-index="38" stop-index="49">
                            <left>
                                <column name="order_id" start-index="38" stop-index="45"/>
                            </left>
                            <operator>&gt;</operator>
                            <right>
                                <literal-expression value="1" start-index="49" stop-index="49"/>
                                <parameter-marker-expression parameter-index="0" start-index="49" stop-index="49"/>
                            </right>
                        </binary-operation-expression>
                    </when-exprs>
                    <then-exprs>
                        <literal-expression value="true" start-index="56" stop-index="61"/>
                        <parameter-marker-expression parameter-index="1" start-index="56" stop-index="56"/>
                    </then-exprs>
                    <else-expr>
                        <literal-expression value="false" start-index="68" stop-index="74"/>
                        <parameter-marker-expression parameter-index="2" start-index="63" stop-index="63"/>
                    </else-expr>
                </case-when-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_where_with_expr_with_not_with_order_by">
        <from start-index="53" stop-index="61">
            <simple-table name="employees" start-index="53" stop-index="61"/>
        </from>
        <projections start-index="7" stop-index="46">
            <column-projection name="last_name" start-index="7" stop-index="15"/>
            <column-projection name="job_id" start-index="18" stop-index="23"/>
            <column-projection name="salary" start-index="26" stop-index="31"/>
            <column-projection name="department_id" start-index="34" stop-index="46"/>
        </projections>
        <where start-index="63" stop-index="116">
            <expr>
                <not-expression start-index="69" stop-index="116">
                    <expr>
                        <binary-operation-expression start-index="74" stop-index="115">
                            <left>
                                <binary-operation-expression start-index="74" stop-index="92">
                                    <left>
                                        <column name="job_id" start-index="74" stop-index="79"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="PU_CLERK" start-index="83" stop-index="92"/>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>AND</operator>
                            <right>
                                <binary-operation-expression start-index="98" stop-index="115">
                                    <left>
                                        <column name="department_id" start-index="98" stop-index="110"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="30" start-index="114" stop-index="115"/>
                                    </right>
                                </binary-operation-expression>
                            </right>
                        </binary-operation-expression>
                    </expr>
                </not-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="last_name" start-index="127" stop-index="135"/>
        </order-by>
    </select>

    <select sql-case-id="select_where_with_subquery">
        <from start-index="37" stop-index="45">
            <simple-table name="employees" start-index="37" stop-index="45"/>
        </from>
        <projections start-index="7" stop-index="30">
            <column-projection name="last_name" start-index="7" stop-index="15"/>
            <column-projection name="department_id" start-index="18" stop-index="30"/>
        </projections>
        <where start-index="47" stop-index="133">
            <expr>
                <binary-operation-expression start-index="53" stop-index="133">
                    <left>
                        <column name="department_id" start-index="53" stop-index="65"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <subquery start-index="69" stop-index="133">
                            <select>
                                <from start-index="96" stop-index="104">
                                    <simple-table name="employees" start-index="96" stop-index="104"/>
                                </from>
                                <projections start-index="77" stop-index="89">
                                    <column-projection name="department_id" start-index="77" stop-index="89"/>
                                </projections>
                                <where start-index="106" stop-index="132">
                                    <expr>
                                        <binary-operation-expression start-index="112" stop-index="132">
                                            <left>
                                                <column name="last_name" start-index="112" stop-index="120"/>
                                            </left>
                                            <operator>=</operator>
                                            <right>
                                                <literal-expression value="Lorentz" start-index="124" stop-index="132"/>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="last_name" start-index="144" stop-index="152"/>
            <column-item name="department_id" start-index="155" stop-index="167"/>
        </order-by>
    </select>

    <select sql-case-id="select_where_with_expr_with_not_in">
        <from start-index="14" stop-index="22">
            <simple-table name="employees" start-index="14" stop-index="22"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="24" stop-index="114">
            <expr>
                <in-expression start-index="30" stop-index="114">
                    <not>true</not>
                    <left>
                        <column name="department_id" start-index="30" stop-index="42"/>
                    </left>
                    <right>
                        <subquery start-index="51" stop-index="114">
                            <select>
                                <from start-index="78" stop-index="70">
                                    <simple-table name="departments" start-index="78" stop-index="88"/>
                                </from>
                                <projections start-index="59" stop-index="71">
                                    <column-projection name="department_id" start-index="59" stop-index="71"/>
                                </projections>
                                <where start-index="90" stop-index="113">
                                    <expr>
                                        <binary-operation-expression start-index="96" stop-index="113">
                                            <left>
                                                <column name="location_id" start-index="96" stop-index="106"/>
                                            </left>
                                            <operator>=</operator>
                                            <right>
                                                <literal-expression value="1700" start-index="110" stop-index="113"/>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </right>
                </in-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="last_name" start-index="125" stop-index="133"/>
        </order-by>
    </select>

    <select sql-case-id="select_projections_with_expr">
        <projections start-index="7" stop-index="58">
            <expression-projection start-index="7" stop-index="11" text="10+20"/>
            <expression-projection start-index="13" stop-index="56" text="CASE order_id WHEN 1 THEN '11' ELSE '00' END">
                <expr>
                    <case-when-expression>
                        <case-expr>
                            <column name="order_id" start-index="18" stop-index="25"/>
                        </case-expr>
                        <when-exprs>
                            <literal-expression value="1" start-index="32" stop-index="32"/>
                        </when-exprs>
                        <then-exprs>
                            <literal-expression value="11" start-index="39" stop-index="42"/>
                        </then-exprs>
                        <else-expr>
                            <literal-expression value="00" start-index="49" stop-index="52"/>
                        </else-expr>
                    </case-when-expression>
                </expr>
            </expression-projection>
            <expression-projection start-index="58" stop-index="58" text="1">
                <expr>
                    <literal-expression value="1" start-index="58" stop-index="58"/>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="65" stop-index="71"/>
        </from>
    </select>

    <select sql-case-id="select_projections_with_only_expr">
        <projections start-index="7" stop-index="50">
            <expression-projection start-index="7" stop-index="50" text="CASE order_id WHEN 1 THEN '11' ELSE '00' END">
                <expr>
                    <case-when-expression>
                        <case-expr>
                            <column name="order_id" start-index="12" stop-index="19"/>
                        </case-expr>
                        <when-exprs>
                            <literal-expression value="1" start-index="26" stop-index="26"/>
                        </when-exprs>
                        <then-exprs>
                            <literal-expression value="11" start-index="33" stop-index="36"/>
                        </then-exprs>
                        <else-expr>
                            <literal-expression value="00" start-index="43" stop-index="46"/>
                        </else-expr>
                    </case-when-expression>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="57" stop-index="63"/>
        </from>
    </select>

    <select sql-case-id="select_with_amp">
        <projections start-index="7" stop-index="11">
            <expression-projection text="1 &amp; 1" start-index="7" stop-index="11"/>
        </projections>
    </select>

    <select sql-case-id="select_with_vertical_bar">
        <projections start-index="7" stop-index="11">
            <expression-projection text="1 | 1" start-index="7" stop-index="11"/>
        </projections>
    </select>

    <select sql-case-id="select_with_abs_function">
        <from>
            <simple-table name="t_order" start-index="19" stop-index="25"/>
        </from>
        <projections start-index="7" stop-index="12">
            <expression-projection text="ABS(1)" start-index="7" stop-index="12">
                <expr>
                    <function function-name="ABS" text="ABS(1)" start-index="7" stop-index="12">
                        <parameter>
                            <literal-expression value="1" start-index="11" stop-index="11"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <where start-index="27" stop-index="42">
            <expr>
                <binary-operation-expression start-index="33" stop-index="42">
                    <left>
                        <function function-name="ABS" text="ABS(1)" start-index="33" stop-index="38">
                            <parameter>
                                <literal-expression value="1" start-index="37" stop-index="37"/>
                            </parameter>
                        </function>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="1" start-index="42" stop-index="42"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <group-by>
            <expression-item expression="ABS(1)" start-index="53" stop-index="58">
                <expr>
                    <function function-name="ABS" text="ABS(1)" start-index="53" stop-index="58">
                        <parameter>
                            <literal-expression value="1" start-index="57" stop-index="57"/>
                        </parameter>
                    </function>
                </expr>
            </expression-item>
        </group-by>
        <order-by>
            <expression-item expression="ABS(1)" start-index="69" stop-index="74">
                <expr>
                    <function function-name="ABS" text="ABS(1)" start-index="69" stop-index="74">
                        <parameter>
                            <literal-expression value="1" start-index="73" stop-index="73"/>
                        </parameter>
                    </function>
                </expr>
            </expression-item>
        </order-by>
    </select>

    <select sql-case-id="select_with_insert_function">
        <from>
            <simple-table name="t_order" start-index="50" stop-index="56"/>
        </from>
        <projections start-index="7" stop-index="43">
            <expression-projection text="INSERT(phone, 4, 3, '***')" alias="phone" start-index="7" stop-index="43">
                <expr>
                    <function function-name="INSERT" text="INSERT(phone, 4, 3, '***')" start-index="7" stop-index="32">
                        <parameter>
                            <column name="phone" start-index="14" stop-index="18"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="4" start-index="21" stop-index="21"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="3" start-index="24" stop-index="24"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="***" start-index="27" stop-index="31"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_repeat_function">
        <from>
            <simple-table name="t_order" start-index="31" stop-index="37"/>
        </from>
        <projections start-index="7" stop-index="24">
            <expression-projection text="REPEAT('MySQL', 3)" start-index="7" stop-index="24">
                <expr>
                    <function function-name="REPEAT" text="REPEAT('MySQL', 3)" start-index="7" stop-index="24">
                        <parameter>
                            <literal-expression value="MySQL" start-index="14" stop-index="20"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="3" start-index="23" stop-index="23"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_regular_function">
        <from>
            <simple-table name="t_order" start-index="17" stop-index="23"/>
        </from>
        <projections start-index="7" stop-index="10">
            <expression-projection text="A(1)" start-index="7" stop-index="10">
                <expr>
                    <function function-name="A" text="A(1)" start-index="7" stop-index="10">
                        <parameter>
                            <literal-expression value="1" start-index="9" stop-index="9"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <where start-index="25" stop-index="38">
            <expr>
                <binary-operation-expression start-index="31" stop-index="38">
                    <left>
                        <function function-name="A" text="A(1)" start-index="31" stop-index="34">
                            <parameter>
                                <literal-expression value="1" start-index="33" stop-index="33"/>
                            </parameter>
                        </function>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="38" stop-index="38"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <group-by>
            <expression-item expression="A(order_id)" start-index="49" stop-index="59">
                <expr>
                    <function function-name="A" text="A(order_id)" start-index="49" stop-index="59">
                        <parameter>
                            <column name="order_id" start-index="51" stop-index="58"/>
                        </parameter>
                    </function>
                </expr>
            </expression-item>
        </group-by>
        <order-by>
            <expression-item expression="A(order_id)" start-index="70" stop-index="80">
                <expr>
                    <function function-name="A" text="A(order_id)" start-index="70" stop-index="80">
                        <parameter>
                            <column name="order_id" start-index="72" stop-index="79"/>
                        </parameter>
                    </function>
                </expr>
            </expression-item>
        </order-by>
    </select>

    <select sql-case-id="select_with_regular_function_for_sql92">
        <from>
            <simple-table name="t_order" start-index="17" stop-index="23"/>
        </from>
        <projections start-index="7" stop-index="10">
            <expression-projection text="A(1)" start-index="7" stop-index="10">
                <expr>
                    <function function-name="A" text="A(1)" start-index="7" stop-index="10">
                        <parameter>
                            <literal-expression value="1" start-index="9" stop-index="9"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <where start-index="25" stop-index="38">
            <expr>
                <binary-operation-expression start-index="31" stop-index="38">
                    <left>
                        <function function-name="A" text="A(1)" start-index="31" stop-index="34">
                            <parameter>
                                <literal-expression value="1" start-index="33" stop-index="33"/>
                            </parameter>
                        </function>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="38" stop-index="38"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_regular_function_utc_timestamp">
        <projections start-index="7" stop-index="38">
            <expression-projection text="TIMEDIFF(NOW(), UTC_TIMESTAMP())" start-index="7" stop-index="38">
                <expr>
                    <function function-name="TIMEDIFF" text="TIMEDIFF(NOW(), UTC_TIMESTAMP())" start-index="7"
                              stop-index="38">
                        <parameter>
                            <function function-name="NOW" text="NOW()" start-index="16" stop-index="20"/>
                        </parameter>
                        <parameter>
                            <function function-name="UTC_TIMESTAMP" text="UTC_TIMESTAMP()" start-index="23"
                                      stop-index="37"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_collate_with_marker" parameters="latin1_bin">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <where start-index="22" stop-index="45" literal-stop-index="54">
            <expr>
                <collate-expression start-index="28" stop-index="45" literal-stop-index="54">
                    <collate-name>
                        <literal-expression value="latin1_bin" start-index="37" stop-index="54"/>
                        <parameter-marker-expression parameter-index="0" start-index="37" stop-index="45"/>
                    </collate-name>
                </collate-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_age_for_postgres">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <function-table>
                <table-function function-name="cypher" text="cypher('sharding_test_1', $$ CREATE (n) $$)">
                    <parameter>
                        <literal-expression value="sharding_test_1" start-index="21" stop-index="37"/>
                        <common-expression literal-text="$$ CREATE (n) $$" start-index="40" stop-index="55"/>
                    </parameter>
                </table-function>
            </function-table>
        </from>
    </select>

    <select sql-case-id="select_datetime_expression">
        <projections start-index="7" stop-index="37">
            <expression-projection text="SYSTIMESTAMP AT TIME ZONE 'UTC'" start-index="7" stop-index="37"/>
        </projections>
        <from>
            <simple-table name="DUAL" start-index="44" stop-index="47"/>
        </from>
    </select>

    <select sql-case-id="select_between_expression">
        <projections start-index="7" stop-index="44">
            <expression-projection text="item_id BETWEEN 1 AND order_id" start-index="7" stop-index="36">
                <expr>
                    <between-expression start-index="7" stop-index="36">
                        <left>
                            <column start-index="7" stop-index="13" name="item_id"/>
                        </left>
                        <between-expr>
                            <literal-expression start-index="23" stop-index="23" value="1"/>
                        </between-expr>
                        <and-expr>
                            <column start-index="29" stop-index="36" name="order_id"/>
                        </and-expr>
                    </between-expression>
                </expr>
            </expression-projection>
            <column-projection start-index="39" stop-index="44" name="status"/>
        </projections>
        <from>
            <simple-table name="t_order_item" start-index="51" stop-index="62"/>
        </from>
    </select>

    <select sql-case-id="select_dbms_logmnr_mine_value_regular_function">
        <projections start-index="7" stop-index="63">
            <expression-projection text="DBMS_LOGMNR.MINE_VALUE(UNDO_VALUE, 'HR.EMPLOYEES.SALARY')" start-index="7"
                                   stop-index="63">
                <function function-name="MINE_VALUE" text="DBMS_LOGMNR.MINE_VALUE(UNDO_VALUE, 'HR.EMPLOYEES.SALARY')"
                          owner="DBMS_LOGMNR">
                    <parameter>
                        <column name="UNDO_VALUE"/>
                        <column name="HR.EMPLOYEES.SALARY"/>
                    </parameter>
                </function>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="V$LOGMNR_CONTENTS" start-index="70" stop-index="86"/>
        </from>
    </select>

    <select sql-case-id="select_interval_day_to_second_expression">
        <projections start-index="7" stop-index="50">
            <expression-projection text="(SYSTIMESTAMP - order_date) DAY(9) TO SECOND" start-index="7" stop-index="50">
                <function>
                    <interval-expression>
                        <left>
                            <column name="SYSTIMESTAMP" start-index="" stop-index=""/>
                        </left>
                        <operator>-</operator>
                        <right>
                            <column name="order_date" start-index="" stop-index=""/>
                        </right>
                        <interval-day-to-second-expr>
                            <leading-field-precision>9</leading-field-precision>
                        </interval-day-to-second-expr>
                    </interval-expression>
                </function>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="orders" start-index="57" stop-index="62"/>
        </from>
    </select>

    <select sql-case-id="select_feature_function">
        <projections start-index="7" stop-index="45">
            <expression-projection text="FEATURE_VALUE(nmf_sh_sample, 3 USING *)" start-index="7" stop-index="45">
                <function function-name="FEATURE_VALUE" text="FEATURE_VALUE(nmf_sh_sample, 3 USING *)"/>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="nmf_sh_sample_apply_prepared" start-index="52" stop-index="79"/>
        </from>
    </select>

    <select sql-case-id="select_cluster_function">
        <projections start-index="7" stop-index="55">
            <expression-projection text="CLUSTER_PROBABILITY(km_sh_clus_sample, 2 USING *)" start-index="7"
                                   stop-index="55">
                <function function-name="CLUSTER_PROBABILITY" text="CLUSTER_PROBABILITY(km_sh_clus_sample, 2 USING *)"/>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="mining_data_apply_v" start-index="62" stop-index="80"/>
        </from>
    </select>

    <select sql-case-id="select_with_multiset_except_expression">
        <from>
            <simple-table name="customers_demo" start-index="103" stop-index="116"/>
        </from>
        <projections start-index="7" stop-index="96">
            <column-projection name="customer_id" start-index="7" stop-index="17"/>
            <expression-projection text="cust_address_ntab MULTISET EXCEPT DISTINCT cust_address2_ntab"
                                   alias="multiset_except" start-index="20" stop-index="96"/>
        </projections>
        <order-by>
            <column-item name="customer_id" order-direction="ASC" start-index="127" stop-index="137"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_multiset_intersect_expression">
        <from>
            <simple-table name="customers_demo" start-index="109" stop-index="122"/>
        </from>
        <projections start-index="7" stop-index="102">
            <column-projection name="customer_id" start-index="7" stop-index="17"/>
            <expression-projection text="cust_address_ntab MULTISET INTERSECT DISTINCT cust_address2_ntab"
                                   alias="multiset_intersect" start-index="20" stop-index="102" literal-start-index="20"
                                   literal-stop-index="102">
                <literalText>cust_address_ntab MULTISET INTERSECT DISTINCT cust_address2_ntab</literalText>
                <expr>
                    <multiset-expression>
                        <left>
                            <column name="cust_address_ntab" start-index="20" stop-index="36" literal-start-index="20"
                                    literal-stop-index="36"/>
                        </left>
                        <right>
                            <column name="cust_address2_ntab" start-index="66" stop-index="83" literal-start-index="66"
                                    literal-stop-index="83"/>
                        </right>
                        <operator>INTERSECT</operator>
                        <keyWord>DISTINCT</keyWord>
                    </multiset-expression>
                </expr>
            </expression-projection>
        </projections>
        <order-by>
            <column-item name="customer_id" order-direction="ASC" start-index="133" stop-index="143"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_multiset_union_expression">
        <from>
            <simple-table name="customers_demo" start-index="92" stop-index="105"/>
        </from>
        <projections start-index="7" stop-index="85">
            <column-projection name="customer_id" start-index="7" stop-index="17"/>
            <expression-projection text="cust_address_ntab MULTISET UNION cust_address2_ntab" alias="multiset_union"
                                   start-index="20" stop-index="85"/>
        </projections>
        <order-by>
            <column-item name="customer_id" order-direction="ASC" start-index="116" stop-index="126"/>
        </order-by>
    </select>

    <select sql-case-id="select_collect_expression">
        <projections start-index="7" stop-index="92">
            <expression-projection text="CAST(COLLECT(warehouse_name ORDER BY warehouse_name) AS warehouse_name_t)"
                                   alias="Warehouses" start-index="7" stop-index="92" literal-start-index="7"
                                   literal-stop-index="92">
                <literalText>CAST(COLLECT(warehouse_name ORDER BY warehouse_name) AS warehouse_name_t)</literalText>
                <expr>
                    <function function-name="CAST"
                              text="CAST(COLLECT(warehouse_name ORDER BY warehouse_name) AS warehouse_name_t)"
                              start-index="7" stop-index="79" literal-start-index="7" literal-stop-index="79">
                        <parameter>
                            <function function-name="COLLECT" text="COLLECT(warehouse_name ORDER BY warehouse_name)"
                                      start-index="12" stop-index="58" literal-start-index="12" literal-stop-index="58">
                                <parameter>
                                    <column name="warehouse_name" start-index="20" stop-index="33"
                                            literal-start-index="20" literal-stop-index="33"/>
                                </parameter>
                                <literalText>COLLECT(warehouse_name ORDER BY warehouse_name)</literalText>
                            </function>
                        </parameter>
                        <parameter>
                            <data-type value="warehouse_name_t" start-index="63" stop-index="78"/>
                        </parameter>
                        <literalText>CAST(COLLECT(warehouse_name ORDER BY warehouse_name) AS warehouse_name_t)
                        </literalText>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table start-index="99" stop-index="108" name="warehouses"/>
        </from>
    </select>

    <select sql-case-id="select_expr_dot_expr">
        <projections start-index="7" stop-index="43">
            <expression-projection start-index="7" stop-index="43" text="DBURIType('/HR/DEPARTMENTS').getXML()">
                <binary-operation-expression start-index="7" stop-index="43">
                    <left>
                        <function function-name="DBURIType" text="DBURIType('/HR/DEPARTMENTS')"/>
                    </left>
                    <operator>.</operator>
                    <right>
                        <function function-name="getXML" text="getXML()"/>
                    </right>
                </binary-operation-expression>
            </expression-projection>
        </projections>
        <from>
            <simple-table start-index="50" stop-index="53" name="DUAL"/>
        </from>
    </select>

    <select sql-case-id="select_arrow_symbol_in_function">
        <projections start-index="7" stop-index="103">
            <expression-projection start-index="7" stop-index="103"
                                   text="DECODE(DBMS_COMPRESSION.GET_COMPRESSION_TYPE(ownname => 'HR'), 'No Compression')"
                                   alias="compression_type">
                <function function-name="DECODE"
                          text="DECODE(DBMS_COMPRESSION.GET_COMPRESSION_TYPE(ownname => 'HR'), 'No Compression')"
                          alias="compression_type"/>
            </expression-projection>
        </projections>
        <from>
            <simple-table start-index="110" stop-index="113" name="DUAL"/>
        </from>
    </select>

    <select sql-case-id="select_prediction_probability_function">
        <projections start-index="7" stop-index="73">
            <expression-projection start-index="7" stop-index="73"
                                   text="PREDICTION_PROBABILITY(dt_sh_clas_sample, 1 USING *)" alias="cust_card_prob">
                <function start-index="7" stop-index="73" function-name="PREDICTION_PROBABILITY" alias="cust_card_prob"
                          text="PREDICTION_PROBABILITY(dt_sh_clas_sample, 1 USING *)"/>
            </expression-projection>
        </projections>
        <from>
            <simple-table start-index="80" stop-index="98" name="mining_data_apply_v"/>
        </from>
        <where start-index="100" stop-index="121">
            <expr>
                <binary-operation-expression start-index="106" stop-index="121">
                    <left>
                        <column name="cust_id" start-index="106" stop-index="112"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression start-index="116" stop-index="121" value="101488"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_prediction_set_function">
        <projections start-index="7" stop-index="63">
            <expression-projection start-index="7" stop-index="63"
                                   text="PREDICTION_SET(dt_sh_clas_sample COST MODEL USING *)" alias="pset">
                <function function-name="PREDICTION_SET" start-index="7" stop-index="63"
                          text="PREDICTION_SET(dt_sh_clas_sample COST MODEL USING *)" alias="pset"/>
            </expression-projection>
        </projections>
        <from>
            <simple-table start-index="70" stop-index="88" name="mining_data_apply_v"/>
        </from>
        <where start-index="90" stop-index="111">
            <expr>
                <binary-operation-expression start-index="96" stop-index="111">
                    <left>
                        <column name="cust_id" start-index="96" stop-index="102"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="100011" start-index="106" stop-index="111"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_cursor_function">
        <projections start-index="7" stop-index="100">
            <expression-projection start-index="7" stop-index="100"
                                   text="CURSOR(SELECTsalary,commission_pctFROMemployeeseWHEREe.department_id=d.department_id)">
                <function function-name="CURSOR" start-index="7" stop-index="100"
                          text="CURSOR(SELECT salary, commission_pct FROM employees e WHERE e.department_id = d.department_id)"/>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="departments" alias="d" start-index="107" stop-index="119"/>
        </from>
    </select>

    <select sql-case-id="select_prediction_bounds_function">
        <projections start-index="7" stop-index="13">
            <column-projection name="cust_id" start-index="7" stop-index="13"/>
        </projections>
        <from>
            <simple-table name="mining_data_apply_v" start-index="20" stop-index="38"/>
        </from>
        <where start-index="40" stop-index="107">
            <expr>
                <binary-operation-expression start-index="46" stop-index="107">
                    <left>
                        <binary-operation-expression start-index="46" stop-index="102">
                            <left>
                                <function function-name="PREDICTION_BOUNDS" start-index="46" stop-index="96"
                                          text="PREDICTION_BOUNDS(glmr_sh_regr_sample,0.98 USING *)"/>
                            </left>
                            <operator>.</operator>
                            <right>
                                <column start-index="98" stop-index="102" name="LOWER"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression start-index="106" stop-index="107" value="24"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_prediction_function">
        <projections start-index="7" stop-index="17">
            <column-projection name="cust_gender" start-index="7" stop-index="17"/>
        </projections>
        <from>
            <simple-table name="mining_data_apply_v" start-index="24" stop-index="42"/>
        </from>
        <where start-index="44" stop-index="153">
            <expr>
                <binary-operation-expression start-index="50" stop-index="153">
                    <left>
                        <function function-name="PREDICTION" start-index="50" stop-index="149"
                                  text="PREDICTION (nb_sh_clas_sample COST MODEL AUTO USING cust_marital_status, aeducation, household_size)"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="153" stop-index="153"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>
    <select sql-case-id="select_prediction_details_function">
        <projections start-index="7" stop-index="51">
            <expression-projection start-index="7" stop-index="51" text="PREDICTION_DETAILS(DT_SH_Clas_sample using *)">
                <function function-name="PREDICTION_DETAILS" start-index="7" stop-index="51"
                          text="PREDICTION_DETAILS(DT_SH_Clas_sample using *)"/>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="mining_data_apply_v" start-index="58" stop-index="76"/>
        </from>
        <where start-index="78" stop-index="105">
            <expr>
                <binary-operation-expression start-index="84" stop-index="105">
                    <left>
                        <column start-index="84" stop-index="93" name="occupation"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="TechSup" start-index="97" stop-index="105"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_predict_by_function">
        <projections start-index="7" stop-index="56">
            <expression-projection start-index="7" stop-index="56" text="PREDICT BY point_kmeans (FEATURES position)"
                                   alias="pos"/>
        </projections>
        <from>
            <subquery-table start-index="63" stop-index="96">
                <subquery>
                    <select>
                        <projections start-index="71" stop-index="71">
                            <shorthand-projection start-index="71" stop-index="71"/>
                        </projections>
                        <from>
                            <simple-table name="kmeans_2d" start-index="78" stop-index="86"/>
                        </from>
                        <limit start-index="88" stop-index="95">
                            <row-count start-index="94" stop-index="95" value="10"/>
                        </limit>
                    </select>
                </subquery>
            </subquery-table>
        </from>
    </select>

    <select sql-case-id="select_inet_function">
        <projections start-index="7" stop-index="55">
            <expression-projection start-index="7" stop-index="55" alias="RESULT"
                                   text="inet '192.168.1.5' = inet '192.168.1.5'">
                <binary-operation-expression start-index="7" stop-index="47">
                    <left>
                        <function function-name="inet" start-index="7" stop-index="27" text="inet '192.168.1.5'"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <function function-name="inet" start-index="30" stop-index="47" text="inet '192.168.1.5'"/>
                    </right>
                </binary-operation-expression>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_inet_function_with_inet_operator">
        <projections start-index="7" stop-index="57">
            <expression-projection start-index="7" stop-index="57" alias="RESULT"
                                   text="inet '192.168.1.5' &lt;&lt;= inet '192.168.1.5'">
                <binary-operation-expression start-index="7" stop-index="49">
                    <left>
                        <function function-name="inet" start-index="7" stop-index="27" text="inet '192.168.1.5'"/>
                    </left>
                    <operator>&lt;&lt;=</operator>
                    <right>
                        <function function-name="inet" start-index="32" stop-index="49" text="inet '192.168.1.5'"/>
                    </right>
                </binary-operation-expression>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_ts_rewrite_function">
        <projections start-index="7" stop-index="62">
            <expression-projection start-index="7" stop-index="62"
                                   text="ts_rewrite('a &amp; b'::tsquery, 'a'::tsquery, 'c'::tsquery)">
                <function start-index="7" stop-index="62" function-name="ts_rewrite"
                          text="ts_rewrite('a &amp; b'::tsquery, 'a'::tsquery, 'c'::tsquery)"/>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_int_2_function">
        <projections start-index="7" stop-index="16">
            <expression-projection start-index="7" stop-index="16" text="int2(25.3)">
                <function start-index="7" stop-index="16" text="int2(25.3);" function-name="int2"/>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_elem_contained_by_range_function">
        <projections start-index="7" stop-index="53">
            <expression-projection start-index="7" stop-index="53"
                                   text="elem_contained_by_range('2', numrange(1.1,2.2))">
                <function start-index="7" stop-index="53" function-name="elem_contained_by_range"
                          text="elem_contained_by_range('2', numrange(1.1,2.2))"/>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_int8range">
        <projections start-index="7" stop-index="50">
            <expression-projection start-index="7" stop-index="50" text="int8range(5,15) * int8range(10,20)"
                                   alias="RESULT">
                <binary-operation-expression start-index="7" stop-index="40">
                    <left>
                        <function start-index="7" stop-index="22" function-name="int8range" text="int8range(5,15)"/>
                    </left>
                    <operator>*</operator>
                    <right>
                        <function start-index="23" stop-index="40" function-name="int8range" text="int8range(10,20)"/>
                    </right>
                </binary-operation-expression>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_int4range">
        <projections start-index="7" stop-index="48">
            <expression-projection start-index="7" stop-index="48" text="int4range(2,4) &lt;@ int4range(1,7)"
                                   alias="RESULT">
                <binary-operation-expression start-index="7" stop-index="38">
                    <left>
                        <function start-index="7" stop-index="21" function-name="int4range" text="int4range(2,4)"/>
                    </left>
                    <operator>&lt;@</operator>
                    <right>
                        <function start-index="23" stop-index="37" function-name="int4range" text="int4range(1,7)"/>
                    </right>
                </binary-operation-expression>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_lower_inf_function">
        <projections start-index="7" stop-index="43">
            <expression-projection start-index="7" stop-index="43" text="lower_inf('(,)'::daterange)" alias="RESULT">
                <function start-index="7" stop-index="35" function-name="lower_inf" text="lower_inf('(,)'::daterange)"/>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_tsquery">
        <projections start-index="7" stop-index="24">
            <expression-projection start-index="7" stop-index="24" text="'super:*'::tsquery">
                <binary-operation-expression start-index="7" stop-index="24">
                    <left>
                        <literal-expression start-index="7" stop-index="15" value="super:*"/>
                    </left>
                    <operator>::</operator>
                    <right>
                        <column start-index="17" stop-index="24" name="tsquery"/>
                    </right>
                </binary-operation-expression>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_abbrev_function">
        <projections start-index="7" stop-index="42">
            <expression-projection start-index="7" stop-index="42" text="abbrev(cidr '10.1.0.0/16')" alias="RESULT">
                <function start-index="7" stop-index="42" function-name="abbrev" text="abbrev(cidr '10.1.0.0/16')"
                          alias="RESULT"/>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_set_masklen_function">
        <projections start-index="7" stop-index="55">
            <expression-projection start-index="7" stop-index="55" text="set_masklen('192.168.1.0/24'::cidr, 16)"
                                   alias="RESULT">
                <function start-index="7" stop-index="55" function-name="set_masklen"
                          text="set_masklen('192.168.1.0/24'::cidr, 16)" alias="RESULT"/>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_text_inet_function">
        <projections start-index="7" stop-index="40">
            <expression-projection start-index="7" stop-index="40" text="text(inet '192.168.1.5')" alias="RESULT">
                <function start-index="7" stop-index="40" function-name="text" text="text(inet '192.168.1.5')"
                          alias="RESULT"/>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_trunc_function">
        <projections start-index="7" stop-index="50">
            <expression-projection start-index="7" stop-index="50" text="trunc(macaddr '12:34:56:78:90:ab')"
                                   alias="RESULT">
                <function start-index="7" stop-index="50" function-name="trunc"
                          text="trunc(macaddr '12:34:56:78:90:ab')" alias="RESULT"/>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_case_when_from_sys">
        <from start-index="651" stop-index="681">
            <simple-table name="database_files" start-index="651" stop-index="681" alias="df">
                <owner name="sys" start-index="658" stop-index="660">
                    <owner name="tempdb" start-index="651" stop-index="656"/>
                </owner>
            </simple-table>
        </from>
        <projections start-index="7" stop-index="644">
            <column-projection name="name" start-index="7" stop-index="24" alias="FileName">
                <owner name="df" start-index="18" stop-index="19"/>
            </column-projection>
            <expression-projection text="df.size*1.0/128" start-index="27" stop-index="64" alias="current_file_size_MB">
                <expr>
                    <binary-operation-expression start-index="50" stop-index="64">
                        <left>
                            <binary-operation-expression start-index="50" stop-index="60">
                                <left>
                                    <column name="size" start-index="50" stop-index="56">
                                        <owner name="df" start-index="50" stop-index="51"/>
                                    </column>
                                </left>
                                <right>
                                    <literal-expression value="1.0" start-index="58" stop-index="60"/>
                                </right>
                                <operator>*</operator>
                            </binary-operation-expression>
                        </left>
                        <right>
                            <literal-expression value="128" start-index="62" stop-index="64"/>
                        </right>
                        <operator>/</operator>
                    </binary-operation-expression>
                </expr>
            </expression-projection>
            <expression-projection start-index="67" stop-index="212" alias="max_size"
                                   text="CASEdf.max_sizeWHEN0THEN'Autogrowth is off.'WHEN-1THEN'Autogrowth is on.'ELSE'Log file grows to a maximum size of 2 TB.'END">
                <expr>
                    <common-expression
                            literal-text="CASEdf.max_sizeWHEN0THEN'Autogrowth is off.'WHEN-1THEN'Autogrowth is on.'ELSE'Log file grows to a maximum size of 2 TB.'END"
                            start-index="78" stop-index="212"/>
                </expr>
            </expression-projection>
            <expression-projection start-index="215" stop-index="407" alias="growth_value"
                                   text="CASEWHENdf.growth=0THENdf.growthWHENdf.growth>0ANDdf.is_percent_growth=0THENdf.growth*1.0/128.0WHENdf.growth>0ANDdf.is_percent_growth=1THENdf.growthEND">
                <expr>
                    <common-expression
                            literal-text="CASEWHENdf.growth=0THENdf.growthWHENdf.growth>0ANDdf.is_percent_growth=0THENdf.growth*1.0/128.0WHENdf.growth>0ANDdf.is_percent_growth=1THENdf.growthEND"
                            start-index="230" stop-index="407"/>
                </expr>
            </expression-projection>
            <expression-projection start-index="410" stop-index="644" alias="growth_increment_unit"
                                   text="CASEWHENdf.growth=0THEN'Size is fixed.'WHENdf.growth>0ANDdf.is_percent_growth=0THEN'Growth value is MB.'WHENdf.growth>0ANDdf.is_percent_growth=1THEN'Growth value is a percentage.'END">
                <expr>
                    <common-expression
                            literal-text="CASEWHENdf.growth=0THEN'Size is fixed.'WHENdf.growth>0ANDdf.is_percent_growth=0THEN'Growth value is MB.'WHENdf.growth>0ANDdf.is_percent_growth=1THEN'Growth value is a percentage.'END"
                            start-index="434" stop-index="644"/>
                </expr>
            </expression-projection>
        </projections>
    </select>
</sql-parser-test-cases>
