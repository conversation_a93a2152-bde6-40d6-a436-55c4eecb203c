package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.definition;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.column.ExpectedColumn;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected constraint definition.
 */
@Getter
@Setter
public final class ExpectedConstraintDefinition extends AbstractExpectedSQLSegment {

    @XmlElement(name = "referenced-table")
    private ExpectedSimpleTable referencedTable;

    @XmlElement(name = "primary-key-column")
    private final List<ExpectedColumn> primaryKeyColumns = new LinkedList<>();

    @XmlElement(name = "index-column")
    private final List<ExpectedColumn> indexColumns = new LinkedList<>();

    @XmlAttribute(name = "constraint-name")
    private String constraintName;

    @XmlAttribute(name = "index-name")
    private String indexName;
}
