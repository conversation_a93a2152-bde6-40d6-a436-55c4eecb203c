package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.hint;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected with table hint clause.
 **/
@Getter
@Setter
public final class ExpectedWithTableHintClause extends AbstractExpectedSQLSegment {

    @XmlElement(name = "table-hint")
    private final List<ExpectedTableHint> tableHint = new LinkedList<>();
}
