package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl;

import lombok.Getter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.column.ExpectedColumn;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Grant statement test case.
 */
@Getter
public final class GrantStatementTestCase extends SQLParserTestCase {

    @XmlElement(name = "table")
    private final List<ExpectedSimpleTable> tables = new LinkedList<>();

    @XmlElement(name = "column")
    private final List<ExpectedColumn> columns = new LinkedList<>();
}
