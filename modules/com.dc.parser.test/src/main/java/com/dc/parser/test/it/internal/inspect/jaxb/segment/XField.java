package com.dc.parser.test.it.internal.inspect.jaxb.segment;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;

@XmlAccessorType(XmlAccessType.FIELD)
@Getter
@Setter
public class XField {

    @XmlAttribute
    private String name;        //columnName

    @XmlAttribute
    private String type;        //INT

    @XmlAttribute
    private Boolean primaryKey; // is PK

    @XmlAttribute
    private String comment;

}
