package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedDelimiterSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.ExpectedExpression;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

@Getter
@Setter
public final class ExpectedCollectionTable extends AbstractExpectedDelimiterSQLSegment {

    @XmlAttribute
    private String alias;

    @XmlElement
    private ExpectedExpression expectedExpression;
}
