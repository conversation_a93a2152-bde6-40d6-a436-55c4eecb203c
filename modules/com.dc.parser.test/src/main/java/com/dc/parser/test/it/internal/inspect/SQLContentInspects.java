package com.dc.parser.test.it.internal.inspect;

import com.dc.parser.test.it.internal.inspect.jaxb.SQLContentInspect;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
 * SQL inspect
 */
@RequiredArgsConstructor
public final class SQLContentInspects {

    private final Map<String, SQLContentInspect> inspects;

    /**
     * Get SQL inspect
     *
     * @param caseId SQL case ID
     * @return got case
     */
    public SQLContentInspect get(final String caseId) {
        //TODO Preconditions.checkState(sample.containsKey(caseId), "Can not find Sample of ID: %s.", caseId);
        return inspects.get(caseId);
    }
}
