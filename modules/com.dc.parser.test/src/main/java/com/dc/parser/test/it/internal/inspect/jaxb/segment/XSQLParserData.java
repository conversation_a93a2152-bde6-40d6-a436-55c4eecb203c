package com.dc.parser.test.it.internal.inspect.jaxb.segment;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@Getter
@Setter
public class XSQLParserData extends XAction {

    @XmlElement(name = "token")   //这里使用 name="token" XML： <token /> 元素
    private List<XToken> tokens;

    @XmlElement
    private XAction action;

}
