package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.top;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.ExpectedProjection;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;

/**
 * Expected top projection.
 */
@Getter
@Setter
public final class ExpectedTopProjection extends AbstractExpectedSQLSegment implements ExpectedProjection {

    @XmlAttribute
    private String alias;

    @XmlElement(name = "top-value")
    private ExpectedTopValue topValue;
}
