package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.set;

import lombok.Getter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.assignment.ExpectedAssignment;

import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected set clause.
 */
@Getter
public final class ExpectedSetClause extends AbstractExpectedSQLSegment {

    @XmlElement(name = "assignment")
    private final List<ExpectedAssignment> assignments = new LinkedList<>();
}
