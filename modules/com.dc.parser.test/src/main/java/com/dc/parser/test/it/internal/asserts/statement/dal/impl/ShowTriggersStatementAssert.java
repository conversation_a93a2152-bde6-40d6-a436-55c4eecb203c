package com.dc.parser.test.it.internal.asserts.statement.dal.impl;

import com.dc.parser.model.statement.dal.ShowTriggersStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.database.DatabaseAssert;
import com.dc.parser.test.it.internal.asserts.segment.show.ShowFilterAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal.ShowTriggersStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Show triggers statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ShowTriggersStatementAssert {

    /**
     * Assert show triggers statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual show triggers statement
     * @param expected      expected show tables statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ShowTriggersStatement actual, final ShowTriggersStatementTestCase expected) {
        if (actual.getFromDatabase().isPresent()) {
            DatabaseAssert.assertIs(assertContext, actual.getFromDatabase().get().getDatabase(), expected.getFromDatabase().getDatabase());
            SQLSegmentAssert.assertIs(assertContext, actual.getFromDatabase().get(), expected.getFromDatabase());
        }
        if (actual.getFilter().isPresent()) {
            ShowFilterAssert.assertIs(assertContext, actual.getFilter().get(), expected.getFilter());
        }
    }
}
