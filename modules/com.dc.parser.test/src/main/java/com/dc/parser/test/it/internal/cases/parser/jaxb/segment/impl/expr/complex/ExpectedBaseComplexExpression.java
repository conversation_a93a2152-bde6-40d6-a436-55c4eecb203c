package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.complex;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Expected base complex expression.
 */
@Getter
@Setter
public abstract class ExpectedBaseComplexExpression extends AbstractExpectedSQLSegment implements ExpectedComplexExpressionSegment {

    @XmlAttribute
    private String text;
}
