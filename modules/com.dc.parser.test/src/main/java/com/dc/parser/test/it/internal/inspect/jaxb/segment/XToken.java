package com.dc.parser.test.it.internal.inspect.jaxb.segment;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;

@XmlAccessorType(XmlAccessType.FIELD)
@Getter
@Setter
public class XToken {

    @XmlAttribute
    private String catalog;

    @XmlAttribute
    private String framework;

    @XmlAttribute
    private String schema;

    @XmlAttribute
    private String name;

    @XmlAttribute
    private String type;

    @XmlAttribute
    private String operation;

}
