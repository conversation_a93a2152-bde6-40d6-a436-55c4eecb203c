package com.dc.parser.test.it.internal.asserts.statement.ddl.impl;

import com.dc.parser.model.statement.ddl.AlterSystemStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.ddl.AlterSystemStatementTestCase;

/**
 * Alter system statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AlterSystemStatementAssert {

    /**
     * Assert alter system statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual alter system statement
     * @param expected      expected alter system statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final AlterSystemStatement actual, final AlterSystemStatementTestCase expected) {
    }
}
