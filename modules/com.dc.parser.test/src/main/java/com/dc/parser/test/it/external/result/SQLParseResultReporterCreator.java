package com.dc.parser.test.it.external.result;


import com.dc.infra.spi.TypedSPI;
import com.dc.infra.spi.annotation.SingletonSPI;

/**
 * SQL parse result reporter creator.
 */
@SingletonSPI
public interface SQLParseResultReporterCreator extends TypedSPI {

    /**
     * Create SQL parse result reporter.
     *
     * @param databaseType database type
     * @param resultPath   result path
     * @return created SQL parse result reporter
     */
    SQLParseResultReporter create(String databaseType, String resultPath);
}
