package com.dc.parser.test.it.internal.asserts.statement.dcl.impl;

import com.dc.parser.model.statement.dcl.AlterUserStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.AlterUserStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Alter user statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AlterUserStatementAssert {

    /**
     * Assert alter user statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual alter user statement
     * @param expected      expected alter user statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final AlterUserStatement actual, final AlterUserStatementTestCase expected) {
    }
}
