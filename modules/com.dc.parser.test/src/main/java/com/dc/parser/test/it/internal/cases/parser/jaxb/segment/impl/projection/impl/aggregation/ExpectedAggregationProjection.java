package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.impl.aggregation;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.expr.ExpectedExpression;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.projection.ExpectedProjection;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import java.util.LinkedList;
import java.util.List;

/**
 * Expected aggregation projection.
 */
@Getter
@Setter
public class ExpectedAggregationProjection extends AbstractExpectedSQLSegment implements ExpectedProjection {

    @XmlAttribute
    private String type;

    @XmlAttribute
    private String expression;

    @XmlAttribute
    private String alias;

    @XmlAttribute
    private String separator;

    @XmlElement(name = "parameters")
    private final List<ExpectedExpression> parameters = new LinkedList<>();
}
