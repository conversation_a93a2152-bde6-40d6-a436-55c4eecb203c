package com.dc.parser.test.it.internal.asserts.statement.dml.impl;

import com.dc.parser.model.statement.dml.LoadXMLStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.LoadXMLStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Load xml statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class LoadXMLStatementAssert {

    /**
     * Assert load xml statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual load xml statement
     * @param expected      expected load xml statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final LoadXMLStatement actual, final LoadXMLStatementTestCase expected) {
        assertTable(assertContext, actual, expected);
    }

    private static void assertTable(final SQLCaseAssertContext assertContext, final LoadXMLStatement actual, final LoadXMLStatementTestCase expected) {
        if (null == expected.getTable()) {
            assertNull(actual.getTableSegment(), assertContext.getText("Actual table should not exist."));
        } else {
            TableAssert.assertIs(assertContext, actual.getTableSegment(), expected.getTable());
        }
    }
}
