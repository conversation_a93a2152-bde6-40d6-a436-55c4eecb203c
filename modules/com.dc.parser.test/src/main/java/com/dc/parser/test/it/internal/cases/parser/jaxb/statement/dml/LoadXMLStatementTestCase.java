package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.table.ExpectedSimpleTable;

import javax.xml.bind.annotation.XmlElement;

/**
 * Load xml statement test case.
 */
@Getter
@Setter
public final class LoadXMLStatementTestCase extends SQLParserTestCase {

    @XmlElement
    private ExpectedSimpleTable table;
}
