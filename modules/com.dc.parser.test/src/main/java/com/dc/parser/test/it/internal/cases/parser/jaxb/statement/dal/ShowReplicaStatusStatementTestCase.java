package com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dal;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.SQLParserTestCase;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Show peplica status statement test case.
 */
@Getter
@Setter
public final class ShowReplicaStatusStatementTestCase extends SQLParserTestCase {

    @XmlAttribute
    private String channel;
}
