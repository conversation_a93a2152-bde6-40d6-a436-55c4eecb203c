package com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.call;

import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.AbstractExpectedSQLSegment;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAttribute;

/**
 * Expected procedure name.
 */
@Getter
@Setter
public final class ExpectedProcedureName extends AbstractExpectedSQLSegment {

    @XmlAttribute
    private String name;
}
