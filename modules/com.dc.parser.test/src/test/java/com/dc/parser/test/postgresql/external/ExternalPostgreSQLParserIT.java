package com.dc.parser.test.postgresql.external;

import com.dc.parser.test.it.external.ExternalSQLParserIT;
import com.dc.parser.test.it.external.loader.StandardExternalTestParameterLoadTemplate;
import com.dc.parser.test.it.loader.ExternalCaseSettings;

@ExternalCaseSettings(value = "PostgreSQL", caseURL = ExternalPostgreSQLParserIT.CASE_URL, resultURL = ExternalPostgreSQLParserIT.RESULT_URL,
        template = StandardExternalTestParameterLoadTemplate.class)
class ExternalPostgreSQLParserIT extends ExternalSQLParserIT {

    static final String CASE_URL = "https://github.com/postgres/postgres/tree/master/src/test/regress/sql";

    static final String RESULT_URL = "https://github.com/postgres/postgres/tree/master/src/test/regress/expected";
}
