package com.dc.parser.ext.mssql.statement.ddl;

import com.dc.parser.ext.mssql.statement.SQLServerStatement;
import com.dc.parser.model.segment.ddl.routine.FunctionNameSegment;
import com.dc.parser.model.statement.ddl.ExecuteStatement;
import lombok.Setter;

/**
 * SQLServer execute statement.
 */
public final class SQLServerExecuteStatement extends ExecuteStatement implements SQLServerStatement {

    @Setter
    private FunctionNameSegment procedureName;
}
