package com.dc.parser.ext.db2.segment;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@RequiredArgsConstructor
public class ParamSegment implements SQLSegment {

    private final int startIndex;

    private final int stopIndex;

    private IdentifierValue paramName;

    private Db2DataTypeSegment dataType;

}
