package com.dc.parser.ext.db2.statement.ddl;

import com.dc.parser.ext.db2.statement.Db2Statement;
import com.dc.parser.model.statement.ddl.CreateSecurityPolicyStatement;
import com.dc.parser.model.value.collection.CollectionValue;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;


@Setter
@Getter
public class Db2CreateSecurityPolicyStatement extends CreateSecurityPolicyStatement implements Db2Statement {

    private IdentifierValue policyName;

    private CollectionValue<IdentifierValue> componentNames;
}
