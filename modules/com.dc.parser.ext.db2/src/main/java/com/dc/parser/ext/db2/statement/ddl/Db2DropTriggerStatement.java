package com.dc.parser.ext.db2.statement.ddl;

import com.dc.parser.ext.db2.statement.Db2Statement;
import com.dc.parser.model.segment.ddl.trigger.TriggerNameSegment;
import com.dc.parser.model.statement.ddl.DropTriggerStatement;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@RequiredArgsConstructor
public class Db2DropTriggerStatement extends DropTriggerStatement implements Db2Statement {

    private final TriggerNameSegment triggerName;

}
