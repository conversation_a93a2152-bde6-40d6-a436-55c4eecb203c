package com.dc.parser.rewrite.sql.token.common.pojo.generic;

import com.dc.infra.database.DialectDatabaseMetaData;
import com.dc.infra.database.enums.QuoteCharacter;
import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.database.type.DatabaseTypeRegistry;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.rewrite.sql.token.common.pojo.SQLToken;
import com.dc.parser.rewrite.sql.token.common.pojo.Substitutable;
import lombok.Getter;

import java.util.Collection;

/**
 * Substitutable column name token.
 */
public final class SubstitutableColumnNameToken extends SQLToken implements Substitutable {

    private static final String COLUMN_NAME_SPLITTER = ", ";

    @Getter
    private final int stopIndex;

    @Getter
    private final Collection<ProjectionSegment> projections;

    @Getter
    private final DatabaseType databaseType;

    public SubstitutableColumnNameToken(final int startIndex, final int stopIndex, final Collection<ProjectionSegment> projections, final DatabaseType databaseType) {
        super(startIndex);
        this.stopIndex = stopIndex;
        this.projections = projections;
        this.databaseType = databaseType;
    }

    @Override
    public String toString() {
        DialectDatabaseMetaData dialectDatabaseMetaData = new DatabaseTypeRegistry(databaseType).getDialectDatabaseMetaData();
        QuoteCharacter quoteCharacter = dialectDatabaseMetaData.getQuoteCharacter();
        StringBuilder result = new StringBuilder();
        int index = 0;
        for (ProjectionSegment each : projections) {
            if (index > 0) {
                result.append(COLUMN_NAME_SPLITTER);
            }
            result.append(quoteCharacter.wrap(each.getColumnLabel()));
            index++;
        }
        return result.toString();
    }
}
