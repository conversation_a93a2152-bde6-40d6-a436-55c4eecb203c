package com.dc.parser.rewrite.sql.token.common.generator.builder;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.rewrite.config.RuleConfiguration;
import com.dc.parser.rewrite.sql.token.common.generator.SQLTokenGenerator;
import com.dc.parser.rewrite.sql.token.common.generator.oracle.OracleSelectStarTokenGenerator;
import lombok.RequiredArgsConstructor;

import java.util.Collection;
import java.util.LinkedList;

/**
 * 自定义SQL Token生成器构建器
 * <p>
 * 负责根据CustomRuleConfiguration和SQLStatementContext来决定需要实例化哪些自定义的SQLTokenGenerator。
 */
@RequiredArgsConstructor
public final class CustomSQLTokenGeneratorBuilder implements SQLTokenGeneratorBuilder {

    private final SQLStatementContext sqlStatementContext;
    private final RuleConfiguration ruleConfiguration;

    @Override
    public Collection<SQLTokenGenerator> getSQLTokenGenerators() {
        Collection<SQLTokenGenerator> result = new LinkedList<>();

        // 根据配置和上下文决定是否添加Oracle SELECT * Token生成器
        if (ruleConfiguration.isEnableOracleSelectStarRewrite()) {
            addSQLTokenGenerator(result, new OracleSelectStarTokenGenerator());
        }

        // 未来可以在这里添加更多的Token生成器
        // if (ruleConfiguration.isEnableOtherRewrite()) {
        //     addSQLTokenGenerator(result, new OtherTokenGenerator());
        // }

        return result;
    }

    /**
     * 添加SQL Token生成器到集合中
     * <p>
     * 只有当生成器的isGenerateSQLToken方法返回true时，才会被添加到结果集合中。
     */
    private void addSQLTokenGenerator(final Collection<SQLTokenGenerator> sqlTokenGenerators,
                                      final SQLTokenGenerator toBeAddedSQLTokenGenerator) {
        if (toBeAddedSQLTokenGenerator.isGenerateSQLToken(sqlStatementContext)) {
            sqlTokenGenerators.add(toBeAddedSQLTokenGenerator);
        }
    }
}
