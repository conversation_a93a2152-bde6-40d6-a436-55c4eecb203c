
package com.dc.summer.model.sql.generator;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPScriptObject;
import com.dc.summer.model.DBPScriptObjectExt;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLConstants;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBStructUtils;
import com.dc.summer.model.struct.rdb.DBSTable;
import com.dc.utils.CommonUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SQLGeneratorDDL extends SQLGenerator<DBPScriptObject> {

    @Override
    public boolean isDDLOption() {
        return true;
    }

    @Override
    public void run(DBRProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
        boolean allTables = true;
        List<DBSTable> tableList = new ArrayList<>();
        for (DBPScriptObject object : objects) {
            if (!(object instanceof DBSTable)) {
                allTables = false;
                break;
            } else {
                tableList.add((DBSTable) object);
            }
        }
        if (!allTables) {
            super.run(monitor);
            return;
        }

        StringBuilder sql = new StringBuilder(100);
        Map<String, Object> options = new HashMap<>();
        addOptions(options);
        try {
            DBStructUtils.generateTableListDDL(monitor, sql, tableList, options, false);
        } catch (DBException e) {
            throw new InvocationTargetException(e);
        }
        setResult(sql.toString().trim());
    }

    @Override
    public void generateSQL(DBRProgressMonitor monitor, StringBuilder sql, DBPScriptObject object) throws DBException {
        if (sql.length() > 0) {
            sql.append("\n");
        }
        Map<String, Object> options = new HashMap<>();
        options.put(DBPScriptObject.OPTION_REFRESH, true);
        addOptions(options);

        String definitionText = CommonUtils.notEmpty(object.getObjectDefinitionText(monitor, options)).trim();
        sql.append(definitionText);
        String delimiter = SQLConstants.DEFAULT_STATEMENT_DELIMITER;
        if (object instanceof DBSObject) {
            SQLDialect sqlDialect = ((DBSObject) object).getDataSource().getSQLDialect();
            delimiter =  SQLUtils.getDefaultScriptDelimiter(sqlDialect);
        }
        if (!definitionText.endsWith(delimiter)) {
            sql.append(SQLConstants.DEFAULT_STATEMENT_DELIMITER);
        }
        sql.append("\n");
        if (object instanceof DBPScriptObjectExt) {
            String extendedDefinitionText = ((DBPScriptObjectExt) object).getExtendedDefinitionText(monitor);
            if (CommonUtils.isNotEmpty(extendedDefinitionText)) {
                String definition2 = extendedDefinitionText.trim();
                sql.append("\n");
                sql.append(definition2);
                if (!definition2.endsWith(SQLConstants.DEFAULT_STATEMENT_DELIMITER)) {
                    sql.append(SQLConstants.DEFAULT_STATEMENT_DELIMITER);
                }
                sql.append("\n");
            }
        }
    }

    @Override
    protected void addOptions(Map<String, Object> options) {
        super.addOptions(options);
        options.put(DBPScriptObject.OPTION_REFRESH, true);
        options.put(DBPScriptObject.OPTION_INCLUDE_OBJECT_DROP, true);
    }
}
