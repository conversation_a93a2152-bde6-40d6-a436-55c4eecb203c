
package com.dc.summer.model.sql.generator;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.struct.rdb.DBSProcedure;
import com.dc.summer.model.struct.rdb.DBSProcedureParameter;
import com.dc.utils.CommonUtils;

import java.util.Collection;

public class SQLGeneratorProcedureCall extends SQLGeneratorProcedure {

    @Override
    protected void generateSQL(DBRProgressMonitor monitor, StringBuilder sql, DBSProcedure proc) throws DBException {
        Collection<? extends DBSProcedureParameter> parameters = proc.getParameters(monitor);
        DBPDataSource dataSource = proc.getDataSource();
        {
            SQLDialect sqlDialect = dataSource.getSQLDialect();
            sqlDialect.generateStoredProcedureCall(sql, proc, CommonUtils.safeCollection(parameters));
        }
    }
}
