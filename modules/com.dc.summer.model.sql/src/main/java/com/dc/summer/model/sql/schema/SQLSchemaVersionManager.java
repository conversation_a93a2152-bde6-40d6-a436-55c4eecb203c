

package com.dc.summer.model.sql.schema;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * SQL schema version manager.
 */
public interface SQLSchemaVersionManager {

    /**
     * Returns current schema version.
     * Returns -1 if schema doesn't exist
     */
    int getCurrentSchemaVersion(DBRProgressMonitor monitor, Connection connection, String schemaName) throws DBException, SQLException;

    /**
     * Updates current schema version
     */
    void updateCurrentSchemaVersion(DBRProgressMonitor monitor, Connection connection, String schemaName) throws DBException, SQLException;

    default void fillInitialSchemaData(DBRProgressMonitor monitor, Connection connection) throws DBException, SQLException {

    }

}
