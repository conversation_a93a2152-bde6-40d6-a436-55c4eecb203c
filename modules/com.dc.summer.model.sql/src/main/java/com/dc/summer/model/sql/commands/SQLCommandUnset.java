
package com.dc.summer.model.sql.commands;

import com.dc.summer.DBException;
import com.dc.summer.model.sql.SQLControlCommand;
import com.dc.summer.model.sql.SQLControlCommandHandler;
import com.dc.summer.model.sql.SQLScriptContext;

/**
 * Control command handler
 */
public class SQLCommandUnset implements SQLControlCommandHandler {

    @Override
    public boolean handleCommand(SQLControlCommand command, SQLScriptContext scriptContext) throws DBException {
        String varName = command.getParameter();
        scriptContext.removeVariable(varName);

        return true;
    }

}
