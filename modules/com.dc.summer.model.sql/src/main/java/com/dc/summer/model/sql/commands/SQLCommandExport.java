
package com.dc.summer.model.sql.commands;

import com.google.gson.Gson;
import com.dc.summer.DBException;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.sql.SQLControlCommand;
import com.dc.summer.model.sql.SQLControlCommandHandler;
import com.dc.summer.model.sql.SQLPragmaHandler;
import com.dc.summer.model.sql.SQLScriptContext;

import java.io.StringReader;
import java.util.Map;

/**
 * Control command handler
 */
public class SQLCommandExport implements SQLControlCommandHandler {

    @Override
    public boolean handleCommand(SQLControlCommand command, SQLScriptContext scriptContext) throws DBException {
        final Map<String, Object> params;

        try {
            params = JSONUtils.parseMap(new Gson(), new StringReader(command.getParameter()));
        } catch (Exception e) {
            throw new DBException("Invalid syntax. Use '@export {\"type\": <type>, \"producer\": {...}}, \"consumer\": {...}}, \"processor\": {...}}'", e);
        }

        scriptContext.setPragma(SQLPragmaHandler.PRAGMA_EXPORT, params);

        return true;
    }
}
