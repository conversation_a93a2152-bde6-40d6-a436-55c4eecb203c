

package com.dc.summer.model.sql.parser;

import com.dc.summer.ModelPreferences;
import com.dc.summer.model.DBPDataSource;
import org.eclipse.jface.text.IDocument;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLSyntaxManager;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.text.parser.TPRuleBasedScanner;

/**
 * Parser context
 */
public class SQLParserContext {

    @Nullable
    private final DBPDataSource dataSource;
    @NotNull
    private final SQLSyntaxManager syntaxManager;
    @NotNull
    private final SQLRuleManager ruleManager;
    @NotNull
    private final IDocument document;
    private TPRuleBasedScanner scanner;

    @Nullable
    private DBPPreferenceStore preferenceStore;

    public SQLParserContext(@Nullable DBPDataSource dataSource, @NotNull SQLSyntaxManager syntaxManager, @NotNull SQLRuleManager ruleManager, @NotNull IDocument document) {
        this.dataSource = dataSource;
        this.syntaxManager = syntaxManager;
        this.ruleManager = ruleManager;
        this.document = document;
    }

    @Nullable
    public DBPDataSource getDataSource() {
        return dataSource;
    }

    @NotNull
    public SQLSyntaxManager getSyntaxManager() {
        return syntaxManager;
    }

    @NotNull
    public SQLRuleManager getRuleManager() {
        return ruleManager;
    }

    @NotNull
    public IDocument getDocument() {
        return document;
    }

    public SQLDialect getDialect() {
        SQLDialect sqlDialect = syntaxManager.getDialect();
        if (sqlDialect != null) {
            return sqlDialect;
        }
        return SQLUtils.getDialectFromDataSource(getDataSource());
    }

    public TPRuleBasedScanner getScanner() {
        if (scanner == null) {
            scanner = new TPRuleBasedScanner();
            scanner.setRules(ruleManager.getAllRules());
        }
        return scanner;
    }

    public DBPPreferenceStore getPreferenceStore() {
        if (preferenceStore != null) {
            return preferenceStore;
        }
        DBPDataSource dataSource = getDataSource();
        return dataSource == null ?
            ModelPreferences.getPreferences() :
            dataSource.getContainer().getPreferenceStore();
    }

    public void setPreferenceStore(@Nullable DBPPreferenceStore preferenceStore) {
        this.preferenceStore = preferenceStore;
    }

    void startScriptEvaluation() {
        getScanner().startEval();
    }

    void endScriptEvaluation() {
        getScanner().endEval();
    }

}
