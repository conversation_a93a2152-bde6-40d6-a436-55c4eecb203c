<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>


<plugin>

    <extension-point id="com.dc.summer.service" name="%extension-point.com.dc.summer.service.name" schema="schema/com.dc.summer.service.exsd"/>
    <extension-point id="com.dc.summer.application" name="%extension-point.com.dc.summer.application.name" schema="schema/com.dc.summer.application.exsd"/>
    <extension-point id="com.dc.summer.expressions" name="%extension-point.com.dc.summer.expressions.name" schema="schema/com.dc.summer.expressions.exsd"/>
    <extension-point id="com.dc.summer.dataFormatter" name="%extension-point.com.dc.summer.dataFormatter.name" schema="schema/com.dc.summer.dataFormatter.exsd"/>
    <extension-point id="com.dc.summer.dataTypeProvider" name="%extension-point.com.dc.summer.dataTypeProvider.name" schema="schema/com.dc.summer.dataTypeProvider.exsd"/>
    <extension-point id="com.dc.summer.serialize" name="%extension-point.com.dc.summer.serialize.name" schema="schema/com.dc.summer.serialize.exsd"/>
    <extension-point id="com.dc.summer.navigator" name="%extension-point.com.dc.summer.navigator.name" schema="schema/com.dc.summer.navigator.exsd"/>

    <extension point="org.eclipse.core.resources.natures" id="com.dc.summer.DBeaverNature" name="%extension.com.dc.summer.DBeaverNature.name">
        <runtime>
            <run class="com.dc.summer.runtime.resource.DBeaverNature">
            </run>
        </runtime>
    </extension>

    <extension point="com.dc.summer.expressions">
        <namespace id="math" class="java.lang.Math" description="Math functions"/>
        <namespace id="content" class="com.dc.summer.registry.expressions.ContentExpressionFunctions" description="Content handling functions"/>
    </extension>

    <extension point="org.eclipse.core.filesystem.filesystems">
        <filesystem scheme="dbvfs">
            <run class="com.dc.summer.model.fs.nio.NIOFileSystem"/>
        </filesystem>
    </extension>

    <extension point="com.dc.summer.dataFormatter">
        <formatter id="number" label="%DateFormatter.number.label" class="com.dc.summer.model.impl.data.formatters.NumberDataFormatter" sampleClass="com.dc.summer.model.impl.data.formatters.NumberFormatSample">
            <propertyGroup label="%DateFormatter.number.general.label">
                <property id="useGrouping" label="%DateFormatter.number.general.grouping.label" type="boolean" description="%DateFormatter.number.general.grouping.description"/>
                <property id="groupingSize" label="%DateFormatter.number.general.groupingSize.label" type="integer" description="%DateFormatter.number.general.groupingSize.description"/>
                <property id="maxIntegerDigits" label="%DateFormatter.number.general.maxIntDigits.label" type="integer" description="%DateFormatter.number.general.maxIntDigits.description"/>
                <property id="minIntegerDigits" label="%DateFormatter.number.general.minIntDigits.label" type="integer" description="%DateFormatter.number.general.minIntDigits.description"/>
                <property id="maxFractionDigits" label="%DateFormatter.number.general.maxFractDigits.label" type="integer" description="%DateFormatter.number.general.maxFractDigits.description"/>
                <property id="minFractionDigits" label="%DateFormatter.number.general.minFractDigits.label" type="integer" description="%DateFormatter.number.general.minFractDigits.description"/>
                <property id="useTypeScale" label="%DateFormatter.number.general.useTypeScale.label" type="boolean" description="%DateFormatter.number.general.useTypeScale.description"/>
                <property id="roundingMode" label="%DateFormatter.number.general.roundingMode.label" type="string" description="%DateFormatter.number.general.roundingMode.description" validValues="UP,DOWN,CEILING,FLOOR,HALF_UP,HALF_DOWN,HALF_EVEN,UNNECESSARY"/>
            </propertyGroup>
        </formatter>
        <formatter id="timestamp" label="%DateFormatter.timestamp.label" class="com.dc.summer.model.impl.data.formatters.DateTimeDataFormatter" sampleClass="com.dc.summer.model.impl.data.formatters.TimestampFormatSample">
            <propertyGroup label="%DateFormatter.timestamp.general.label">
                <property id="pattern" label="%DateFormatter.timestamp.general.pattern.label" type="string" description="%DateFormatter.timestamp.general.pattern.description" required="true"/>
                <property id="timezone" label="%DateFormatter.timestamp.general.timezone.label" type="string" description="%DateFormatter.timestamp.general.timezone.description" required="false"/>
            </propertyGroup>
        </formatter>
        <formatter id="timestamptz" label="%DateFormatter.timestamptz.label" class="com.dc.summer.model.impl.data.formatters.DateTimeDataFormatter" sampleClass="com.dc.summer.model.impl.data.formatters.TimestampTzFormatSample">
            <propertyGroup label="%DateFormatter.timestamp.general.label">
                <property id="pattern" label="%DateFormatter.timestamp.general.pattern.label" type="string" description="%DateFormatter.timestamp.general.pattern.description" required="true"/>
            </propertyGroup>
        </formatter>
        <formatter id="date" label="%DateFormatter.date.label" class="com.dc.summer.model.impl.data.formatters.DateTimeDataFormatter" sampleClass="com.dc.summer.model.impl.data.formatters.DateFormatSample">
            <propertyGroup label="%DateFormatter.date.general.label">
                <property id="pattern" label="%DateFormatter.date.general.pattern.label" type="string" description="%DateFormatter.date.general.pattern.description" required="true"/>
            </propertyGroup>
        </formatter>
        <formatter id="time" label="%DateFormatter.time.label" class="com.dc.summer.model.impl.data.formatters.DateTimeDataFormatter" sampleClass="com.dc.summer.model.impl.data.formatters.TimeFormatSample">
            <propertyGroup label="%DateFormatter.time.general.label">
                <property id="pattern" label="%DateFormatter.time.general.pattern.label" type="string" description="%DateFormatter.time.general.pattern.description" required="true"/>
                <property id="timezone" label="%DateFormatter.time.general.timezone.label" type="string" description="%DateFormatter.time.general.timezone.description" required="false"/>
            </propertyGroup>
        </formatter>
        <formatter id="timetz" label="%DateFormatter.timetz.label" class="com.dc.summer.model.impl.data.formatters.DateTimeDataFormatter" sampleClass="com.dc.summer.model.impl.data.formatters.TimeTzFormatSample">
            <propertyGroup label="%DateFormatter.time.general.label">
                <property id="pattern" label="%DateFormatter.time.general.pattern.label" type="string" description="%DateFormatter.time.general.pattern.description" required="true"/>
            </propertyGroup>
        </formatter>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider class="com.dc.summer.model.impl.jdbc.data.handlers.JDBCStandardValueHandlerProvider" id="com.dc.summer.core.standardDataTypeProvider">
            <type standard="BIT"/>
            <type standard="TINYINT"/>
            <type standard="SMALLINT"/>
            <type standard="INTEGER"/>
            <type standard="BIGINT"/>
            <type standard="FLOAT"/>
            <type standard="REAL"/>
            <type standard="DOUBLE"/>
            <type standard="DECIMAL"/>
            <type standard="NUMERIC"/>
            <type standard="CHAR"/>
            <type standard="VARCHAR"/>
            <type standard="LONGVARCHAR"/>
            <type standard="DATE"/>
            <type standard="TIME"/>
            <type standard="TIMESTAMP"/>
            <type standard="BINARY"/>
            <type standard="VARBINARY"/>
            <type standard="LONGVARBINARY"/>
            <!--
            <type standard="NULL"/>
            <type standard="OTHER"/>
            -->

            <!--
            <type standard="JAVA_OBJECT"/>
            <type standard="DISTINCT"/>
            -->

            <type standard="STRUCT"/>
            <type standard="ARRAY"/>
            <type standard="REF"/>

            <type standard="BLOB"/>
            <type standard="CLOB"/>
            <!--
            <type standard="REF"/>
            <type standard="DATALINK"/>
            -->

            <type standard="BOOLEAN"/>
            <type standard="ROWID"/>
            <type standard="NCHAR"/>
            <type standard="NVARCHAR"/>
            <type standard="LONGNVARCHAR"/>
            <type standard="NCLOB"/>
            <type standard="SQLXML"/>

        </provider>

        <!-- Standard transformers (enabled by default) -->

        <transformer
                class="com.dc.summer.model.impl.data.transformers.ComplexTypeAttributeTransformer"
                id="com.dc.summer.core.ComplexTypeAttributeTransformer"
                name="Complex type"
                description="%ComplexTypeTransformer.general.description"
                applyByDefault="true">
            <type kind="STRUCT"/>
            <type kind="DOCUMENT"/>
            <!--<type kind="OBJECT"/>-->
        </transformer>
        <transformer
                class="com.dc.summer.model.impl.data.transformers.MapAttributeTransformer"
                id="com.dc.summer.core.MapAttributeTransformer"
                name="Map"
                description="%MapTransformer.general.description"
                applyByDefault="true">
            <type kind="STRUCT"/>
            <type kind="DOCUMENT"/>
            <!--<type kind="OBJECT"/>-->
            <type kind="ANY"/>
        </transformer>
        <transformer
                class="com.dc.summer.model.impl.data.transformers.ArrayAttributeTransformer"
                id="com.dc.summer.core.ArrayAttributeTransformer"
                name="Array"
                description="%ArrayTransformer.general.description"
                applyByDefault="true">
            <type kind="ARRAY"/>
        </transformer>

        <!-- Extra transformers -->
        <transformer
                class="com.dc.summer.model.impl.data.transformers.EpochTimeAttributeTransformer"
                id="com.dc.summer.core.EpochTimeAttributeTransformer"
                name="Epoch Time"
                description="%EpochTimeTransformer.general.description"
                applyByDefault="false"
                custom="true">
            <type kind="NUMERIC"/>
            <propertyGroup label="Properties">
                <property id="unit" label="Unit" type="string" description="%EpochTimeTransformer.property.unit.description" defaultValue="milliseconds" required="true" validValues="seconds,milliseconds,nanoseconds,dotnet,w32filetime,oadate,sqliteJulian"/>
                <property id="zoneId" label="Timezone ID" type="string" description="%EpochTimeTransformer.property.timezoneID.description"/>
            </propertyGroup>
        </transformer>
        <transformer
                class="com.dc.summer.model.impl.data.transformers.RadixAttributeTransformer"
                id="com.dc.summer.core.RadixAttributeTransformer"
                name="Number Radix"
                description="%RadixTransformer.general.description"
                applyByDefault="false"
                custom="true">
            <type kind="NUMERIC"/>
            <propertyGroup label="Properties">
                <property id="radix" label="Radix" type="string" description="Number radix" defaultValue="16" required="true" validValues="2,8,10,16,32"/>
                <property id="bits" label="Bits" type="integer" description="Visible least significant bits number (when radix is 2)" defaultValue="32" required="true"/>
                <property id="prefix" label="Show prefix" type="boolean" description="Shows radix prefix (0x for hex)" defaultValue="false" required="true"/>
                <property id="unsigned" label="Unsigned" type="boolean" description="Shows value without sign" defaultValue="false" required="true"/>
            </propertyGroup>
        </transformer>
        <transformer
                class="com.dc.summer.model.impl.data.transformers.URLAttributeTransformer"
                id="com.dc.summer.core.URLAttributeTransformer"
                name="URL"
                description="%URLTransformer.general.description"
                applyByDefault="false"
                custom="true">
            <type kind="NUMERIC"/>
            <type kind="STRING"/>
            <propertyGroup label="Properties">
                <property id="pattern" label="%URLTransformer.property.pattern.label" type="string" description="%URLTransformer.property.pattern.description" defaultValue="http://${value}" required="false"/>
            </propertyGroup>
        </transformer>
        <transformer
                class="com.dc.summer.model.impl.data.transformers.BooleanAttributeTransformer"
                id="boolean"
                name="Boolean"
                description="%BooleanTransformer.general.description"
                applyByDefault="false"
                custom="true">
            <type kind="NUMERIC"/>
        </transformer>
        <transformer
                class="com.dc.summer.model.impl.data.transformers.UUIDAttributeTransformer"
                id="com.dc.summer.core.UUIDAttributeTransformer"
                name="UUID"
                description="%UUIDTransformer.general.description"
                applyByDefault="false"
                custom="true">
            <type kind="BINARY"/>
            <type kind="CONTENT"/>
            <propertyGroup label="Properties">
                <property id="type" label="%UUIDTransformer.property.type.label" type="string" description="%UUIDTransformer.property.type.description" defaultValue="Version1" required="true" validValues="Version1,Version2,Ordered" allowCustomValues="false"/>
                <property id="case" label="%UUIDTransformer.property.case.label" type="string" description="%UUIDTransformer.property.case.description" defaultValue="Lower case" required="true" validValues="Lower case,Upper case" allowCustomValues="false"/>
            </propertyGroup>
        </transformer>
        <transformer
                class="com.dc.summer.model.impl.data.transformers.BinaryAttributeTransformer"
                id="com.dc.summer.core.BinaryAttributeTransformer"
                name="Binary"
                description="%BinaryTransformer.general.description"
                applyByDefault="false"
                custom="true">
            <type kind="STRING"/>
            <propertyGroup label="Properties">
                <property id="format" label="%BinaryTransformer.property.format.label" type="string" description="%BinaryTransformer.property.format.description" defaultValue="hex" required="false" validValues="hex,native,base64,string" allowCustomValues="false"/>
                <property id="encoding" label="%BinaryTransformer.property.encoding.label" type="string" description="%BinaryTransformer.property.encoding.description" defaultValue="utf-8" required="false"/>
            </propertyGroup>
        </transformer>
        <transformer
                class="com.dc.summer.model.impl.data.transformers.NumericAttributeTransformer"
                id="com.dc.summer.core.NumericAttributeTransformer"
                name="Numeric"
                description="Represents string value as number"
                applyByDefault="false"
                custom="true">
            <type kind="STRING"/>
            <propertyGroup label="Properties">
                <property id="type" type="string" label="%NumericTransformer.property.type.label" description="%NumericTransformer.property.type.description" defaultValue="double" required="true" validValues="byte,short,int,long,float,double" allowCustomValues="false"/>
                <property id="lenient" type="boolean" label="%NumericTransformer.property.lenient.label" description="%NumericTransformer.property.lenient.description" defaultValue="false" required="true"/>
            </propertyGroup>
        </transformer>

    </extension>

</plugin>
