
package com.dc.summer.model;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;

import java.util.Map;

/**
 * Configuration origin provider.
 */
public interface DBPDataSourceOriginProvider {

    @NotNull
    DBPDataSourceOrigin getOrigin(
        @NotNull Map<String, Object> dsConfiguration,
        @Nullable DBPExternalConfiguration externalConfiguration) throws DBException;

}
