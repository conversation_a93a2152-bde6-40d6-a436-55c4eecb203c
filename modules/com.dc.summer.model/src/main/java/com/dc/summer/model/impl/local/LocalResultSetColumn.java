

package com.dc.summer.model.impl.local;

import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCAttributeMetaData;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.exec.DBCEntityMetaData;
import com.dc.summer.model.meta.Property;

/**
 * LocalResultSetColumn
 */
public class LocalResultSetColumn implements DBCAttributeMetaData
{
    protected final DBCResultSet resultSet;
    private final int index;
    private final String label;
    private final DBPDataKind dataKind;
    private final DBSTypedObject typedObject;

    public LocalResultSetColumn(DBCResultSet resultSet, int index, String label, DBPDataKind dataKind)
    {
        this.resultSet = resultSet;
        this.index = index;
        this.label = label;
        this.dataKind = dataKind;
        this.typedObject = null;
    }

    public LocalResultSetColumn(DBCResultSet resultSet, int index, String label, DBSTypedObject typedObject)
    {
        this.resultSet = resultSet;
        this.index = index;
        this.label = label;
        this.dataKind = typedObject.getDataKind();
        this.typedObject = typedObject;
    }

    @Property(viewable = true, order = 1)
    @Override
    public int getOrdinalPosition()
    {
        return index;
    }

    @Nullable
    @Override
    public Object getSource() {
        return null;
    }

    @Property(viewable = true, order = 2)
    @NotNull
    @Override
    public String getLabel()
    {
        return label;
    }

    @Property(viewable = true, order = 3)
    @Nullable
    @Override
    public String getEntityName()
    {
        return null;
    }

    @Override
    public boolean isReadOnly()
    {
        return true;
    }

    @Nullable
    @Override
    public DBCEntityMetaData getEntityMetaData()
    {
        return null;
    }

    @Override
    public boolean isRequired()
    {
        return false;
    }

    @Property(viewable = true, order = 4)
    @Override
    public boolean isAutoGenerated() {
        return false;
    }

    @NotNull
    @Override
    public String getName()
    {
        return label;
    }

    @Property(viewable = true, order = 5)
    @Override
    public String getTypeName()
    {
        return typedObject == null ?
            DBUtils.getDefaultDataTypeName(resultSet.getSession().getDataSource(), dataKind) :
            typedObject.getTypeName();
    }

    @Override
    public String getFullTypeName() {
        return typedObject == null ? DBUtils.getFullTypeName(this) : typedObject.getFullTypeName();
    }

    @Override
    public int getTypeID()
    {
        return typedObject == null ? 0 : typedObject.getTypeID();
    }

    @Override
    public DBPDataKind getDataKind()
    {
        return dataKind;
    }

    @Override
    public Integer getScale()
    {
        return typedObject == null ? null : typedObject.getScale();
    }

    @Override
    public Integer getPrecision()
    {
        return typedObject == null ? null : typedObject.getPrecision();
    }

    @Override
    public long getMaxLength()
    {
        return typedObject == null ? 0 : typedObject.getMaxLength();
    }

    @Override
    public long getTypeModifiers() {
        return typedObject == null ? 0 : typedObject.getTypeModifiers();
    }

    @Override
    public String toString() {
        return getName();
    }
}
