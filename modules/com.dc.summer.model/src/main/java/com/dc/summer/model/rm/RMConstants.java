
package com.dc.summer.model.rm;

/**
 * RM constants
 */
public interface RMConstants  {
    String PERMISSION_PROJECT_CONNECTIONS_EDIT = "project-connection-edit";
    String PERMISSION_PROJECT_CONNECTIONS_VIEW = "project-connection-view";

    String PERMISSION_PROJECT_RESOURCE_VIEW = "project-resource-view";
    String PERMISSION_PROJECT_RESOURCE_EDIT = "project-resource-edit";

    String PERMISSION_PROJECT_ADMIN = "project-admin";

    // RM admin can create/delete projects. It also can assign project permissions.
    String PERMISSION_RM_ADMIN = "rm-admin";
}
