
package com.dc.summer.model.impl;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.sql.SQLQueryType;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.summer.model.DBPTransactionIsolation;
import com.dc.utils.Pair;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * AbstractDataSourceInfo
 */
public abstract class AbstractDataSourceInfo implements DBPDataSourceInfo
{
    @Override
    public boolean supportsTransactions() {
        return false;
    }

    @Override
    public boolean supportsTransactionsForDDL() {
        return supportsTransactions();
    }

    @Override
    public boolean supportsSavepoints() {
        return false;
    }

    @Override
    public boolean supportsReferentialIntegrity() {
        return false;
    }

    @Override
    public boolean supportsIndexes() {
        return false;
    }

    @Override
    public boolean supportsStoredCode() {
        return false;
    }

    @Override
    public Collection<DBPTransactionIsolation> getSupportedTransactionsIsolation() {
        return null;
    }

    @Override
    public boolean supportsResultSetLimit() {
        return false;
    }

    @Override
    public boolean supportsResultSetScroll() {
        return false;
    }

    @Override
    public boolean supportsResultSetOrdering() {
        return true;
    }

    @Override
    public boolean supportsNullableUniqueConstraints() {
        return false;
    }

    @Override
    public boolean isDynamicMetadata() {
        return false;
    }

    @Override
    public boolean supportsMultipleResults() {
        return false;
    }

    @Override
    public boolean isReadOnlyData()
    {
        return false;
    }

    @Override
    public boolean isReadOnlyMetaData()
    {
        return false;
    }

    @Override
    public Map<String, Object> getDatabaseProductDetails() {
        return null;
    }

    @Override
    public boolean isMultipleResultsFetchBroken() {
        return false;
    }

    @Override
    public DBSObjectType[] getSupportedObjectTypes() {
        return new DBSObjectType[0];
    }

    @Override
    public boolean needsTableMetaForColumnResolution() {
        return true;
    }

    @Override
    public boolean supportsBatchUpdates()
    {
        return false;
    }

    @Override
    public boolean supportsLocalTransaction() {
        return false;
    }

    @Override
    public String getFormatColumnName(String column) {
        return column;
    }

    @Override
    public boolean supportsDDLAutoCommit() {
        return true;
    }

    @Override
    public boolean isExecuteErrorTransactionInterrupted() {
        return false;
    }

    @Override
    public boolean isQueryStatementLockTable() {
        return false;
    }

    @Override
    public List<String> getBigDataColumnType() {
        return null;
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        return null;
    }

    @Override
    public String getSynonymSql(String schemaName, String synonym) {
        return null;
    }

    @Override
    public List<SQLQueryType> getBackupTypes() {
        return Arrays.asList(
                SQLQueryType.COMMIT,
                SQLQueryType.TRUNCATE,
                SQLQueryType.ROLLBACK);
    }

    @Override
    public List<SQLQueryType> getDmlTypes() {
        return Arrays.asList(
                SQLQueryType.UPDATE,
                SQLQueryType.DELETE,
                SQLQueryType.INSERT);
    }

    @Override
    public String getPrimaryKeySql(String schemaName, String tableName) {
        return null;
    }

    @Override
    public List<String> getPrimaryKeyColumns(List<Map<String, Object>> list) {
        return null;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {
        return null;
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {
        return null;
    }

    @Override
    public String getCheckTableExistenceSql(String name) {
        return null;
    }

    @Override
    public void setDatabaseVersion(boolean isDockerVersion) {

    }

    @Override
    public boolean isSupportsTransactionIsolation() {
        return true;
    }

    @Override
    public boolean isIntervalNeedQuotes() {
        return true;
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        //只有一个值的情况使用原有的生成单条的sql
        if (CollectionUtils.size(list) == 1) {
            return generateInsertSql(schemaName, tableName, list.get(0), null);
        }
        return generateInsertSqlBatch(schemaName, tableName, list, contents);
    }

    @Override
    public String generateInsertIgnoreSql(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        throw new UnsupportedOperationException("不支持生成 INSERT_IGNORE SQL");
    }

    @Override
    public String generateReplaceSql(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        throw new UnsupportedOperationException("不支持生成 REPLACE SQL");
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {
        return "";
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        return "";
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        if (StringUtils.isNotBlank(schemaName)) {
            return String.format("TRUNCATE TABLE %s.%s", schemaName, tableName);
        } else {
            return String.format("TRUNCATE TABLE %s", tableName);
        }
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {
        return List.of();
    }

    @Override
    public boolean isSupportsPrepareStatement(DBPDataSource dataSource) {
        return true;
    }

    @Override
    public boolean isRepeatProperties(DBPConnectionConfiguration configuration) {
        Map<String, String> properties = configuration.getProperties();
        return List.of("username", "password").stream().anyMatch(s -> properties.containsKey(s));
    }

    @Override
    public boolean supportsSetNetworkTimeout() {
        return true;
    }

    @Override
    public String getFunctionOrProcedureOid(String objectName, String schemaName, List<String> args) {
        return null;
    }

    @Override
    public boolean isSupportsBooleanQuoted() {
        return true;
    }

    @Override
    public boolean isNeedAppendColumnType() {
        return false;
    }

    @Override
    public boolean isSystemSchema(String name) {
        return false;
    }

    @Override
    public boolean showInteger(DBDAttributeBinding column) {
        if (column.getPrecision() == null || column.getScale() == null) {
            return true;
        }
        return column.getPrecision() <= 0 || column.getScale() <= 0;
    }

    @Override
    public boolean isSupportsNumber(DBDAttributeBinding column) {
        return false;
    }

    @Override
    public boolean supportsMergeExportFile() {
        return false;
    }

    @Override
    public List<String> getStatementWarningList(DBCStatement dbcStatement, DBCSession session, String sql) throws DBCException, SQLException {
        return dbcStatement.getStatementWarningList();
    }

    @Override
    public String getDesensitizePrefix() {
        return null;
    }

    @Override
    public void setDbId(String dbId) {

    }

    @Override
    public String changePrintQuery(String query, long offset, long limit) {
        return query;
    }

    @Override
    public boolean supportsCatalog() {
        return false;
    }

    @Override
    public boolean catalogIsSchema() {
        return false;
    }

    @Override
    public boolean ignoreTableName() {
        return false;
    }

    @Override
    public boolean isRedis() {
        return false;
    }

    @Override
    public boolean getSchemaFromQuery(DBPDataSource dataSource) {
        return false;
    }

    @Override
    public boolean getCatalogFromQuery(DBPDataSource dataSource) {
        return false;
    }

    @Override
    public String defaultSchemaName() {
        return "";
    }

    @Override
    public boolean getSchemaFromParser() {
        return false;
    }

    @Override
    public boolean isQueryComment() {
        return false;
    }

    @Override
    public void setQueryComment(boolean queryComment) {

    }

    @Override
    public boolean supportsTableColumnSQL() {
        return false;
    }

    @Override
    public String generateSystemPrivilegeSql() {
        return "";
    }

    @Override
    public List<String> getDirectories(DBRProgressMonitor monitor, DBCExecutionContext context) {
        return null;
    }

    @Override
    public String getObjectType(DBRProgressMonitor monitor,DBCExecutionContext context ,String objectName, String owner) {
        return null;
    }

    @Override
    public String getSchemaIdSql(String schemaName) {
        return null;
    }

    @Override
    public List<Long> getSchemaId(List<Map<String, Object>> list) {
        return null;
    }

    @Override
    public String getTableRealNameSql(List<Long> schemaIds, String schemaName, String tableName) {
        return null;
    }

    @Override
    public String generateConditionSql(String key, Pair<String, String> pair, String conditionSql, String and) {
        String column = getFormatColumnName(key);
        String typeName = pair.getFirst();
        String value = pair.getSecond();
        if (typeName.equalsIgnoreCase("DATETIME") || typeName.equalsIgnoreCase("TIMESTAMP")) {
            conditionSql = conditionSql + and + column + " = TIMESTAMP '" + value + "'";
        } else {
            conditionSql = conditionSql + and + column + " = '" + value + "'";
        }
        return conditionSql;
    }
}
