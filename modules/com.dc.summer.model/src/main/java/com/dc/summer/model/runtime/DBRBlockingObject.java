

package com.dc.summer.model.runtime;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;

/**
 * Object which can block execution flow.
 * Such as socket, statement or connection, etc.
 */
public interface DBRBlockingObject {

    /**
     * Cancels block.
     * In actual implementation this object may not block process at the moment of invocation
     * of this method. Implementor should check object's state and cancel blocking on demand.
     *
     * @param monitor     monitor
     * @param blockThread thread which initiated the block. Can be null.
     * @throws DBException on error
     */
    void cancelBlock(@NotNull DBRProgressMonitor monitor, @Nullable Thread blockThread) throws DBException;

}
