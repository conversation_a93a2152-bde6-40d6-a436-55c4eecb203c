
package com.dc.summer.model.connection;

import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceOriginProvider;
import com.dc.summer.model.preferences.DBPPreferenceStore;

import java.util.List;

/**
 * Data source provider
 */
public interface DBPDataSourceProviderRegistry {

    List<? extends DBPDataSourceProviderDescriptor> getDataSourceProviders();

    List<? extends DBPDataSourceProviderDescriptor> getEnabledDataSourceProviders();

    DBPDataSourceProviderDescriptor getDataSourceProvider(String id);
    DBPDataSourceProviderDescriptor makeFakeProvider(String providerID);

    DBPAuthModelDescriptor getAuthModel(String id);
    List<? extends DBPAuthModelDescriptor> getAllAuthModels();
    List<? extends DBPAuthModelDescriptor> getApplicableAuthModels(DBPDriver driver);

    DBPConnectionType getConnectionType(String id, DBPConnectionType defaultType);
    void addConnectionType(DBPConnectionType connectionType);
    void removeConnectionType(DBPConnectionType connectionType);
    void saveConnectionTypes();

    DBPDriver findDriver(String driverIdOrName);

    DBPEditorContribution[] getContributedEditors(String category, DBPDataSourceContainer dataSource);

    // This pref store can be used to listen preference changes in ANY datasource.
    DBPPreferenceStore getGlobalDataSourcePreferenceStore();

    DBPDataSourceOriginProvider getDataSourceOriginProvider(String id);

}
