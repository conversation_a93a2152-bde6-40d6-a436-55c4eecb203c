
package com.dc.summer.model.impl.data.formatters;

import com.dc.summer.model.DBConstants;
import com.dc.summer.model.data.DBDDataFormatterSample;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.Locale;
import java.util.Map;
import java.util.Random;

public class TimestampFormatSample implements DBDDataFormatterSample {

    @Override
    public Map<String, Object> getDefaultProperties(Locale locale) {
        return Collections.singletonMap(
            DateTimeDataFormatter.PROP_PATTERN,
            DBConstants.DEFAULT_TIMESTAMP_FORMAT);
    }

    @Override
    public Object getSampleValue() {
        Timestamp ts = new Timestamp(System.currentTimeMillis());
        ts.setNanos(ts.getNanos() + new Random(System.currentTimeMillis()).nextInt(99999));
        return ts;
    }

}
