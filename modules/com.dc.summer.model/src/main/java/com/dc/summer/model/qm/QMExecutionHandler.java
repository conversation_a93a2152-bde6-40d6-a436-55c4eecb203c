

package com.dc.summer.model.qm;

import com.dc.summer.model.exec.*;
import com.dc.summer.model.runtime.features.DBRFeature;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPTransactionIsolation;

import java.util.Map;

/**
 * Query manager execution handler.
 * Handler methods are invoked right at time of DBC operation, so they should work as fast as possible.
 * Implementers should not invoke any DBC execution function in passed objects - otherwise execution handling may enter infinite recursion.
 */
public interface QMExecutionHandler {

    @NotNull
    String getHandlerName();

    void handleContextOpen(@NotNull DBCExecutionContext context, boolean transactional);

    void handleContextClose(@NotNull DBCExecutionContext context);

    void handleSessionOpen(@NotNull DBCSession session);

    void handleSessionClose(@NotNull DBCSession session);

    void handleTransactionAutocommit(@NotNull DBCExecutionContext context, boolean autoCommit);

    void handleTransactionIsolation(@NotNull DBCExecutionContext context, @NotNull DBPTransactionIsolation level);

    void handleTransactionCommit(@NotNull DBCExecutionContext context);

    void handleTransactionSavepoint(@NotNull DBCSavepoint savepoint);

    void handleTransactionRollback(@NotNull DBCExecutionContext context, @Nullable DBCSavepoint savepoint);

    void handleStatementOpen(@NotNull DBCStatement statement);

    void handleStatementExecuteBegin(@NotNull DBCStatement statement);

    void handleStatementExecuteEnd(@NotNull DBCStatement statement, long rows, Throwable error);

    void handleStatementBind(@NotNull DBCStatement statement, Object column, @Nullable Object value);

    void handleStatementClose(@NotNull DBCStatement statement, long rows);

    void handleResultSetOpen(@NotNull DBCResultSet resultSet);

    void handleResultSetClose(@NotNull DBCResultSet resultSet, long rowCount);

    void handleScriptBegin(@NotNull DBCSession session);
    
    void handleScriptEnd(@NotNull DBCSession session);

    void handleFeatureUsage(@NotNull DBRFeature feature, @Nullable Map<String, Object> parameters);
}
