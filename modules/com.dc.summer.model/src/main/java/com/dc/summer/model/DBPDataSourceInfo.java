

package com.dc.summer.model;

import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.sql.SQLQueryType;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.utils.Pair;
import org.osgi.framework.Version;

import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * DBPDataSourceInfo
 */
public interface DBPDataSourceInfo
{

    /**
     * Retrieves whether this database is in read-only mode.
     *
     * @return <code>true</code> if so; <code>false</code> otherwise
     */
    boolean isReadOnlyData();

    /**
     * Retrieves whether this database is in read-only mode.
     *
     * @return <code>true</code> if so; <code>false</code> otherwise
     */
    boolean isReadOnlyMetaData();

    /**
     * Retrieves the name of this database product.
     *
     * @return database product name
     */
    String getDatabaseProductName();

    /**
     * Retrieves the version number of this database product.
     *
     * @return database version number
     */
    String getDatabaseProductVersion();

    /**
     * Detailed server information.
     * @return server version details or null
     */
    Map<String, Object> getDatabaseProductDetails();

    /**
     * Database server version
     * @return server version
     */
    Version getDatabaseVersion();

    /**
     * Retrieves the name of this driver.
     *
     * @return driver name
     */
    String getDriverName();

    /**
     * Retrieves the version number of this driver as a <code>String</code>.
     *
     * @return driver version
     */
    String getDriverVersion();

    /**
     * Retrieves the database vendor's preferred term for "schema".
     *
     * @return the vendor term for "schema"
     */
    String getSchemaTerm();

    /**
     * Retrieves the database vendor's preferred term for "procedure".
     *
     * @return the vendor term for "procedure"
     */
    String getProcedureTerm();

    /**
     * Retrieves the database vendor's preferred term for "catalog".
     *
     * @return the vendor term for "catalog"
     */
    String getCatalogTerm();

    /**
     * Retrieves whether this database supports transactions. If not, invoking the
     * method <code>commit</code> is a noop.
     *
     * @return <code>true</code> if transactions are supported;
     *         <code>false</code> otherwise
     */
    boolean supportsTransactions();

    /**
     * Retrieves whether this database supports transactions for DDLs. If not, then
     * DDL commands will use transaction mode from the session rather than using
     * transactions on their own.
     *
     * @return {@code true} if the transactions inside DDLs are supported, {@code false} otherwise
     */
    boolean supportsTransactionsForDDL();

    /**
     * Retrieves whether this database supports savepoints.
     *
     * @return <code>true</code> if savepoints are supported;
     *         <code>false</code> otherwise
     */
    boolean supportsSavepoints();

    /**
     * Retrieves whether this database supports referential integrity (foreign keys and checks).
     * @return true or false
     */
    boolean supportsReferentialIntegrity();

    /**
     * Retrieves whether this database supports indexes.
     * @return true or false
     */
    boolean supportsIndexes();

    /**
     * Retrieves whether this database supports stored code (procedures, functions, packages, etc).
     * @return true or false
     */
    boolean supportsStoredCode();

    /**
     * Retrieves list of supported transaction isolation levels
     * @return list of supported transaction isolation levels
     */
    Collection<DBPTransactionIsolation> getSupportedTransactionsIsolation();

    boolean supportsBatchUpdates();

    boolean supportsResultSetLimit();

    boolean supportsResultSetScroll();

    boolean supportsResultSetOrdering();

    boolean supportsNullableUniqueConstraints();

    /**
     * Dynamic metadata means that each execution of the same query may produce different results.
     */
    boolean isDynamicMetadata();

    /**
     * Checks whether this data source supports multiple results for a single statement
     */
    boolean supportsMultipleResults();

    /**
     * Workaround for broken drivers (#2792)
     */
    boolean isMultipleResultsFetchBroken();

    DBSObjectType[] getSupportedObjectTypes();

    boolean needsTableMetaForColumnResolution();

    boolean supportsLocalTransaction();

    String getFormatColumnName(String column);

    boolean supportsDDLAutoCommit();

    boolean isExecuteErrorTransactionInterrupted();

    boolean isQueryStatementLockTable();

    List<String> getBigDataColumnType();

    String getTableColumnSql(String schemaName , String tableName);

    /**
     * 获取同义词SQL，主要用于 同义词支持列注释功能
     * <a href="http://*************/zentao/story-view-854.html">研发需求文档</a>
     *
     * @param schemaName schema name
     * @param synonym synonym
     * @return synonym sql
     */
    String getSynonymSql(String schemaName , String synonym);

    boolean supportDDLCallable();

    String getPrimaryKeySql(String schemaName, String tableName);

    List<String> getPrimaryKeyColumns(List<Map<String, Object>> list);

    String getTableRealNameSql(String schemaName, String tableName);

    String getTableRealName(List<Map<String, Object>> list);

    String getCheckTableExistenceSql(String name);

    void setDatabaseVersion(boolean isDockerVersion);

    boolean isSupportsTransactionIsolation();

    boolean isIntervalNeedQuotes();

    List<SQLQueryType> getBackupTypes();

    List<SQLQueryType> getDmlTypes();

    String generateInsertSql(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents);

    String generateInsertIgnoreSql(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents);

    String generateReplaceSql(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents);

    String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content);

    String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents);

    String generateTruncateSql(String schemaName, String tableName);

    List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException;

    boolean isSupportsPrepareStatement(DBPDataSource dataSource);

    boolean isRepeatProperties(DBPConnectionConfiguration configuration);

    boolean supportsSetNetworkTimeout();

    String getFunctionOrProcedureOid(String objectName, String schemaName, List<String> args);

    boolean isSupportsBooleanQuoted();

    boolean isNeedAppendColumnType();

    boolean isSystemSchema(String name);

    boolean showInteger(DBDAttributeBinding column);

    boolean isSupportsNumber(DBDAttributeBinding column);

    boolean supportsMergeExportFile();

    List<String> getStatementWarningList(DBCStatement dbcStatement, DBCSession session, String sql) throws DBCException, SQLException;

    /**
     * ES 脱敏加入 _source
     */
    String getDesensitizePrefix();

    void setDbId(String dbId);

    String changePrintQuery(String query, long offset, long limit);

    boolean supportsCatalog();

    boolean catalogIsSchema();

    boolean ignoreTableName();

    boolean isRedis();

    boolean getSchemaFromQuery(DBPDataSource dataSource);

    boolean getCatalogFromQuery(DBPDataSource dataSource);

    String defaultSchemaName();

    boolean getSchemaFromParser();

    boolean isQueryComment();

    void setQueryComment(boolean queryComment);

    boolean supportsTableColumnSQL();

    String generateSystemPrivilegeSql();

    List<String> getDirectories(DBRProgressMonitor monitor, DBCExecutionContext context);

    String getObjectType(DBRProgressMonitor monitor,DBCExecutionContext context ,String objectName, String owner);

    String getSchemaIdSql(String schemaName);

    List<Long> getSchemaId(List<Map<String, Object>> list);

    String getTableRealNameSql(List<Long> schemaIds, String schema, String tableName);

    String generateConditionSql(String key, Pair<String, String> pair, String conditionSql, String and);
}
