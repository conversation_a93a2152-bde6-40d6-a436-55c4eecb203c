
## Errors ##

error_not_connected_to_database = \u30C7\u30FC\u30BF\u30D9\u30FC\u30B9\u306B\u63A5\u7D9A\u3055\u308C\u3066\u3044\u307E\u305B\u3093
error_can_create_temp_dir = \u4E00\u6642\u30C7\u30A3\u30EC\u30AF\u30C8\u30EA "{0}" \u3092\u4F5C\u6210\u3067\u304D\u307E\u305B\u3093
error_can_create_temp_file = "{1}" \u306B\u4E00\u6642\u30D5\u30A1\u30A4\u30EB "{0}" \u3092\u4F5C\u6210\u3067\u304D\u307E\u305B\u3093
dialog_web_download_text_known = \u30C0\u30A6\u30F3\u30ED\u30FC\u30C9 {0} / {1} ({2})\n\u6B8B\u308A\u6642\u9593\u306E\u76EE\u5B89: {3}
dialog_web_download_text_unknown = \u30C0\u30A6\u30F3\u30ED\u30FC\u30C9 {0}

common_error_sql=SQL\u30A8\u30E9\u30FC
model_constraint_type_foreign_key = \u5916\u90E8\u30AD\u30FC
model_constraint_type_primary_key = \u4E3B\u30AD\u30FC
model_constraint_type_unique_key = \u30E6\u30CB\u30FC\u30AF\u30AD\u30FC

model_navigator__connections = \ \u63A5\u7D9A
model_navigator_Connection = \u63A5\u7D9A

model_connection_events_event_after_connect = \u63A5\u7D9A\u5F8C
model_connection_events_event_after_disconnect = \u5207\u65AD\u5F8C
model_connection_events_event_before_connect = \u63A5\u7D9A\u524D
model_connection_events_event_before_disconnect = \u5207\u65AD\u524D

model_edit_execute_ = \u5B9F\u884C
model_jdbc_read_database_meta_data = \u30C7\u30FC\u30BF\u30D9\u30FC\u30B9\u306E\u30E1\u30BF\u30C7\u30FC\u30BF\u3092\u8AAD\u307F\u53D6\u308B
model_jdbc__rows_fetched = \ \u884C\u3092\u53D6\u5F97
model_jdbc_create_new_constraint = \u65B0\u3057\u3044\u5236\u7D04\u3092\u4F5C\u6210
model_jdbc_create_new_foreign_key = \u65B0\u3057\u3044\u5916\u90E8\u30AD\u30FC\u3092\u4F5C\u6210
model_jdbc_array_result_set = \u7D50\u679C\u30BB\u30C3\u30C8\u306E\u914D\u5217
model_jdbc_create_new_index = \u65B0\u3057\u3044\u30A4\u30F3\u30C7\u30C3\u30AF\u30B9\u3092\u4F5C\u6210
model_jdbc_create_new_object = \u65B0\u3057\u3044\u30AA\u30D6\u30B8\u30A7\u30AF\u30C8\u3092\u4F5C\u6210
model_jdbc_create_new_table = \u65B0\u3057\u3044\u30C6\u30FC\u30D6\u30EB\u3092\u4F5C\u6210
model_jdbc_create_new_table_column = \u65B0\u3057\u3044\u30C6\u30FC\u30D6\u30EB\u5217\u3092\u4F5C\u6210
model_jdbc_Database = \u30C7\u30FC\u30BF\u30D9\u30FC\u30B9
model_jdbc_delete_object = \u30AA\u30D6\u30B8\u30A7\u30AF\u30C8\u3092\u524A\u9664
model_jdbc_driver_properties = \u30C9\u30E9\u30A4\u30D0\u306E\u30D7\u30ED\u30D1\u30C6\u30A3
model_jdbc_drop_constraint = \u5236\u7D04\u306E\u524A\u9664
model_jdbc_drop_foreign_key = \u5916\u90E8\u30AD\u30FC\u306E\u524A\u9664
model_jdbc_drop_index = \u30A4\u30F3\u30C7\u30C3\u30AF\u30B9\u306E\u524A\u9664
model_jdbc_drop_table = \u30C6\u30FC\u30D6\u30EB\u306E\u524A\u9664
model_jdbc_drop_table_column = \u30C6\u30FC\u30D6\u30EB\u5217\u306E\u524A\u9664
model_jdbc_exception_bad_savepoint_object = \u4E0D\u6B63\u306A\u30BB\u30FC\u30D6\u30DD\u30A4\u30F3\u30C8\u30AA\u30D6\u30B8\u30A7\u30AF\u30C8
model_jdbc_exception_could_not_bind_statement_parameter = \u30B9\u30C6\u30FC\u30C8\u30E1\u30F3\u30C8\u30D1\u30E9\u30E1\u30FC\u30BF\u3092\u30D0\u30A4\u30F3\u30C9\u3067\u304D\u307E\u305B\u3093
model_jdbc_exception_could_not_close_connection = \u63A5\u7D9A\u3092\u9589\u3058\u308B\u3053\u3068\u304C\u3067\u304D\u307E\u305B\u3093
model_jdbc_exception_could_not_get_result_set_value = \u7D50\u679C\u30BB\u30C3\u30C8\u306E\u5024\u3092\u53D6\u5F97\u3067\u304D\u307E\u305B\u3093
model_jdbc_exception_internal_jdbc_driver_error = JDBC\u30C9\u30E9\u30A4\u30D0\u306E\u5185\u90E8\u30A8\u30E9\u30FC
model_jdbc_exception_invalid_transaction_isolation_parameter = \u7121\u52B9\u306A\u30C8\u30E9\u30F3\u30B6\u30AF\u30B7\u30E7\u30F3\u5206\u96E2\u30D1\u30E9\u30E1\u30FC\u30BF
model_jdbc_exception_unsupported_array_type_ = \u30B5\u30DD\u30FC\u30C8\u3055\u308C\u3066\u3044\u306A\u3044\u914D\u5217\u30BF\u30A4\u30D7: 
model_jdbc_exception_unsupported_value_type_ = \u30B5\u30DD\u30FC\u30C8\u3055\u308C\u3066\u3044\u306A\u3044\u5024\u30BF\u30A4\u30D7: 
model_jdbc_fetch_table_data = \u30C6\u30FC\u30D6\u30EB\u30C7\u30FC\u30BF\u3092\u53D6\u5F97
model_jdbc_fetch_table_row_count = \u30C6\u30FC\u30D6\u30EB\u306E\u884C\u6570\u3092\u53D6\u5F97
model_jdbc_find_best_row_identifier = \u6700\u9069\u306A\u884C\u8B58\u5225\u5B50\u3092\u898B\u3064\u3051\u308B
model_jdbc_find_objects_by_name = \u540D\u524D\u3067\u30AA\u30D6\u30B8\u30A7\u30AF\u30C8\u3092\u691C\u7D22
model_jdbc_find_version_columns = \u30D0\u30FC\u30B8\u30E7\u30F3\u5217\u3092\u691C\u7D22
model_jdbc_jdbc_error = JDBC\u30A8\u30E9\u30FC
model_jdbc_load_catalogs = \u30AB\u30BF\u30ED\u30B0\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_client_info = \u30AF\u30E9\u30A4\u30A2\u30F3\u30C8\u60C5\u5831\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_column_privileges = \u5217\u306E\u6A29\u9650\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_columns = \u5217\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_cross_reference = \u76F8\u4E92\u53C2\u7167\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_exported_keys = \u30A8\u30AF\u30B9\u30DD\u30FC\u30C8\u6E08\u307F\u306E\u30AD\u30FC\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_from_file_ = \u30D5\u30A1\u30A4\u30EB\u304B\u3089\u8AAD\u307F\u8FBC\u3080 ...
model_jdbc_load_function_columns = \u95A2\u6570\u306E\u5217\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_functions = \u95A2\u6570\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_imported_keys = \u30A4\u30F3\u30DD\u30FC\u30C8\u6E08\u307F\u30AD\u30FC\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_indexes = \u30A4\u30F3\u30C7\u30C3\u30AF\u30B9\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_primary_keys = \u4E3B\u30AD\u30FC\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_procedure_columns = \u30D7\u30ED\u30FC\u30B8\u30E3\u306E\u5217\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_procedures = \u30D7\u30ED\u30FC\u30B8\u30E3\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_schemas = \u30B9\u30AD\u30FC\u30DE\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_table_privileges = \u30C6\u30FC\u30D6\u30EB\u306E\u6A29\u9650\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_table_types = \u30C6\u30FC\u30D6\u30EB\u306E\u7A2E\u985E\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_tables = \u30C6\u30FC\u30D6\u30EB\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_type_info = \u30BF\u30A4\u30D7\u60C5\u5831\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_udt_attributes = UDT\u306E\u5C5E\u6027\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_load_udts = UDT\u3092\u8AAD\u307F\u8FBC\u3080
model_jdbc_lob_and_binary_data_cant_be_edited_inline = LOB\u304A\u3088\u3073\u30D0\u30A4\u30CA\u30EA\u30C7\u30FC\u30BF\u306F\u30A4\u30F3\u30E9\u30A4\u30F3\u3067\u7DE8\u96C6\u3067\u304D\u307E\u305B\u3093
model_jdbc_max_length = \u6700\u5927\u9577
model_jdbc_None = \u306A\u3057
model_jdbc_precision = \u7CBE\u5EA6
model_jdbc_Procedure = \u30D7\u30ED\u30FC\u30B8\u30E3
model_jdbc_rename_object = \u30AA\u30D6\u30B8\u30A7\u30AF\u30C8\u306E\u540D\u524D\u3092\u5909\u66F4
model_jdbc_read_committed = READ COMMITTED
model_jdbc_read_uncommitted = READ UNCOMMITTED
model_jdbc_repeatable_read = REPEATABLE READ
model_jdbc_save_to_file_ = \u30D5\u30A1\u30A4\u30EB\u306B\u4FDD\u5B58 ...
model_jdbc_scale = \u30B9\u30B1\u30FC\u30EB
model_jdbc_Schema = \u30B9\u30AD\u30FC\u30DE
model_jdbc_Serializable = SERIALIZABLE
model_jdbc_unknown = \u4E0D\u660E
model_navigator_Description = \u8AAC\u660E
model_navigator_load_ = \u8AAD\u307F\u8FBC\u3080 
model_navigator_load_items_ = \u30A2\u30A4\u30C6\u30E0\u3092\u8AAD\u307F\u8FBC\u3080 ...
model_navigator_Model_root = \u30E2\u30C7\u30EB\u30EB\u30FC\u30C8
model_navigator_Name = \u540D\u524D
model_navigator_Project = \u30D7\u30ED\u30B8\u30A7\u30AF\u30C8
model_navigator_Root = \u30EB\u30FC\u30C8
model_struct_Association = \u30A2\u30BD\u30B7\u30A8\u30FC\u30B7\u30E7\u30F3
model_struct_Cascade = CASCADE
model_struct_Check = \u30C1\u30A7\u30C3\u30AF\u5236\u7D04
model_struct_Clustered = \u30AF\u30E9\u30B9\u30BF\u30FC\u5316
model_struct_Foreign_Key = \u5916\u90E8\u30AD\u30FC
model_struct_Hashed = \u30CF\u30C3\u30B7\u30E5
model_struct_Index = \u30A4\u30F3\u30C7\u30C3\u30AF\u30B9
model_struct_Inheritance = \u7D99\u627F
model_struct_No_Action = NO ACTION
model_struct_Not_NULL = NOT NULL
model_struct_Other = \u305D\u306E\u4ED6
model_struct_Primary_Key = \u4E3B\u30AD\u30FC
model_struct_Restrict = RESTRICT
model_struct_Set_Default = SET DEFAULT
model_struct_Set_NULL = SET NULL
model_struct_Unique_Key = \u30E6\u30CB\u30FC\u30AF\u30AD\u30FC
model_struct_Virtual_Key = \u4EEE\u60F3\u30E6\u30CB\u30FC\u30AF\u30AD\u30FC
model_struct_Virtual_Foreign_Key = \u4EEE\u60F3\u5916\u90E8\u30AD\u30FC
model_struct_Pseudo_Key = \u7591\u4F3C\u30AD\u30FC
model_struct_Unknown = \u4E0D\u660E

dbp_connection_type_table_development = Development
dbp_connection_type_table_production = Production
dbp_connection_type_table_production_database = \u672C\u756A\u30C7\u30FC\u30BF\u30D9\u30FC\u30B9
dbp_connection_type_table_regular_development_database = \u901A\u5E38\u306E\u958B\u767A\u30C7\u30FC\u30BF\u30D9\u30FC\u30B9
dbp_connection_type_table_test = Test
dbp_connection_type_table_test_database = \u30C6\u30B9\u30C8 (QA) \u30C7\u30FC\u30BF\u30D9\u30FC\u30B9

dialog_connection_wizard_start_connection_monitor_close =\u63A5\u7D9A\u3092\u9589\u3058\u308B
dialog_connection_wizard_start_connection_monitor_connected =\u63A5\u7D9A\u6E08\u307F ({0} ms)
dialog_connection_wizard_start_connection_monitor_start =\u63A5\u7D9A\u3092\u53D6\u5F97\u3059\u308B
dialog_connection_wizard_start_connection_monitor_subtask_test =\u63A5\u7D9A\u306E\u30C6\u30B9\u30C8
dialog_connection_wizard_start_connection_monitor_success =\u6210\u529F
dialog_connection_wizard_start_connection_monitor_thread =\u30C6\u30B9\u30C8\u30C7\u30FC\u30BF\u30BD\u30FC\u30B9\u63A5\u7D9A
dialog_connection_wizard_start_dialog_error_message =\u30C7\u30FC\u30BF\u30D9\u30FC\u30B9\u63A5\u7D9A\u30A8\u30E9\u30FC

dbp_permission_edit_data_name = \u30C7\u30FC\u30BF\u306E\u7DE8\u96C6\u3092\u5236\u9650\u3059\u308B
dbp_permission_edit_data_description = \u76F4\u63A5\u30C7\u30FC\u30BF\u3092\u5909\u66F4\u3067\u304D\u306A\u3044\u3088\u3046\u306B\u3057\u307E\u3059
dbp_permission_edit_metadata_name = \u69CB\u9020\u306E\u5909\u66F4\u3092\u5236\u9650\u3059\u308B
dbp_permission_edit_metadata_description = \u30C6\u30FC\u30D6\u30EB\u4F5C\u6210\u306A\u3069\u306E\u69CB\u9020 (\u30E1\u30BF\u30C7\u30FC\u30BF) \u306E\u5909\u66F4\u3092\u5236\u9650\u3057\u307E\u3059
dbp_permission_execute_scripts_name = \u30B9\u30AF\u30EA\u30D7\u30C8\u306E\u5B9F\u884C\u3092\u5236\u9650\u3059\u308B
dbp_permission_execute_scripts_description = \u30AB\u30B9\u30BF\u30E0\u30E6\u30FC\u30B6\u30B9\u30AF\u30EA\u30D7\u30C8\uFF08SQL\uFF09\u306E\u5B9F\u884C\u3092\u5236\u9650\u3057\u307E\u3059
dbp_permission_import_data_name = \u30A4\u30F3\u30DD\u30FC\u30C8\u306E\u5236\u9650
dbp_permission_import_data_description = \u30A4\u30F3\u30DD\u30FC\u30C8\u3092\u5236\u9650\u3057\u307E\u3059

task_rows_fetched_message_part = \u884C\u53D6\u5F97: {0}
task_rows_modified_message_part = \u884C\u4FEE\u6B63: {0}
task_statements_executed_message_part = \u30AF\u30A8\u30EA: {0}

controls_querylog__ms = \ \u30DF\u30EA\u79D2
controls_querylog_action_clear_log = \u30ED\u30B0\u3092\u30AF\u30EA\u30A2\u3059\u308B
controls_querylog_action_copy = \u30B3\u30D4\u30FC
controls_querylog_action_copy_all_fields = \u3059\u3079\u3066\u306E\u30D5\u30A3\u30FC\u30EB\u30C9\u3092\u30B3\u30D4\u30FC
controls_querylog_action_select_all = \u3059\u3079\u3066\u9078\u629E
controls_querylog_column_connection_name = \u60C5\u5831\u5143
controls_querylog_column_connection_tooltip = \u3053\u306E\u30C7\u30FC\u30BF\u30D9\u30FC\u30B9\u30A4\u30D9\u30F3\u30C8\u304C\u5C5E\u3059\u308B\u63A5\u7D9A
controls_querylog_column_context_name = \u63A5\u7D9A
controls_querylog_column_context_tooltip = \u3053\u306E\u30A4\u30D9\u30F3\u30C8\u306E\u5F71\u97FF\u3092\u53D7\u3051\u308B\u5B9F\u969B\u306E\u7269\u7406\u63A5\u7D9A
controls_querylog_column_duration_name = \u671F\u9593
controls_querylog_column_duration_tooltip = \u64CD\u4F5C\u5B9F\u884C\u6642\u9593
controls_querylog_column_result_name = \u7D50\u679C
controls_querylog_column_result_tooltip = \u5B9F\u884C\u7D50\u679C
controls_querylog_column_rows_name = \u884C
controls_querylog_column_rows_tooltip = \u30B9\u30C6\u30FC\u30C8\u30E1\u30F3\u30C8\u306B\u3088\u3063\u3066\u51E6\u7406\u3055\u308C\u305F\u884C\u306E\u6570
controls_querylog_column_text_name = \u30C6\u30AD\u30B9\u30C8
controls_querylog_column_text_tooltip = SQL\u6587\u306E\u30C6\u30AD\u30B9\u30C8/\u8AAC\u660E
controls_querylog_column_time_name = \u6642\u9593
controls_querylog_column_time_tooltip = \u30B9\u30C6\u30FC\u30C8\u30E1\u30F3\u30C8\u304C\u5B9F\u884C\u3055\u308C\u305F\u6642\u523B
controls_querylog_column_type_name = \u30BF\u30A4\u30D7
controls_querylog_column_type_tooltip = \u30A4\u30D9\u30F3\u30C8\u30BF\u30A4\u30D7
controls_querylog_commit = \u30B3\u30DF\u30C3\u30C8
controls_querylog_connected_to = \u306B\u63A5\u7D9A\u3055\u308C\u3066\u3044\u307E\u3059 "
controls_querylog_disconnected_from = \u304B\u3089\u5207\u65AD\u3055\u308C\u305F "
controls_querylog_error = \u30A8\u30E9\u30FC [
controls_querylog_format_minutes = {0}\u5206{1}\u79D2
controls_querylog_job_refresh = \u30EA\u30ED\u30FC\u30C9QM\u30A4\u30D9\u30F3\u30C8\u30ED\u30B0
controls_querylog_label_result = \u7D50\u679C
controls_querylog_label_text = \u30C6\u30AD\u30B9\u30C8
controls_querylog_label_time = \u6642\u9593
controls_querylog_label_type = \u30BF\u30A4\u30D7
controls_querylog_rollback = \u30ED\u30FC\u30EB\u30D0\u30C3\u30AF
controls_querylog_savepoint = \u30BB\u30FC\u30D6\u30DD\u30A4\u30F3\u30C8
controls_querylog_script = \u30B9\u30AF\u30EA\u30D7\u30C8
controls_querylog_shell_text = \u30D3\u30E5\u30FC
controls_querylog_success = \u6210\u529F
controls_querylog_transaction = \u30C8\u30E9\u30F3\u30B6\u30AF\u30B7\u30E7\u30F3
