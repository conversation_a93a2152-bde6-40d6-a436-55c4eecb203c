#Generated by ResourceBundle Editor (http://essiembre.github.io/eclipse-rbe/)
# Copyright (C) 2012 Brook.Tran (<EMAIL>)

CreateLinkedFileRunnable_e_cancelled_link = \u5DF2\u53D6\u6D88\u5C06\u6587\u4EF6 {0} \u4E0E\u4F4D\u7F6E {1} \u7684\u94FE\u63A5

CreateLinkedFileRunnable_e_unable_to_link = \u65E0\u6CD5\u5C06\u6587\u4EF6 {0} \u4E0E\u4F4D\u7F6E {1} \u76F8\u5173\u8054

CreateLinkedFolderRunnable_e_cancelled_link = \u5DF2\u53D6\u6D88\u5C06\u6587\u4EF6\u5939 {0} \u4E0E\u4F4D\u7F6E {1} \u7684\u94FE\u63A5

CreateLinkedFolderRunnable_e_unable_to_link = \u65E0\u6CD5\u5C06\u6587\u4EF6\u5939 {0} \u4E0E\u4F4D\u7F6E {1} \u76F8\u5173\u8054

common_error_sql = SQL \u9519\u8BEF

dbp_connection_type_table_development = \u5F00\u53D1

dbp_connection_type_table_production = \u751F\u4EA7

dbp_connection_type_table_production_database = \u751F\u4EA7\u6570\u636E\u5E93

dbp_connection_type_table_regular_development_database = \u5E38\u89C4\u5F00\u53D1\u6570\u636E\u5E93

dbp_connection_type_table_test = \u6D4B\u8BD5

dbp_connection_type_table_test_database = \u6D4B\u8BD5 (QA) \u6570\u636E\u5E93

dbp_permission_edit_data_description = \u9650\u5236\u76F4\u63A5\u6570\u636E\u4FEE\u6539

dbp_permission_edit_data_name = \u9650\u5236\u6570\u636E\u7F16\u8F91

dbp_permission_edit_metadata_description = \u9650\u5236\u7ED3\u6784\uFF08\u5143\u6570\u636E\uFF09\u66F4\u6539\uFF0C\u4F8B\u5982\u521B\u5EFA\u8868

dbp_permission_edit_metadata_name = \u9650\u5236\u7ED3\u6784\u7F16\u8F91

dbp_permission_execute_scripts_description = \u9650\u5236\u81EA\u5B9A\u4E49\u7528\u6237\u811A\u672C (SQL) \u6267\u884C

dbp_permission_execute_scripts_name = \u9650\u5236\u811A\u672C\u6267\u884C

dbp_permission_import_data_description = \u9650\u5236\u5BFC\u5165\u6570\u636E

dbp_permission_import_data_name = \u9650\u5236\u6570\u636E\u5BFC\u5165

dialog_connection_wizard_start_connection_monitor_close = \u5173\u95ED\u8FDE\u63A5

dialog_connection_wizard_start_connection_monitor_connected = \u5DF2\u8FDE\u63A5 ({0} ms)

dialog_connection_wizard_start_connection_monitor_start = \u83B7\u53D6\u8FDE\u63A5

dialog_connection_wizard_start_connection_monitor_subtask_test = \u6D4B\u8BD5\u8FDE\u63A5

dialog_connection_wizard_start_connection_monitor_success = \u6210\u529F

dialog_connection_wizard_start_connection_monitor_thread = \u6D4B\u8BD5\u6570\u636E\u6E90\u8FDE\u63A5

dialog_connection_wizard_start_dialog_error_message = \u6570\u636E\u5E93\u8FDE\u63A5\u9519\u8BEF

dialog_web_download_text_known = \u5DF2\u4E0B\u8F7D {0} / {1} ({2})\r\n\u9884\u8BA1\u5269\u4F59\u65F6\u95F4\uFF1A{3}

dialog_web_download_text_unknown = \u5DF2\u4E0B\u8F7D {0}

error_can_create_temp_dir = \u65E0\u6CD5\u521B\u5EFA\u4E34\u65F6\u76EE\u5F55 "{0}"

error_can_create_temp_file = \u65E0\u6CD5\u5728 "{1}" \u521B\u5EFA\u4E34\u65F6\u76EE\u5F55"{0}" 

## Errors ##
error_not_connected_to_database = \u6CA1\u6709\u8FDE\u63A5\u5230\u6570\u636E\u5E93

model_connection_events_event_after_connect = \u8FDE\u63A5\u4E4B\u540E

model_connection_events_event_after_disconnect = \u65AD\u5F00\u8FDE\u63A5\u4E4B\u540E

model_connection_events_event_before_connect = \u8FDE\u63A5\u4E4B\u524D

model_connection_events_event_before_disconnect = \u65AD\u5F00\u8FDE\u63A5\u4E4B\u524D

model_constraint_type_foreign_key = \u5916\u952E

model_constraint_type_primary_key = \u4E3B\u952E

model_constraint_type_unique_key = \u552F\u4E00\u952E

model_edit_execute_ = \u6267\u884C

model_jdbc_Database = \u6570\u636E\u5E93

model_jdbc_None = \u6CA1\u6709\u4EFB\u4F55\u6570\u636E

model_jdbc_Procedure = \u5B58\u50A8\u8FC7\u7A0B

model_jdbc_Schema = \u6A21\u5F0F

model_jdbc_Serializable = \u53EF\u4E32\u884C\u5316

model_jdbc__rows_fetched = \u83B7\u53D6\u884C

model_jdbc_array_result_set = \u7ED3\u679C\u96C6\u6570\u7EC4

model_jdbc_create_new_constraint = \u65B0\u5EFA\u7EA6\u675F

model_jdbc_create_new_foreign_key = \u65B0\u5EFA\u5916\u952E

model_jdbc_create_new_index = \u65B0\u5EFA\u7D22\u5F15

model_jdbc_create_new_object = \u65B0\u5EFA\u5BF9\u8C61

model_jdbc_create_new_table = \u65B0\u5EFA\u8868

model_jdbc_create_new_table_column = \u65B0\u5EFA\u5217

model_jdbc_delete_object = \u5220\u9664\u5BF9\u8C61

model_jdbc_driver_properties = \u9A71\u52A8\u5C5E\u6027

model_jdbc_drop_constraint = \u5220\u9664\u7EA6\u675F

model_jdbc_drop_foreign_key = \u5220\u9664\u5916\u952E

model_jdbc_drop_index = \u5220\u9664\u7D22\u5F15

model_jdbc_drop_table = \u5220\u9664\u8868

model_jdbc_drop_table_column = \u5220\u9664\u5217

model_jdbc_exception_bad_savepoint_object = \u65E0\u6548\u4FDD\u5B58\u70B9\u5BF9\u8C61

model_jdbc_exception_could_not_bind_statement_parameter = \u65E0\u6CD5\u8BBE\u7F6E\u811A\u672C\u53C2\u6570

model_jdbc_exception_could_not_close_connection = \u65E0\u6CD5\u5173\u95ED\u8FDE\u63A5

model_jdbc_exception_could_not_get_result_set_value = \u65E0\u6CD5\u83B7\u53D6\u7ED3\u679C\u96C6\u6570\u636E

model_jdbc_exception_internal_jdbc_driver_error = jdbc \u9A71\u52A8\u5185\u90E8\u9519\u8BEF

model_jdbc_exception_invalid_transaction_isolation_parameter = \u4E8B\u52A1\u53C2\u6570\u65E0\u6548

model_jdbc_exception_unsupported_array_type_ = \u4E0D\u652F\u6301\u7684\u6570\u7EC4\u7C7B\u578B: 

model_jdbc_exception_unsupported_value_type_ = \u4E0D\u652F\u6301\u7684\u6570\u636E\u7C7B\u578B: 

model_jdbc_fetch_table_data = \u83B7\u53D6\u8868\u6570\u636E

model_jdbc_fetch_table_row_count = \u83B7\u53D6\u8868\u884C\u6570

model_jdbc_find_best_row_identifier = \u67E5\u627E\u6700\u5408\u9002\u7684\u884C\u6807\u8BC6

model_jdbc_find_objects_by_name = \u67E5\u627E\u5BF9\u8C61(\u901A\u8FC7\u5BF9\u8C61\u540D\u79F0)

model_jdbc_find_version_columns = \u67E5\u627E\u7248\u672C\u5217

model_jdbc_jdbc_error = JDBC \u9519\u8BEF

model_jdbc_load_catalogs = \u8F7D\u5165\u76EE\u5F55

model_jdbc_load_client_info = \u8F7D\u5165\u5BA2\u6237\u7AEF\u4FE1\u606F

model_jdbc_load_column_privileges = \u8F7D\u5165\u5217\u6743\u9650

model_jdbc_load_columns = \u8F7D\u5165\u5217

model_jdbc_load_cross_reference = \u8F7D\u5165\u4EA4\u53C9\u5F15\u7528

model_jdbc_load_exported_keys = \u8F7D\u5165\u5DF2\u5BFC\u51FA\u952E

model_jdbc_load_from_file_ = \u4ECE\u6587\u4EF6\u4E2D\u8F7D\u5165 ...

model_jdbc_load_function_columns = \u8F7D\u5165\u51FD\u6570\u5217

model_jdbc_load_functions = \u8F7D\u5165\u51FD\u6570

model_jdbc_load_imported_keys = \u8F7D\u5165\u5DF2\u5BFC\u5165\u7684\u952E

model_jdbc_load_indexes = \u8F7D\u5165\u7D22\u5F15

model_jdbc_load_primary_keys = \u8F7D\u5165\u4E3B\u952E

model_jdbc_load_procedure_columns = \u8F7D\u5165\u5B58\u50A8\u8FC7\u7A0B\u5217

model_jdbc_load_procedures = \u8F7D\u5165\u5B58\u50A8\u8FC7\u7A0B

model_jdbc_load_schemas = \u8F7D\u5165\u6A21\u5F0F(schemas)

model_jdbc_load_super_tables = \u8F7D\u5165\u4E3B\u8868

model_jdbc_load_super_types = \u8F7D\u5165\u4E3B\u8868

model_jdbc_load_table_privileges = \u8F7D\u5165\u8868\u6743\u9650

model_jdbc_load_table_types = \u8F7D\u5165\u8868\u7C7B\u578B

model_jdbc_load_tables = \u8F7D\u5165\u8868

model_jdbc_load_type_info = \u8F7D\u5165\u7C7B\u578B\u4FE1\u606F

model_jdbc_load_udt_attributes = \u8F7D\u5165UDT\u5C5E\u6027

model_jdbc_load_udts = \u8F7D\u5165 UDT

model_jdbc_lob_and_binary_data_cant_be_edited_inline = \u5B9E\u4F53\u56FE\u548C\u4E8C\u8FDB\u5236\u6570\u636E\u65E0\u6CD5\u76F4\u63A5\u7F16\u8F91

model_jdbc_max_length = \u6700\u5927\u957F\u5EA6

model_jdbc_precision = \u7CBE\u786E\u5EA6

model_jdbc_read_committed = \u8BFB\u5DF2\u63D0\u4EA4

model_jdbc_read_database_meta_data = \u8BFB\u53D6\u6570\u636E\u5E93\u5143\u6570\u636E

model_jdbc_read_uncommitted = \u8BFB\u672A\u63D0\u4EA4

model_jdbc_rename_object = \u91CD\u547D\u540D\u5BF9\u8C61

model_jdbc_repeatable_read = \u53EF\u91CD\u590D\u8BFB

model_jdbc_save_to_file_ = \u4FDD\u5B58\u81F3\u6587\u4EF6 ...

model_jdbc_scale = \u8303\u56F4

model_jdbc_type_name = \u7C7B\u578B\u540D\u79F0

model_jdbc_unknown = \u672A\u77E5

model_jdbc_unsupported_column_type_ = \u4E0D\u652F\u6301\u7684\u5217\u7C7B\u578B

model_jdbc_unsupported_content_value_type_ = \u4E0D\u652F\u6301\u7684\u5185\u5BB9\u7C7B\u578B

model_jdbc_unsupported_value_type_ = \u4E0D\u652F\u6301\u7684\u503C\u7C7B\u578B: 

model_navigator_Connection = \u8FDE\u63A5

model_navigator_Description = \u63CF\u8FF0

model_navigator_Model_root = \u6839\u6A21\u578B

model_navigator_Name = \u540D\u79F0

model_navigator_Project = \u9879\u76EE

model_navigator_Root = \u6839

model_navigator__connections = \ \u8FDE\u63A5

model_navigator_load_ = \u8F7D\u5165

model_navigator_load_items_ = \u8F7D\u5165\u9879 ...

model_struct_Association = \u5173\u8054

model_struct_Cascade = \u7EA7\u8054

model_struct_Check = \u68C0\u67E5

model_struct_Clustered = \u805A\u5408

model_struct_Foreign_Key = \u5916\u952E

model_struct_Hashed = \u6563\u5217

model_struct_Index = \u7D22\u5F15

model_struct_Inheritance = \u7EE7\u627F

model_struct_No_Action = \u65E0\u52A8\u4F5C

model_struct_Not_NULL = \u975E\u7A7A

model_struct_Other = \u5176\u5B83

model_struct_Primary_Key = \u4E3B\u952E

model_struct_Pseudo_Key = \u4F2A\u952E

model_struct_Restrict = \u9650\u5236

model_struct_Set_Default = \u8BBE\u4E3A\u9ED8\u8BA4

model_struct_Set_NULL = \u8BBE\u4E3A\u7A7A

model_struct_Statistic = \u7EDF\u8BA1

model_struct_Unique_Key = \u552F\u4E00\u952E

model_struct_Unknown = \u672A\u77E5

model_struct_Virtual_Foreign_Key = \u865A\u62DF\u5916\u952E

model_struct_Virtual_Key = \u865A\u62DF\u552F\u4E00\u952E

controls_querylog__ms = \u6BEB\u79D2

controls_querylog_action_clear_log = \u6E05\u9664\u65E5\u5FD7

controls_querylog_action_copy = \u590D\u5236

controls_querylog_action_copy_all_fields = \u590D\u5236\u6240\u6709\u5B57\u6BB5

controls_querylog_action_select_all = \u5168\u9009

controls_querylog_column_connection_name = \u6570\u636E\u6E90

controls_querylog_column_connection_tooltip = \u8BE5\u6570\u636E\u5E93\u4E8B\u4EF6\u6240\u5C5E\u7684\u8FDE\u63A5

controls_querylog_column_context_name = \u8FDE\u63A5

controls_querylog_column_context_tooltip = \u53D7\u6B64\u4E8B\u4EF6\u5F71\u54CD\u7684\u5B9E\u9645\u7269\u7406\u8FDE\u63A5

controls_querylog_column_duration_name = \u6301\u7EED\u65F6\u95F4

controls_querylog_column_duration_tooltip = \u64CD\u4F5C\u8FD0\u884C\u7684\u65F6\u95F4

controls_querylog_column_result_name = \u7ED3\u679C

controls_querylog_column_result_tooltip = \u6267\u884C\u7ED3\u679C

controls_querylog_column_rows_name = \u884C

controls_querylog_column_rows_tooltip = \u8BED\u53E5\u6267\u884C\u5F97\u5230\u7684\u6570\u636E\u884C\u6570

controls_querylog_column_text_name = \u6587\u672C

controls_querylog_column_text_tooltip = SQL \u8BED\u53E5\u6587\u672C/\u63CF\u8FF0

controls_querylog_column_time_name = \u65F6\u95F4

controls_querylog_column_time_tooltip = \u6267\u884C SQL \u8BED\u53E5\u7684\u65F6\u95F4

controls_querylog_column_type_name = \u7C7B\u578B

controls_querylog_column_type_tooltip = \u4E8B\u4EF6\u7C7B\u578B

controls_querylog_commit = \u63D0\u4EA4

controls_querylog_connected_to = \u8FDE\u63A5\u81F3 "

controls_querylog_disconnected_from = \u65AD\u5F00\u8FDE\u63A5 "

controls_querylog_error = \u9519\u8BEF [

controls_querylog_format_minutes = {0} \u5206 {1} \u79D2

controls_querylog_job_refresh = \u91CD\u65B0\u8F7D\u5165\u67E5\u8BE2\u7BA1\u7406\u5668\u7684\u65E5\u5FD7

controls_querylog_label_result = \u7ED3\u679C

controls_querylog_label_text = \u6587\u672C

controls_querylog_label_time = \u65F6\u95F4

controls_querylog_label_type = \u7C7B\u578B

controls_querylog_rollback = \u56DE\u6EDA

controls_querylog_savepoint = \u4FDD\u5B58\u70B9

controls_querylog_script = \u811A\u672C

controls_querylog_shell_text = \u89C6\u56FE

controls_querylog_success = \u6210\u529F

controls_querylog_transaction = \u4E8B\u52A1
