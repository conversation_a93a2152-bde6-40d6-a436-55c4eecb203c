

package com.dc.summer.model.navigator.meta;

import com.dc.summer.model.impl.AbstractDescriptor;
import org.apache.commons.jexl3.JexlExpression;
import com.dc.summer.DBException;
import com.dc.summer.Log;

/**
 * DBXTreeIcon
 */
public class DBXTreeIcon
{
    private static final Log log = Log.getLog(DBXTreeIcon.class);

    private final String exprString;
    private JexlExpression expression;

    public DBXTreeIcon(String exprString)
    {
        this.exprString = exprString;
        try {
            this.expression = AbstractDescriptor.parseExpression(exprString);
        } catch (DBException ex) {
            log.warn("Can't parse icon expression: " + exprString, ex);
        }
    }

    public void dispose()
    {
    }

    public String getExprString()
    {
        return exprString;
    }

    public JexlExpression getExpression()
    {
        return expression;
    }

}
