
package com.dc.summer.model.impl.data.formatters;

/**
 * Hex formatter.
 * Formats binary data to hex with preceding x'0123456789ABCDEF'
 */
public class BinaryFormatterHexString extends BinaryFormatterHex {

    public static final BinaryFormatterHexString INSTANCE = new BinaryFormatterHexString();

    private static final String HEX_PREFIX = "x'";
    private static final String HEX_POSTFIX = "'";

    @Override
    public String getId()
    {
        return "hex_string";
    }

    @Override
    public String getTitle()
    {
        return "Hex";
    }

    @Override
    public String toString(byte[] bytes, int offset, int length)
    {
        return HEX_PREFIX + super.toString(bytes, offset, length) + HEX_POSTFIX;
    }

    @Override
    public byte[] toBytes(String string)
    {
        if (string.startsWith(HEX_PREFIX) || string.endsWith(HEX_POSTFIX)) {
            string = string.substring(HEX_PREFIX.length(), string.length() - HEX_PREFIX.length() - HEX_POSTFIX.length());
        }
        return super.toBytes(string);
    }

}
