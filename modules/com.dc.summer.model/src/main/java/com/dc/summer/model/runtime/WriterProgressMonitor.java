
package com.dc.summer.model.runtime;

import java.io.PrintWriter;
import java.io.Writer;

/**
 * Progress monitor with extra logging
 */
public class WriterProgressMonitor extends ProxyProgressMonitor {

    private final PrintWriter out;

    public WriterProgressMonitor(DBRProgressMonitor monitor, Writer out) {
        super(monitor);
        this.out = new PrintWriter(out);
    }

    @Override
    public void beginTask(String name, int totalWork) {
        super.beginTask(name, totalWork);
        out.println(name);
    }

    @Override
    public void subTask(String name) {
        super.subTask(name);
        out.println("\t" + name);
    }
}