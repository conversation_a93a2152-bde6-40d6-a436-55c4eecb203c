
package com.dc.summer.model.impl.jdbc.exec;

import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBValueFormatting;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDDataFormatter;
import com.dc.summer.model.data.DBDDataFormatterProfile;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.model.sql.DBSQLException;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.utils.StackTraceUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.*;
import java.util.Calendar;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Manageable prepared statement.
 * Stores information about execution in query manager and operated progress monitor.
 */
@Slf4j
public class JDBCPreparedStatementImpl extends JDBCStatementImpl<PreparedStatement> implements JDBCPreparedStatement {

    private static final Object NULL_VALUE = new Object();

    private Map<Object, Object> paramMap;

    protected static class ContentParameter {
        String displayString;
        ContentParameter(JDBCSession session, Object value) {
            if (value instanceof RowId) {
                displayString = SQLUtils.quoteString(session.getDataSource(), new String(((RowId) value).getBytes()));
            } else if (value instanceof byte[]) {
                byte[] bytes = (byte[])value;
                displayString = DBValueFormatting.formatBinaryString(session.getDataSource(), bytes, DBDDisplayFormat.NATIVE, true);
            } else {
                displayString = "DATA(" + (value == null ? DBConstants.NULL_VALUE_LABEL : value.getClass().getSimpleName()) + ")";
            }
        }

        @Override
        public String toString() {
            return displayString;
        }
    }

    JDBCPreparedStatementImpl(
        @NotNull JDBCSession connection,
        @NotNull PreparedStatement original,
        String query,
        boolean disableLogging)
    {
        super(connection, original, disableLogging);
        setQueryString(query);
    }

    @Override
    public PreparedStatement getOriginal()
    {
        return original;
    }

    @Override
    public void close() {
        if (paramMap != null) {
            paramMap.clear();
            paramMap = null;
        }
        super.close();
    }

    public String getFormattedQuery() {
        if (paramMap == null) {
            return getQueryString();
        } else {
            String q = getQueryString();
            if (q == null) {
                return "";
            }
            int length = q.length();
            StringBuilder formatted = new StringBuilder(length * 2);
            int paramIndex = 0;
            for (int i = 0; i < length; i++) {
                char c = q.charAt(i);
                switch (c) {
                    case '?': {
                        paramIndex++;
                        Object paramValue = paramMap.get(paramIndex);
                        if (paramValue != null) {
                            formatted.append(formatParameterValue(paramValue));
                            continue;
                        }
                        break;
                    }
                    case ':': {
                        // FIXME: process named parameters
                        break;
                    }
                    case '\'':
                    case '"': {
                        formatted.append(c);
                        for (int k = i + 1; k < length; k++) {
                            char c2 = q.charAt(k);
                            if (c2 == c && q.charAt(k - 1) != '\\') {
                                i = k;
                                c = c2;
                                break;
                            } else {
                                formatted.append(c2);
                            }
                        }
                        break;
                    }
                }
                formatted.append(c);
            }

            return formatted.toString();
        }
    }

    @NotNull
    private String formatParameterValue(Object value) {
        if (value instanceof CharSequence) {
            return SQLUtils.quoteString(connection.getDataSource(), value.toString());
        } else if (value instanceof Number) {
            return DBValueFormatting.convertNumberToNativeString((Number) value, false);
        } else if (value instanceof java.util.Date) {
            try {
                DBDDataFormatterProfile formatterProfile = getSession().getDataFormatterProfile();
                if (value instanceof Date) {
                    return SQLUtils.quoteString(connection.getDataSource(), formatterProfile.createFormatter(DBDDataFormatter.TYPE_NAME_DATE, null).formatValue(value));
                } else if (value instanceof Time) {
                    return SQLUtils.quoteString(connection.getDataSource(), formatterProfile.createFormatter(DBDDataFormatter.TYPE_NAME_TIME, null).formatValue(value));
                } else {
                    return SQLUtils.quoteString(connection.getDataSource(), formatterProfile.createFormatter(DBDDataFormatter.TYPE_NAME_TIMESTAMP_TZ, null).formatValue(value));
                }
            } catch (Exception e) {
                log.debug("Error formatting date [" + value + "]", e);
            }
        } else if (value == NULL_VALUE) {
            return "NULL";
        }
        return value.toString();
    }

    protected void handleStatementBind(Object parameter, @Nullable Object o)
    {
        if (isQMLoggingEnabled()) {
            // Save parameters
            if (o == null) {
                o = NULL_VALUE;
            } else if (!DBUtils.isAtomicParameter(o)) {
                // Wrap complex things
                o = new ContentParameter(connection, o);
            }
            if (paramMap == null) {
                paramMap = new LinkedHashMap<>();
            }
            paramMap.put(parameter, o);
            if (getSession().isLoggingEnabled()) {
                QMUtils.getDefaultHandler().handleStatementBind(this, parameter, o);
            }
        }
    }

    ////////////////////////////////////////////////////////////////////
    // DBC Statement overrides
    ////////////////////////////////////////////////////////////////////

    @Override
    public boolean executeStatement()
        throws DBCException
    {
        try {
            return execute();
        }
        catch (SQLException e) {
            throw new DBSQLException(query, e, connection.getExecutionContext());
        }
    }

    @Override
    public void addToBatch() throws DBCException
    {
        try {
            addBatch();
        }
        catch (SQLException e) {
            throw new DBCException(e, connection.getExecutionContext());
        }
    }

    ////////////////////////////////////////////////////////////////////
    // Statement overrides
    ////////////////////////////////////////////////////////////////////

    @Override
    public JDBCResultSet executeQuery()
        throws SQLException
    {
        this.beforeExecute();
        try {
            return createResultSetImpl(getOriginal().executeQuery());
        } catch (Throwable e) {
            throw super.handleExecuteError(e);
        } finally {
            super.afterExecute();
        }
    }

    @Override
    public int executeUpdate()
        throws SQLException
    {
        this.beforeExecute();
        try {
            return getOriginal().executeUpdate();
        } catch (Throwable e) {
            throw super.handleExecuteError(e);
        } finally {
            super.afterExecute();
        }
    }

    @Override
    public boolean execute()
        throws SQLException
    {
        this.beforeExecute();
        try {
            return getOriginal().execute();
        } catch (Throwable e) {
            throw super.handleExecuteError(e);
        } finally {
            super.afterExecute();
        }
    }

    @Override
    public void setNull(int parameterIndex, int sqlType)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, sqlType);
        getOriginal().setNull(parameterIndex, sqlType);

        handleStatementBind(parameterIndex, null);
    }

    @Override
    public void setBoolean(int parameterIndex, boolean x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setBoolean(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setByte(int parameterIndex, byte x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setByte(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setShort(int parameterIndex, short x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setShort(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setInt(int parameterIndex, int x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setInt(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setLong(int parameterIndex, long x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setLong(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setFloat(int parameterIndex, float x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setFloat(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setDouble(int parameterIndex, double x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setDouble(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setBigDecimal(int parameterIndex, BigDecimal x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setBigDecimal(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setString(int parameterIndex, String x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setString(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setBytes(int parameterIndex, byte[] x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setBytes(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setDate(int parameterIndex, Date x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setDate(parameterIndex, x);
        
        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setTime(int parameterIndex, Time x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setTime(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setTimestamp(int parameterIndex, Timestamp x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setTimestamp(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setAsciiStream(int parameterIndex, InputStream x, int length)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, length);
        getOriginal().setAsciiStream(parameterIndex, x, length);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    @Deprecated
    public void setUnicodeStream(int parameterIndex, InputStream x, int length)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, length);
        getOriginal().setUnicodeStream(parameterIndex, x, length);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setBinaryStream(int parameterIndex, InputStream x, int length)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, length);
        getOriginal().setBinaryStream(parameterIndex, x, length);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void clearParameters()
        throws SQLException
    {
        getOriginal().clearParameters();
    }

    @Override
    public void setObject(int parameterIndex, Object x, int targetSqlType)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, targetSqlType);
        getOriginal().setObject(parameterIndex, x, targetSqlType);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setObject(int parameterIndex, Object x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setObject(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void addBatch()
        throws SQLException
    {
        getOriginal().addBatch();
    }

    @Override
    public void setCharacterStream(int parameterIndex, Reader reader, int length)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, reader, length);
        getOriginal().setCharacterStream(parameterIndex, reader, length);

        handleStatementBind(parameterIndex, reader);
    }

    @Override
    public void setRef(int parameterIndex, Ref x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setRef(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setBlob(int parameterIndex, Blob x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setBlob(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setClob(int parameterIndex, Clob x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setClob(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setArray(int parameterIndex, Array x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setArray(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public ResultSetMetaData getMetaData()
        throws SQLException
    {
        return getOriginal().getMetaData();
    }

    @Override
    public void setDate(int parameterIndex, Date x, Calendar cal)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, cal);
        getOriginal().setDate(parameterIndex, x, cal);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setTime(int parameterIndex, Time x, Calendar cal)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, cal);
        getOriginal().setTime(parameterIndex, x, cal);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setTimestamp(int parameterIndex, Timestamp x, Calendar cal)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, cal);
        getOriginal().setTimestamp(parameterIndex, x, cal);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setNull(int parameterIndex, int sqlType, String typeName)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, sqlType, typeName);
        getOriginal().setNull(parameterIndex, sqlType, typeName);

        handleStatementBind(parameterIndex, null);
    }

    @Override
    public void setURL(int parameterIndex, URL x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setURL(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public ParameterMetaData getParameterMetaData()
        throws SQLException
    {
        return getOriginal().getParameterMetaData();
    }

    @Override
    public void setRowId(int parameterIndex, RowId x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setRowId(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setNString(int parameterIndex, String x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setNString(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setNCharacterStream(int parameterIndex, Reader x, long length)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setNCharacterStream(parameterIndex, x, length);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setNClob(int parameterIndex, NClob x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setNClob(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setClob(int parameterIndex, Reader x, long length)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setClob(parameterIndex, x, length);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setBlob(int parameterIndex, InputStream x, long length)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setBlob(parameterIndex, x, length);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setNClob(int parameterIndex, Reader x, long length)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setNClob(parameterIndex, x, length);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setSQLXML(int parameterIndex, SQLXML xmlObject)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, xmlObject);
        getOriginal().setSQLXML(parameterIndex, xmlObject);

        handleStatementBind(parameterIndex, xmlObject);
    }

    @Override
    public void setObject(int parameterIndex, Object x, int targetSqlType, int scaleOrLength)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, targetSqlType, scaleOrLength);
        getOriginal().setObject(parameterIndex, x, targetSqlType, scaleOrLength);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setAsciiStream(int parameterIndex, InputStream x, long length)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, length);
        getOriginal().setAsciiStream(parameterIndex, x, length);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setBinaryStream(int parameterIndex, InputStream x, long length)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, length);
        getOriginal().setBinaryStream(parameterIndex, x, length);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setCharacterStream(int parameterIndex, Reader x, long length)
        throws SQLException
    {
        log.debug("==> {}: [{},{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x, length);
        getOriginal().setCharacterStream(parameterIndex, x, length);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setAsciiStream(int parameterIndex, InputStream x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setAsciiStream(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setBinaryStream(int parameterIndex, InputStream x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setBinaryStream(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setCharacterStream(int parameterIndex, Reader x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setCharacterStream(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setNCharacterStream(int parameterIndex, Reader x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setNCharacterStream(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setClob(int parameterIndex, Reader x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setClob(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setBlob(int parameterIndex, InputStream x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setBlob(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

    @Override
    public void setNClob(int parameterIndex, Reader x)
        throws SQLException
    {
        log.debug("==> {}: [{},{}]", StackTraceUtils.getCurrentMethodName(), parameterIndex, x);
        getOriginal().setNClob(parameterIndex, x);

        handleStatementBind(parameterIndex, x);
    }

}
