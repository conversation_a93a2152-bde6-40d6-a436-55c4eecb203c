
package com.dc.summer.model.struct;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.Nullable;

/**
 * DBSEntityAssociationLazy
 */
public interface DBSEntityAssociationLazy extends DBSEntityAssociation {

    @Nullable
    DBSEntityConstraint getReferencedConstraint(DBRProgressMonitor monitor) throws DBException;

    DBSEntity getAssociatedEntity(DBRProgressMonitor monitor) throws DBException;
}