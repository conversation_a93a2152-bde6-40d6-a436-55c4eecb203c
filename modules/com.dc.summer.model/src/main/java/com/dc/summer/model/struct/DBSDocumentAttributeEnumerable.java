
package com.dc.summer.model.struct;

import com.dc.summer.DBException;
import com.dc.summer.model.data.DBDLabelValuePair;
import com.dc.summer.model.exec.DBCSession;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.List;

public interface DBSDocumentAttributeEnumerable extends DBSDocumentContainer {
    @NotNull
    List<DBDLabelValuePair> getValueEnumeration(
        @NotNull DBCSession session,
        @NotNull DBSAttributeBase attribute,
        @Nullable String pattern,
        boolean calcCount,
        boolean caseInsensitive,
        int maxResults) throws DBException;
}
