
package com.dc.summer.runtime.properties;

import com.dc.summer.Log;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.meta.IPropertyCacheValidator;
import com.dc.summer.model.meta.LazyProperty;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.meta.PropertyGroup;
import com.dc.summer.model.preferences.DBPPropertySource;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.utils.BeanUtils;
import com.dc.utils.CommonUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.*;

/**
 * Abstract object attribute
 */
public abstract class ObjectAttributeDescriptor {

    static final Log log = Log.getLog(ObjectAttributeDescriptor.class);

    public static final Comparator<ObjectAttributeDescriptor> ATTRIBUTE_DESCRIPTOR_COMPARATOR = new Comparator<ObjectAttributeDescriptor>() {
        @Override
        public int compare(ObjectAttributeDescriptor o1, ObjectAttributeDescriptor o2) {
            return o1.getOrderNumber() - o2.getOrderNumber();
        }
    };

    private final DBPPropertySource source;
    private ObjectPropertyGroupDescriptor parent;
    private int orderNumber;
    private String id;
    private Method getter;
    private boolean isLazy;
    private IPropertyCacheValidator cacheValidator;
    private Class<?> declaringClass;

    public ObjectAttributeDescriptor(
        DBPPropertySource source,
        ObjectPropertyGroupDescriptor parent,
        Method getter,
        String id,
        int orderNumber)
    {
        this.source = source;
        this.parent = parent;
        this.getter = getter;
        this.orderNumber = orderNumber;
        this.id = id;
        if (CommonUtils.isEmpty(this.id)) {
            this.id = BeanUtils.getPropertyNameFromGetter(getter.getName());
        }

        declaringClass = parent == null ? getter.getDeclaringClass() : parent.getDeclaringClass();
        if (this.getter.getParameterTypes().length > 0 && getter.getParameterTypes()[0] == DBRProgressMonitor.class) {
            this.isLazy = true;
        }

        if (isLazy) {
            final LazyProperty lazyInfo = getter.getAnnotation(LazyProperty.class);
            if (lazyInfo != null) {
                try {
                    cacheValidator = lazyInfo.cacheValidator().getConstructor().newInstance();
                } catch (Exception e) {
                    log.warn("Can't instantiate lazy cache validator '" + lazyInfo.cacheValidator().getName() + "'", e);
                }
            }
        }
    }

    public Class<?> getDeclaringClass()
    {
        return declaringClass;
    }

    public DBPPropertySource getSource()
    {
        return source;
    }

    public int getOrderNumber()
    {
        return orderNumber;
    }

    @NotNull
    public String getId()
    {
        return id;
    }

    public <T extends Annotation> T getAnnotation(Class<T> annoType) {
        return getter == null ? null : getter.getAnnotation(annoType);
    }

    public Method getGetter()
    {
        return getter;
    }

    public boolean isNameProperty() {
        return id.equals(DBConstants.PROP_ID_NAME) || orderNumber == 1;
    }

    public boolean isDescriptionProperty() {
        return id.equals(DBConstants.PROP_ID_DESCRIPTION);
    }

    public boolean isRemote()
    {
        return isLazy || parent != null && parent.isRemote();
    }

    public boolean isLazy()
    {
        return isLazy;
    }

    public boolean isLazy(Object object, boolean checkParent)
    {
        if (isLazy && cacheValidator != null) {
            if (parent != null) {
                if (parent.isLazy(object, true)) {
                    return true;
                }
                try {
                    // Parent isn't lazy so use null progress monitor
                    object = parent.getGroupObject(object, null);
                } catch (Exception e) {
                    log.debug(e);
                    return true;
                }
            }
            return !cacheValidator.isPropertyCached(object, id);
        }
        return isLazy || (checkParent && parent != null && parent.isLazy(object, checkParent));
    }

    public IPropertyCacheValidator getCacheValidator()
    {
        return cacheValidator;
    }

    public ObjectPropertyGroupDescriptor getParent()
    {
        return parent;
    }

    public abstract String getCategory();

    public abstract String getDescription();

    public static List<ObjectPropertyDescriptor> extractAnnotations(
        @Nullable DBPPropertySource source,
        Class<?> theClass,
        IPropertyFilter filter,
        @Nullable String locale)
    {
        List<ObjectPropertyDescriptor> annoProps = new ArrayList<ObjectPropertyDescriptor>();
        extractAnnotations(source, null, theClass, annoProps, filter, locale);
        return annoProps;
    }

    public static List<ObjectPropertyDescriptor> extractAnnotations(
        DBPPropertySource source,
        Collection<Class<?>> classList,
        IPropertyFilter filter)
    {
        List<ObjectPropertyDescriptor> annoProps = new ArrayList<>();
        for (Class<?> objectClass : classList) {
            annoProps.addAll(ObjectAttributeDescriptor.extractAnnotations(source, objectClass, filter, null));
        }
        annoProps.sort(ATTRIBUTE_DESCRIPTOR_COMPARATOR);
        return annoProps;
    }

    static void extractAnnotations(
        @Nullable DBPPropertySource source,
        @Nullable ObjectPropertyGroupDescriptor parent,
        Class<?> theClass,
        List<ObjectPropertyDescriptor> annoProps,
        IPropertyFilter filter,
        @Nullable String locale)
    {
        Object object = source == null ? null : source.getEditableValue();
        Method[] methods = theClass.getMethods();
        Map<String, Method> passedNames = new HashMap<>();
        for (Method method : methods) {
            String methodFullName = method.getDeclaringClass().getName() + "." + method.getName();
            final Method prevMethod = passedNames.get(methodFullName);
            if (prevMethod != null) {
                // The same method but probably with another return type
                final Class<?> prevReturnType = prevMethod.getReturnType();
                final Class<?> newReturnType = method.getReturnType();
                if (newReturnType == null || prevReturnType == null || newReturnType == prevReturnType || !prevReturnType.isAssignableFrom(newReturnType)) {
                    continue;
                }
                // Let it another chance. New return types seems to be subclass of previous
            }
            final PropertyGroup propGroupInfo = method.getAnnotation(PropertyGroup.class);
            if (propGroupInfo != null && method.getReturnType() != null) {
                // Property group
                ObjectPropertyGroupDescriptor groupDescriptor = new ObjectPropertyGroupDescriptor(source, parent, method, propGroupInfo, filter, locale);
                annoProps.addAll(groupDescriptor.getChildren());
            } else {
                final Property propInfo = method.getAnnotation(Property.class);
                if (propInfo == null || !BeanUtils.isGetterName(method.getName()) || method.getReturnType() == null) {
                    continue;
                }
                // Single property
                ObjectPropertyDescriptor desc = new ObjectPropertyDescriptor(source, parent, propInfo, method, locale);
                if (filter != null && !filter.select(object, desc)) {
                    continue;
                }
                if (prevMethod != null) {
                    // Remove previous anno
                    annoProps.removeIf(
                        objectPropertyDescriptor -> objectPropertyDescriptor.getId().equals(desc.getId()));
                }
                annoProps.add(desc);
                passedNames.put(methodFullName, method);
            }
        }
        annoProps.sort(ATTRIBUTE_DESCRIPTOR_COMPARATOR);
    }

}
