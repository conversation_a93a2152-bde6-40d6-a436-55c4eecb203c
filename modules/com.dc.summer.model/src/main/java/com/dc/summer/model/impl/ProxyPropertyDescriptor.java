
package com.dc.summer.model.impl;

import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.meta.PropertyLength;

/**
 * ProxyPropertyDescriptor
*/
public class ProxyPropertyDescriptor implements DBPPropertyDescriptor
{

    protected final DBPPropertyDescriptor original;

    public ProxyPropertyDescriptor(DBPPropertyDescriptor original)
    {
        this.original = original;
    }

    @NotNull
    @Override
    public String getId()
    {
        return this.original.getId();
    }

    @Override
    public String getCategory()
    {
        return this.original.getCategory();
    }

    @Override
    public String getDescription()
    {
        return this.original.getDescription();
    }

    @Override
    public Class<?> getDataType() {
        return original.getDataType();
    }

    @Override
    public boolean isRequired() {
        return original.isRequired();
    }

    @Override
    public Object getDefaultValue() {
        return original.getDefaultValue();
    }

    @Override
    public boolean isEditable(Object object) {
        return original.isEditable(object);
    }

    @NotNull
    @Override
    public PropertyLength getLength() {
        return original.getLength();
    }

    @Nullable
    @Override
    public String[] getFeatures() {
        return original.getFeatures();
    }

    @Override
    public boolean hasFeature(@NotNull String feature) {
        return original.hasFeature(feature);
    }

    @NotNull
    @Override
    public String getDisplayName()
    {
        return this.original.getDisplayName();
    }


}
