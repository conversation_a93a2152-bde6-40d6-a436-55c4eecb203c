

package com.dc.summer.runtime.ui;

import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressListener;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

/**
 * Connections Service
 */
public interface UIServiceConnections {

    void openConnectionEditor(@NotNull DBPDataSourceContainer dataSourceContainer, @Nullable String defaultPageName);

    void connectDataSource(@NotNull DBPDataSourceContainer dataSourceContainer, DBRProgressListener onFinish);

    void disconnectDataSource(@NotNull DBPDataSourceContainer dataSourceContainer);

    void closeActiveTransaction(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionContext context, boolean commitTxn);

    boolean confirmTransactionsClose(@NotNull DBCExecutionContext[] contexts);

    boolean checkAndCloseActiveTransaction(@NotNull DBCExecutionContext[] contexts);

}