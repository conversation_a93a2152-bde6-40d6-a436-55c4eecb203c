package com.dc.summer.model.data.result;

import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.sql.SqlFieldData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class DataResultNodeCol extends DataResultNode {

    @Override
    public void accept(DataResultVisitor visitor) {
        visitor.visit(this);
    }

    private List<SqlFieldData> sqlFieldDataList;

    private DBDAttributeBinding[] bindings;

}
