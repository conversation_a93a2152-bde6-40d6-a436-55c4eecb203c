
package com.dc.summer.model.impl;

import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.struct.DBSTypedObject;

/**
 * SimpleTypedObject
 */
public class SimpleTypedObject implements DBSTypedObject {

    public static final SimpleTypedObject DEFAULT_TYPE = new SimpleTypedObject("Object");

    private String typeName;

    public SimpleTypedObject(String typeName) {
        this.typeName = typeName;
    }

    @Override
    public String getTypeName() {
        return typeName;
    }

    @Override
    public String getFullTypeName() {
        return getTypeName();
    }

    @Override
    public int getTypeID() {
        return 0;
    }

    @Override
    public DBPDataKind getDataKind() {
        return DBPDataKind.OBJECT;
    }

    @Override
    public Integer getScale() {
        return null;
    }

    @Override
    public Integer getPrecision() {
        return null;
    }

    @Override
    public long getMaxLength() {
        return 0;
    }

    @Override
    public long getTypeModifiers() {
        return 0;
    }
}