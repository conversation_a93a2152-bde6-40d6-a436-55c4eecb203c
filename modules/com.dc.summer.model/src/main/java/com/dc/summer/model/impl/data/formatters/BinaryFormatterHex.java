
package com.dc.summer.model.impl.data.formatters;

import com.dc.summer.model.data.DBDBinaryFormatter;
import com.dc.summer.utils.GeneralUtils;

/**
 * Hex formatter
 */
public class BinaryFormatterHex implements DBDBinaryFormatter {

    public static final BinaryFormatterHex INSTANCE = new BinaryFormatterHex();

    @Override
    public String getId()
    {
        return "hex";
    }

    @Override
    public String getTitle()
    {
        return "Hex";
    }

    @Override
    public String toString(byte[] bytes, int offset, int length)
    {
        return new String(toHexChars(bytes, offset, length));
    }

    protected static char[] toHexChars(byte[] bytes, int offset, int length) {
        char[] chars = new char[length * 2];
        for (int i = 0; i < length; i++) {
            String hex = GeneralUtils.byteToHex[bytes[offset + i] & 0x0ff];
            chars[i * 2] = hex.charAt(0);
            chars[i * 2 + 1] = hex.charAt(1);
        }
        return chars;
    }

    @Override
    public byte[] toBytes(String string)
    {
        int length = string.length();
        if (length > 0 && length % 2 != 0) {
            length--;
        }
        byte bytes[] = new byte[length / 2];
        for (int i = 0; i < length; i += 2) {
            bytes[i / 2] = (byte) ((Character.digit(string.charAt(i), 16) << 4)
                + Character.digit(string.charAt(i + 1), 16));
        }
        return bytes;
    }

}
