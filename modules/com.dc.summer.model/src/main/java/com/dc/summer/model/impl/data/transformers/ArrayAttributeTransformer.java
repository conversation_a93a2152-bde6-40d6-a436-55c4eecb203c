
package com.dc.summer.model.impl.data.transformers;

import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.data.DBDAttributeTransformer;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.struct.DBSDataType;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.summer.model.struct.DBSTypedObjectEx;

import java.util.List;
import java.util.Map;

/**
 * Transforms attribute of array type into hierarchy of attributes
 */
public class ArrayAttributeTransformer implements DBDAttributeTransformer {

    @Override
    public void transformAttribute(@NotNull DBCSession session, @NotNull DBDAttributeBinding attribute, @NotNull List<Object[]> rows, @NotNull Map<String, Object> options) throws DBException {
        if (!session.getDataSource().getContainer().getPreferenceStore().getBoolean(ModelPreferences.RESULT_TRANSFORM_COMPLEX_TYPES)) {
            return;
        }
        DBSDataType collectionType;
        if (attribute.getAttribute() instanceof DBSTypedObjectEx) {
            collectionType = ((DBSTypedObjectEx) attribute.getAttribute()).getDataType();
        } else {
            collectionType = DBUtils.resolveDataType(session.getProgressMonitor(), session.getDataSource(), attribute.getTypeName());
        }
        if (collectionType != null) {
            DBSDataType componentType = collectionType.getComponentType(session.getProgressMonitor());
            if (componentType instanceof DBSEntity) {
                ComplexTypeAttributeTransformer.createNestedTypeBindings(session, attribute, rows, componentType);
                return;
            }
        }
        // No component type found.
        // Array items should be resolved in a lazy mode
        MapAttributeTransformer.resolveMapsFromData(session, attribute, rows);
    }

}
