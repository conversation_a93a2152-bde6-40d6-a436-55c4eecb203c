
package com.dc.summer.model.auth;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;

import java.util.Map;

public interface SMAuthenticationManager {
    void updateAuthStatus(
        @NotNull String authId,
        @NotNull SMAuthStatus authStatus,
        @NotNull Map<String, Object> authInfo,
        @Nullable String error) throws DBException;

    SMAuthInfo finishAuthentication(@NotNull String authId) throws DBException;
}
