
package com.dc.summer.model.struct.cache;

import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.struct.DBSObject;

import java.util.List;

/**
 * Simple objects cache.
 * Doesn't provide any reading facilities, just stores objects
 */
public class SimpleObjectCache<OWNER extends DBSObject, OBJEC<PERSON> extends DBSObject> extends AbstractObjectCache<OWNER, OBJECT> {

    @NotNull
    @Override
    public List<OBJECT> getAllObjects(@NotNull DBRProgressMonitor monitor, @Nullable OWNER owner) {
        return getCachedObjects();
    }

    @Nullable
    @Override
    public OBJECT getObject(@NotNull DBRProgressMonitor monitor, @Nullable OWNER owner, @NotNull String name) {
        return getCachedObject(name);
    }
}
