
package com.dc.summer.model.security.user;


import com.dc.code.Nullable;

import java.util.Objects;
import java.util.Set;

public class SMAuthPermissions {
    @Nullable
    private final String userId;
    private final String sessionId;
    private final Set<String> permissions;

    public SMAuthPermissions(@Nullable String userId, String sessionId, Set<String> permissions) {
        this.userId = userId;
        this.permissions = permissions;
        this.sessionId = sessionId;
    }

    @Nullable
    public String getUserId() {
        return userId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public Set<String> getPermissions() {
        return permissions;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SMAuthPermissions that = (SMAuthPermissions) o;
        return Objects.equals(userId, that.userId) && Objects.equals(permissions, that.permissions);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, permissions);
    }
}
