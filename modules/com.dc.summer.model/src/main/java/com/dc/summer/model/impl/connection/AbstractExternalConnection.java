package com.dc.summer.model.impl.connection;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPExternalConnection;
import com.dc.summer.model.exec.DBCException;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;

@Slf4j
public abstract class AbstractExternalConnection implements DBPExternalConnection {

    protected final DBPDataSourceContainer container;

    protected AbstractExternalConnection(DBPDataSourceContainer container) {
        this.container = container;
    }

    @Override
    public Connection obtain(String url, Properties info) throws DBException {
        ensureRegisterDataSource(url, info, false);
        if (refreshDataSource(url, info) || checkDataSourceFailContinuousStatus()) {
            ensureRegisterDataSource(url, info, true);
        }
        try {
            return getConnection(info);
        } catch (Exception e) {
            throw new DBCException("扩展数据源无法打开连接。", e);
        }
    }

    private void ensureRegisterDataSource(String url, Properties info, boolean force) throws DBException {
        ConnectionPool pool = ConnectionPool.getInstance();

        if (!pool.hasDataSource(container.getId()) || force) {
            Connection connection = null;
            DataSource dataSource = null;
            Object lock = container.getExclusiveLock().acquireExclusiveLock();
            try {
                if (!pool.hasDataSource(container.getId()) || force) {

                    dataSource = openDataSource(url, info);
                    connection = dataSource.getConnection();

                    if (force) {
                        closeDataSource(getExecPool());
                    }

                    pool.registerDataSource(container.getId(), dataSource);
                }
            } catch (Exception e) {
                // 释放掉出错数据源
                if (dataSource != null) {
                    closeDataSource(dataSource);
                }
                throw new DBCException("无法打开扩展数据源。");
            } finally {
                // 释放掉测试连接
                if (connection != null) {
                    try {
                        connection.close();
                    } catch (Exception ignored) {
                        // nothing to do here
                    }
                }
                container.getExclusiveLock().releaseExclusiveLock(lock);
            }
        }

    }

    public DataSource getExecPool() {
        return ConnectionPool.getInstance().getDataSource(container.getId());
    }

    protected abstract Connection getConnection(Properties info) throws SQLException;

    protected abstract boolean checkDataSourceFailContinuousStatus();

    protected abstract DataSource openDataSource(String url, Properties info) throws Exception;

    protected abstract boolean refreshDataSource(String url, Properties info);

    protected abstract void closeDataSource(DataSource dataSource);

}
