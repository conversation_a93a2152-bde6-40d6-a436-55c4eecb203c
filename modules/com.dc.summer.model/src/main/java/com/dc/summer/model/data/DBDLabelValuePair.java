

package com.dc.summer.model.data;

import com.dc.code.NotNull;
import com.dc.summer.model.DBUtils;

/**
 * Label value pair
 */
public class DBDLabelValuePair implements Comparable {

    private final String label;
    private Object value;

    public DBDLabelValuePair(String label, Object value) {
        this.label = label;
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value + " (" + label + ")";
    }

    @Override
    public int compareTo(@NotNull Object o) {
        if (o instanceof DBDLabelValuePair) {
            final DBDLabelValuePair lvp = (DBDLabelValuePair) o;
            if (value == lvp.value) {
                return 0;
            }
            if (value == null) {
                return -1;
            }
            if (lvp.value == null) {
                return 1;
            }
            if (value instanceof Comparable && value.getClass() == lvp.value.getClass()) {
                return DBUtils.compareDataValues(value, lvp.value);
            }
        }
        return 0;
    }
}
