
package com.dc.summer.model.data;

import com.dc.summer.model.*;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.utils.CommonUtils;

/**
 * Collection element binding info
 */
public class DBDAttributeBindingElement extends DBDAttributeBindingNested {
    @NotNull
    private final DBDCollection collection;
    private final int index;

    public DBDAttributeBindingElement(
        @NotNull DBDAttributeBinding parent,
        @NotNull DBDCollection collection,
        int index)
    {
        super(parent, collection.getComponentValueHandler());
        this.collection = collection;
        this.index = index;
    }

    /**
     * Attribute index in result set
     * @return attribute index (zero based)
     */
    @Override
    public int getOrdinalPosition()
    {
        return 0;
    }

    @Override
    public boolean isRequired() {
        return false;
    }

    @Override
    public boolean isAutoGenerated() {
        return false;
    }

    @Override
    public boolean isPseudoAttribute() {
        return false;
    }

    @Nullable
    @Override
    public DBDCollection getSource() {
        return collection;
    }

    /**
     * Attribute label
     */
    @NotNull
    public String getLabel()
    {
        return getName();
    }

    @Nullable
    @Override
    public String getEntityName() {
        return null;
    }

    /**
     * Attribute name
     */
    @NotNull
    public String getName()
    {
        return String.valueOf(index + 1);
    }

    /**
     * Entity attribute
     */
    @Nullable
    public DBSEntityAttribute getEntityAttribute()
    {
        return null;
    }

    @Nullable
    @Override
    public Object extractNestedValue(@NotNull Object ownerValue, int itemIndex) throws DBCException {
        if (collection.isNull()) {
            // Can happen if values was released
            return null;
        }
        if (ownerValue instanceof DBDCollection) {
            return ((DBDCollection) ownerValue).get(index);
        }
        return collection.getItem(index);
    }

    @Override
    public String getTypeName() {
        return collection.getComponentType().getTypeName();
    }

    @Override
    public String getFullTypeName() {
        return DBUtils.getFullTypeName(collection.getComponentType());
    }

    @Override
    public int getTypeID() {
        return collection.getComponentType().getTypeID();
    }

    @Override
    public DBPDataKind getDataKind() {
        return collection.getComponentType().getDataKind();
    }

    @Override
    public Integer getScale() {
        return collection.getComponentType().getScale();
    }

    @Override
    public Integer getPrecision() {
        return collection.getComponentType().getPrecision();
    }

    @Override
    public long getMaxLength() {
        return collection.getComponentType().getMaxLength();
    }

    @Override
    public long getTypeModifiers() {
        return collection.getComponentType().getTypeModifiers();
    }

    @Override
    public String toString() {
        return collection.toString() + "@" + index;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj) && obj instanceof DBDAttributeBindingElement &&
            CommonUtils.equalObjects(collection, ((DBDAttributeBindingElement) obj).collection) &&
            index == ((DBDAttributeBindingElement) obj).index;
    }

    @Override
    public int hashCode() {
        return collection.hashCode() + index;
    }


}
