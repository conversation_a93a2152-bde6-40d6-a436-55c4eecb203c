
package com.dc.summer.model.struct;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.List;

/**
 * DBSEntityContainer
 * Smarter version of DBSObjectContainer
 */
public interface DBSEntityContainer extends DBSObjectContainer {

    DBSEntityTypeMapping[] getNestedEntityTypes();

    List<? extends DBSObject> getEntities(DBRProgressMonitor monitor, DBSEntityType entityType)
        throws DBException;

}
