

package com.dc.summer.utils;

import com.dc.summer.Log;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.utils.CommonUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * Preferences utilities
 */
public class PrefUtils {

    private static final Log log = Log.getLog(PrefUtils.class);

    public static void savePreferenceStore(DBPPreferenceStore store)
    {
        try {
            store.save();
        } catch (IOException e) {
            log.warn(e);
        }
    }

    public static void setDefaultPreferenceValue(DBPPreferenceStore store, String name, Object value)
    {
        if (CommonUtils.isEmpty(store.getDefaultString(name))) {
            store.setDefault(name, value.toString());
        }
    }

    public static Object getPreferenceValue(DBPPreferenceStore store, String propName, Class<?> valueType)
    {
        try {
            final String str = store.getString(propName);
            if (str == null) {
                return null;
            }
            if (valueType == null || CharSequence.class.isAssignableFrom(valueType)) {
                return CommonUtils.isEmpty(str) ? null : str;
            } else if (valueType == Boolean.class || valueType == Boolean.TYPE) {
                return CommonUtils.toBoolean(str);
            } else if (valueType == Long.class || valueType == Long.TYPE) {
                return CommonUtils.toLong(str);
            } else if (valueType == Integer.class || valueType == Integer.TYPE ||
                valueType == Short.class || valueType == Short.TYPE ||
                valueType == Byte.class || valueType == Byte.TYPE) {
                return CommonUtils.toInt(str);
            } else if (valueType == Double.class || valueType == Double.TYPE) {
                return CommonUtils.toDouble(str);
            } else if (valueType == Float.class || valueType == Float.TYPE) {
                return CommonUtils.toFloat(store);
            } else if (valueType == BigInteger.class) {
                return new BigInteger(str);
            } else if (valueType == BigDecimal.class) {
                return new BigDecimal(str);
            }
        } catch (RuntimeException e) {
            log.error(e);
        }
        final String string = store.getString(propName);
        return CommonUtils.isEmpty(string) ? null : string;
    }

    public static void setPreferenceValue(DBPPreferenceStore store, String propName, Object value)
    {
        if (value == null) {
            return;
        }
        if (value instanceof CharSequence) {
            store.setValue(propName, value.toString());
        } else if (value instanceof Boolean) {
            store.setValue(propName, (Boolean) value);
        } else if (value instanceof Long) {
            store.setValue(propName, (Long) value);
        } else if (value instanceof Integer || value instanceof Short || value instanceof Byte) {
            store.setValue(propName, ((Number) value).intValue());
        } else if (value instanceof Double) {
            store.setValue(propName, (Double) value);
        } else if (value instanceof Float) {
            store.setValue(propName, (Float) value);
        } else {
            store.setValue(propName, value.toString());
        }
    }

    public static void setPreferenceDefaultValue(DBPPreferenceStore store, String propName, Object value)
    {
        if (value == null) {
            return;
        }
        if (value instanceof CharSequence) {
            store.setDefault(propName, value.toString());
        } else if (value instanceof Boolean) {
            store.setDefault(propName, (Boolean) value);
        } else if (value instanceof Long) {
            store.setDefault(propName, (Long) value);
        } else if (value instanceof Integer || value instanceof Short || value instanceof Byte) {
            store.setDefault(propName, ((Number) value).intValue());
        } else if (value instanceof Double) {
            store.setDefault(propName, (Double) value);
        } else if (value instanceof Float) {
            store.setDefault(propName, (Float) value);
        } else {
            store.setDefault(propName, value.toString());
        }
    }
}
