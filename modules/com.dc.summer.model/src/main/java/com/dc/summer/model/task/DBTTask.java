
package com.dc.summer.model.task;

import com.dc.summer.model.DBPNamedObject;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPObjectWithDescription;
import com.dc.summer.model.app.DBPProject;

import java.nio.file.Path;
import java.util.Date;
import java.util.Map;

/**
 * Task configuration
 */
public interface DBTTask extends DBPNamedObject, DBPObjectWithDescription {

    @NotNull
    String getId();

    @NotNull
    DBPProject getProject();

    @NotNull
    Date getCreateTime();

    @NotNull
    Date getUpdateTime();

    @NotNull
    DBTTaskType getType();

    @Nullable
    DBTTaskFolder getTaskFolder();

    @NotNull
    Map<String, Object> getProperties();

    void setProperties(@NotNull Map<String, Object> properties);

    boolean isTemporary();

    @Nullable
    DBTTaskRun getLastRun();

    @NotNull
    DBTTaskRun[] getRunStatistics();

    @NotNull
    Path getRunLogFolder();

    @NotNull
    Path getRunLog(DBTTaskRun run);

    void removeRunLog(DBTTaskRun taskRun);

    void cleanRunStatistics();

    void refreshRunStatistics();
}
