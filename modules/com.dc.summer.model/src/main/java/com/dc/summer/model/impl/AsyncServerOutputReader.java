
package com.dc.summer.model.impl;

import com.dc.summer.Log;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.code.NotNull;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionResult;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;

public class AsyncServerOutputReader extends DefaultServerOutputReader {
    private static final Log log = Log.getLog(AsyncServerOutputReader.class);

        @Override
        public boolean isAsyncOutputReadSupported() {
            return true;
        }

        @Override
        public List<String> readServerOutput(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionContext context, DBCExecutionResult executionResult, DBCStatement statement, @NotNull PrintWriter output) throws DBCException {
            if (statement == null) {
                super.readServerOutput(monitor, context, executionResult, null, output);
            } else {
                // Do not read from connection warnings as it blocks statements cancelation and other connection-level stuff.
                // See #7885
/*
                try {
                    SQLWarning connWarning = ((JDBCSession) statement.getSession()).getWarnings();
                    if (connWarning != null) {
                        dumpWarnings(output, Collections.singletonList(connWarning));
                    }
                } catch (SQLException e) {
                    log.debug(e);
                }
*/

                Throwable[] statementWarnings = statement.getStatementWarnings();
                if (statementWarnings != null && statementWarnings.length > 0) {
                    return dumpWarnings(output, Arrays.asList(statementWarnings));
                }
            }
            return null;
        }
    }

