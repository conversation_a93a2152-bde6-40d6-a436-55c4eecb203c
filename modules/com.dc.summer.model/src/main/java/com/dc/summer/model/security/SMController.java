
package com.dc.summer.model.security;

import com.dc.summer.model.auth.SMAuthInfo;
import com.dc.summer.model.security.user.SMObjectPermissions;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.auth.SMAuthCredentialsManager;
import com.dc.summer.model.security.user.SMAuthPermissions;
import com.dc.summer.model.security.user.SMRole;
import com.dc.summer.model.security.user.SMUser;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Admin interface
 */
public interface SMController extends SMAuthCredentialsManager {

    ///////////////////////////////////////////
    // Users
    @NotNull
    SMRole[] getUserRoles(String userId) throws DBException;

    SMUser getUserById(String userId) throws DBException;

    Map<String, Object> getUserParameters(String userId) throws DBException;

    void setUserParameter(String userId, String name, Object value) throws DBException;

    ///////////////////////////////////////////
    // Credentials

    /**
     * Sets user credentials for specified provider
     */
    void setUserCredentials(@NotNull String userId, @NotNull String authProviderId, @NotNull Map<String, Object> credentials) throws DBException;

    /**
     * Returns list of auth provider IDs associated with this user
     */
    String[] getUserLinkedProviders(@NotNull String userId) throws DBException;

    ///////////////////////////////////////////
    // Permissions

    @NotNull
    Set<String> getSubjectPermissions(String subjectId) throws DBException;

    @NotNull
    Set<String> getUserPermissions(String userId) throws DBException;

    ///////////////////////////////////////////
    // Sessions

    boolean isSessionPersisted(String id) throws DBException;

    SMAuthInfo authenticateAnonymousUser(
        @NotNull String appSessionId,
        @NotNull Map<String, Object> sessionParameters,
        @NotNull SMSessionType sessionType
    ) throws DBException;

    SMAuthInfo authenticate(
        @NotNull String appSessionId,
        @Nullable String previousSmSessionId,
        @NotNull Map<String, Object> sessionParameters,
        @NotNull SMSessionType sessionType,
        @NotNull String authProviderId,
        @Nullable String authProviderConfigurationId,
        @NotNull Map<String, Object> userCredentials) throws DBException;

    SMAuthInfo getAuthStatus(@NotNull String authId) throws DBException;

    void updateSession(
        @NotNull String sessionId,
        @Nullable String userId,
        Map<String, Object> parameters) throws DBException;

    ///////////////////////////////////////////
    // Permissions

    SMAuthPermissions getTokenPermissions(String token) throws DBException;

    ///////////////////////////////////////////
    // Auth providers

    SMAuthProviderDescriptor[] getAvailableAuthProviders() throws DBException;

    @NotNull
    List<SMObjectPermissions> getAllAvailableObjectsPermissions(
        @NotNull String subjectId,
        @NotNull SMObjectType objectType
    ) throws DBException;

    void setObjectPermissions(
        @NotNull Set<String> objectIds,
        @NotNull SMObjectType objectType,
        @NotNull Set<String> subjectIds,
        @NotNull Set<String> permissions,
        @NotNull String grantor
    ) throws DBException;

    @NotNull
    List<SMObjectPermissionsGrant> getObjectPermissionGrants(
        @NotNull String objectId,
        @NotNull SMObjectType smObjectType
    ) throws DBException;

    @NotNull
    SMObjectPermissions getObjectPermissions(
        @NotNull String subjectId,
        @NotNull String objectId,
        @NotNull SMObjectType objectType
    ) throws DBException;
}
