
package com.dc.summer.model.struct.rdb;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.Collection;

/**
 * Procedures container
 */
public interface DBSProcedureContainer
{
    Collection<? extends DBSProcedure> getProcedures(DBRProgressMonitor monitor) throws DBException;

    DBSProcedure getProcedure(DBRProgressMonitor monitor, String uniqueName) throws DBException;
}