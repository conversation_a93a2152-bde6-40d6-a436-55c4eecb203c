<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.dataSourceProvider">

        <!-- BigQuery -->

        <datasource
                class="com.dc.summer.ext.bigquery.BigQueryDataSourceProvider"
                description="%datasource.bq.description"
                id="bigquery"
                parent="generic"
                label="Google BigQuery"
                icon="icons/bigquery_icon.png"
                dialect="google_bigquery">
            <drivers managable="true">

                <!-- Simba JDBC driver -->
                <driver
                        id="google_bigquery_jdbc_simba"
                        label="Google BigQuery"
                        icon="icons/bigquery_icon.png"
                        iconBig="icons/bigquery_icon_big.png"
                        class="com.simba.googlebigquery.jdbc42.Driver"
                        sampleURL="jdbc:bigquery://{host}:{port};ProjectId={database};OAuthType=0;OAuthServiceAcctEmail={user};OAuthPvtKeyPath={server};"
                        defaultHost="https://www.googleapis.com/bigquery/v2"
                        defaultPort="443"
                        description="Google BigQuery driver"
                        webURL="https://cloud.google.com/bigquery/partners/simba-drivers/"
                        propertiesURL="https://cdn.simba.com/products/BigQuery/doc/JDBC_InstallGuide/"
                        categories="bigdata"
                        singleConnection="true">
                    <file type="jar" path="https://storage.googleapis.com/simba-bq-release/jdbc/SimbaJDBCDriverforGoogleBigQuery42_1.2.21.1025.zip" bundle="!drivers.bigquery"/>/>
                    <file type="jar" path="drivers/bigquery" bundle="drivers.bigquery"/>/>

                    <property name="@summer-default-resultset.maxrows.sql" value="true"/>
                    <parameter name="supports-catalog-selection" value="false"/>
                    <parameter name="supports-schema-selection" value="false"/>
                </driver>

            </drivers>

        </datasource>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="google_bigquery" parent="generic" class="com.dc.summer.ext.bigquery.model.BigQuerySQLDialect" label="BigQuery" description="Google BigQuery." icon="icons/bigquery_icon.png">
        </dialect>
    </extension>

    <extension point="com.dc.summer.dataSourceAuth">
        <authModel
                id="google_bigquery"
                label="Google Cloud Auth"
                description="BigQuery internal authentication"
                class="com.dc.summer.ext.bigquery.auth.BigQueryAuthModel"
                default="true">
            <replace model="native"/>
            <datasource id="bigquery"/>
        </authModel>
    </extension>

</plugin>
