package com.dc.summer.ext.dm.plan;

import com.dc.summer.ext.dm.tree.ITreeNode;

public class PlanTree {
   private PlanTreeNode root;

   public PlanTree(PlanTreeNode root) {
      this.root = root;
   }

   public PlanTreeNode getRoot() {
      return this.root;
   }

   public void setRoot(PlanTreeNode root) {
      this.root = root;
   }

   public int getNodeDeepth(ITreeNode node) {
      return node == this.root ? 0 : this.getNodeDeepth(this.root, 1);
   }

   private int getNodeDeepth(ITreeNode node, int n) {
      ITreeNode[] var6;
      int var5 = (var6 = node.getChildren()).length;

      for(int var4 = 0; var4 < var5; ++var4) {
         ITreeNode childNode = var6[var4];
         if (childNode == node) {
            return n + 1;
         }

         int k = this.getNodeDeepth(childNode, n + 1);
         if (k != -1) {
            return k;
         }
      }

      return -1;
   }
}
