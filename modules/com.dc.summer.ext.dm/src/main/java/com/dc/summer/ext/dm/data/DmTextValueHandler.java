package com.dc.summer.ext.dm.data;

import com.dc.summer.ext.dm.model.utils.DmConstants;
import com.dc.summer.model.data.DBDContent;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCContentValueHandler;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCObjectValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;

public class DmTextV<PERSON>ue<PERSON>andler extends JDBCObjectValueHandler {

    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
        return resultSet.getString(index);
    }
}
