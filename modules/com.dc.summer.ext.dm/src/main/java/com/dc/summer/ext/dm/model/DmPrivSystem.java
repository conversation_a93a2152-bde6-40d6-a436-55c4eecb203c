package com.dc.summer.ext.dm.model;

import java.sql.ResultSet;

import com.dc.code.NotNull;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;

/**
 * DM Priv System
 * <AUTHOR>
 *
 */
public class DmPrivSystem extends DmPriv {
	private boolean defaultRole;

	public DmPrivSystem(DmGrantee user, ResultSet resultSet) {
		super(user, JDBCUtils.safeGetString(resultSet, "PRIVILEGE"), resultSet);
	}

	@NotNull
	@Override
	@Property(viewable = true, order = 2)
	public String getName() {
		return super.getName();
	}
}
