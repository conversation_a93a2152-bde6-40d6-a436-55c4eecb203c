package com.dc.summer.ext.dm.model;

import java.sql.ResultSet;
import java.util.Collection;

import com.dc.summer.DBException;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class DmTablePartition extends DmPartitionBase<DmTablePhysical> {

	protected DmTablePartition(
	        DmTablePhysical DmTable,
	        boolean subpartition,
	        ResultSet dbResult)
	    {
	        super(DmTable, subpartition, dbResult);
	    }

	@Association
	public Collection<DmTablePartition> getSubPartitions(DBRProgressMonitor monitor) throws DBException {
		return getParentObject().getSubPartitions(monitor, this);
	}
}
