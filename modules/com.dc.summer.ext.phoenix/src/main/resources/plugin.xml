<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.generic.meta">
        <meta id="phoenixServer" class="com.dc.summer.ext.phoenix.model.PhoenixMetaModel" driverClass="org.apache.phoenix.jdbc.PhoenixDriver" dialect="apache_phoenix"/>
    </extension>
    
 

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.ext.phoenix.model.data.PhoenixValueHandlerProvider"
                description="phoenix data types provider"
                id="com.dc.summer.ext.phoenix.model.data.PhoenixValueHandlerProvider"
                label="Phoenix data types provider">

            <datasource class="com.dc.summer.ext.phoenix.model.PhoenixDataSource"/>
            <type standard="ARRAY"/>
        </provider>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="apache_phoenix" parent="generic" class="com.dc.summer.ext.phoenix.model.PhoenixSQLDialect" label="Phoenix" description="Apache Phoenix SQL dialect." icon="platform:/plugin/com.dc.summer.ext.generic/icons/phoenix_icon.png">
        </dialect>
    </extension>

</plugin>
