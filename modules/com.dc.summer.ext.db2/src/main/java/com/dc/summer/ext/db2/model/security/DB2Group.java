/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2013-2015 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.db2.model.security;

import com.dc.summer.model.access.DBAUser;
import com.dc.summer.ext.db2.model.DB2DataSource;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.sql.ResultSet;

/**
 * DB2 Group
 * 
 * <AUTHOR>
 */
public class DB2Group extends DB2Grantee implements DBAUser {

    // -----------------------
    // Constructors
    // -----------------------

    public DB2Group(DBRProgressMonitor monitor, DB2DataSource dataSource, ResultSet resultSet)
    {
        super(monitor, dataSource, resultSet, "GRANTEE");
    }

    // -----------------------
    // Business Contract
    // -----------------------

    @Override
    public DB2AuthIDType getType()
    {
        return DB2AuthIDType.G;
    }

}
