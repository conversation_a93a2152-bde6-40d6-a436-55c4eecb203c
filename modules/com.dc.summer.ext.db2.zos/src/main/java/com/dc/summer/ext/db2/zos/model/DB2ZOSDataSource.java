
package com.dc.summer.ext.db2.zos.model;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class DB2ZOSDataSource extends GenericDataSource {

    private static final Log log = Log.getLog(DB2ZOSDataSource.class);

    public DB2ZOSDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel)
        throws DBException
    {
        super(monitor, container, metaModel, new DB2ZOSSQLDialect());
    }

}
