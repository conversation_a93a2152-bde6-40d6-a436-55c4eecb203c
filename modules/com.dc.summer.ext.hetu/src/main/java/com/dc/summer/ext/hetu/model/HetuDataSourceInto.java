package com.dc.summer.ext.hetu.model;

import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceInfo;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class HetuDataSourceInto extends JDBCDataSourceInfo {

    private final Gson gson = new GsonBuilder().serializeNulls().create();

    public HetuDataSourceInto(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {
        ArrayList<Map<String, Object>> returnList = new ArrayList<>();

        try (JDBCSession session = (JDBCSession) context.openSession(monitor, DBCExecutionPurpose.UTIL, "get schemas info")) {
            JDBCResultSet catalogs = session.getMetaData().getCatalogs();
            while (catalogs.next()) {
                Map<String, Object> returnMap = new LinkedHashMap<>();

                String catalogName = catalogs.getString("table_cat");
                returnMap.put("username", catalogName);
                returnMap.put("charset", "UTF8");
                returnMap.put("is_sys", 0);
                returnMap.put("def_dbo_name", "");

                List<Map<String, Object>> innerSchemaInfos = new ArrayList<>();
                JDBCResultSet schemas = session.getMetaData().getSchemas(catalogName, null);
                while (schemas.next()) {
                    String schemaName = schemas.getString("table_schem");

                    Map<String, Object> schemaInfo = new LinkedHashMap<>();
                    schemaInfo.put("username", schemaName);
                    schemaInfo.put("charset", returnMap.get("charset"));
                    schemaInfo.put("is_sys", returnMap.get("is_sys"));
                    schemaInfo.put("def_dbo_name", "");
                    schemaInfo.put("catalog_name", catalogName);
                    schemaInfo.put("count", 0L);

                    innerSchemaInfos.add(schemaInfo);
                }

                returnMap.put("innerSchemaInfos", innerSchemaInfos);
                returnMap.put("count", 0L);

                returnList.add(returnMap);
            }
        } catch (Throwable e) {
            log.error("get schemas info error", e);
            throw new RuntimeException("get schemas info error ! ", e);
        }

        return returnList;
    }


    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {
        if (list == null) {
            return "";
        }

        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {
            if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString()) && item.getFieldType().toUpperCase(Locale.ROOT).contains("ROW(")) {
                try {
                    String fileType = item.getFieldType();
                    String typeString = fileType.substring(fileType.indexOf("(") + 1, fileType.lastIndexOf(")"));
                    String[] typeStrings = typeString.split(",");
                    Map<String, String> keyAndType = new HashMap<>(typeStrings.length);
                    for (String type : typeStrings) {
                        String[] nameAndType = type.trim().split(" ");
                        keyAndType.put(nameAndType[0], nameAndType[1]);
                    }
                    Map<String, Object> map = gson.fromJson(item.getFieldValue().toString(), Map.class);
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append("ROW(");
                    for (String key : map.keySet()) {
                        if ("integer".equalsIgnoreCase(keyAndType.get(key))) {
                            String value = map.get(key).toString();
                            if (map.get(key).toString().contains(".")) {
                                value = map.get(key).toString().substring(0, map.get(key).toString().indexOf("."));
                            }
                            stringBuilder.append(Integer.parseInt(value));
                        } else {
                            stringBuilder.append(String.format("'%s'", map.get(key)));
                        }
                        stringBuilder.append(",");
                    }
                    if (!map.isEmpty()) {
                        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
                    }
                    stringBuilder.append(")");
                    data.add(stringBuilder.toString());
                } catch (Exception e) {
                    log.error("格式化hetu row类型失败！", e);
                    data.add(String.format("'%s'", item.getFieldValue()));
                }
            } else if (null != item.getFieldValue() &&
                    StringUtils.isNotBlank(item.getFieldValue().toString()) &&
                    item.getFieldType().toUpperCase(Locale.ROOT).contains("VARCHAR")) {
                data.add(String.format("'%s'", item.getFieldValue()));
            } else if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                String columnType = item.getFieldType().toUpperCase(Locale.ROOT);
                if (columnType.contains("(")) {
                    columnType = columnType.split("\\(")[0];
                }
                data.add(String.format("%s '%s'", columnType, item.getFieldValue()));
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String columns = StringUtils.join(fields, "\",\"");
        String values = StringUtils.join(data, ",");

        String tablePublic = null;
        String tableTrueName = null;
        if (tableName.contains(".")) {
            String[] tables = tableName.split("\\.");
            tablePublic = tables[0];
            tableTrueName = tables[1];
        } else {
            tableTrueName = tableName;
        }

        if (StringUtils.isNotBlank(tablePublic)) {
            tableTrueName = String.format("%s\".\"%s", tablePublic, tableTrueName);
        }

        if (StringUtils.isNotEmpty(schemaName)) {
            if (schemaName.contains(".")) {
                String[] names = schemaName.split("\\.");
                schemaName = String.format("%s\".\"%s", names[0], names[1]);
            }
            return String.format("INSERT INTO \"%s\".\"%s\" (%s) VALUES (%s)", schemaName, tableTrueName, "\"" + columns + "\"", values);
        }
        return String.format("INSERT INTO \"%s\" (%s) VALUES (%s)", tableTrueName, "\"" + columns + "\"", values);
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }

        String fields = list.get(0).stream().map(SqlFieldData::getFieldName).collect(Collectors.joining("\",\""));

        String values = list.stream().map(sqlFieldDataList -> {
            List<String> data = new ArrayList<>();
            for (SqlFieldData item : sqlFieldDataList) {
                if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString()) && item.getFieldType().toUpperCase(Locale.ROOT).contains("ROW(")) {
                    try {
                        String fileType = item.getFieldType();
                        String typeString = fileType.substring(fileType.indexOf("(") + 1, fileType.lastIndexOf(")"));
                        String[] typeStrings = typeString.split(",");
                        Map<String, String> keyAndType = new HashMap<>(typeStrings.length);
                        for (String type : typeStrings) {
                            String[] nameAndType = type.trim().split(" ");
                            keyAndType.put(nameAndType[0], nameAndType[1]);
                        }
                        Map<String, Object> map = gson.fromJson(item.getFieldValue().toString(), Map.class);
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("ROW(");
                        for (String key : map.keySet()) {
                            if ("integer".equalsIgnoreCase(keyAndType.get(key))) {
                                String value = map.get(key).toString();
                                if (map.get(key).toString().contains(".")) {
                                    value = map.get(key).toString().substring(0, map.get(key).toString().indexOf("."));
                                }
                                stringBuilder.append(Integer.parseInt(value));
                            } else {
                                stringBuilder.append(String.format("'%s'", map.get(key)));
                            }
                            stringBuilder.append(",");
                        }
                        if (!map.isEmpty()) {
                            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
                        }
                        stringBuilder.append(")");
                        data.add(stringBuilder.toString());
                    } catch (Exception e) {
                        log.error("格式化hetu row类型失败！", e);
                        data.add(String.format("'%s'", item.getFieldValue()));
                    }
                } else if (null != item.getFieldValue() &&
                        StringUtils.isNotBlank(item.getFieldValue().toString()) &&
                        item.getFieldType().toUpperCase(Locale.ROOT).contains("VARCHAR")) {
                    data.add(String.format("'%s'", item.getFieldValue()));
                } else if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                    String columnType = item.getFieldType().toUpperCase(Locale.ROOT);
                    if (columnType.contains("(")) {
                        columnType = columnType.split("\\(")[0];
                    }
                    data.add(String.format("%s '%s'", columnType, item.getFieldValue()));
                } else {
                    data.add(String.format("%s", "NULL"));
                }
            }

            return String.format("(%s)", StringUtils.join(data, ","));
        }).collect(Collectors.joining(","));

        if (StringUtils.isNotEmpty(schemaName)) {
            if (schemaName.contains(".")) {
                String[] names = schemaName.split("\\.");
                schemaName = String.format("%s\".\"%s", names[0], names[1]);
            }
            return String.format("INSERT INTO \"%s\".\"%s\" (%s) VALUES %s", schemaName, tableName, "\"" + fields + "\"", values);
        }
        return String.format("INSERT INTO \"%s\" (%s) VALUES %s", tableName, "\"" + fields + "\"", values);
    }

    @Override
    public boolean isSupportsBooleanQuoted() {
        return false;
    }

    @Override
    public boolean isNeedAppendColumnType() {
        return true;
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {

        String tablePublic = null;
        String tableTrueName = null;
        if (tableName.contains(".")) {
            String[] tables = tableName.split("\\.");
            tablePublic = tables[0];
            tableTrueName = tables[1];
        } else {
            tableTrueName = tableName;
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(tablePublic)) {
            tableTrueName = String.format("%s\".\"%s", tablePublic, tableTrueName);
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(schemaName)) {
            if (schemaName.contains(".")) {
                String[] names = schemaName.split("\\.");
                schemaName = String.format("%s\".\"%s", names[0], names[1]);
            }
            return String.format("TRUNCATE TABLE \"%s\".\"%s\"", schemaName, tableTrueName);
        } else {
            return String.format("TRUNCATE TABLE \"%s\"", tableName);
        }
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        String[] objectName = schemaName.split("\\.");
        String tableCatalog = objectName[0];
        String tableSchema = objectName[1];

        return "select \n" +
                "    table_cat,\n" +
                "    table_schem,\n" +
                "    table_name,\n" +
                "    column_name,\n" +
                "    type_name as \"column_type\",\n" +
                "    type_name as \"data_type\",\n" +
                "    remarks as \"comments\" ,\n" +
                "    char_octet_length,\n" +
                "    column_size as \"data_length\",\n" +
                "    is_nullable, \n" +
                "    column_def,\n" +
                "    decimal_digits,\n" +
                "    IS_AUTOINCREMENT\n" +
                "from system.jdbc.columns\n" +
                "where table_cat = '" + tableCatalog + "' and table_schem = '" + tableSchema + "' and table_name = '" + tableName + "'\n" +
                "ORDER BY ORDINAL_POSITION ASC";
    }

    @Override
    public boolean supportsCatalog() {
        return true;
    }

    @Override
    public boolean supportsTableColumnSQL() {
        return true;
    }
}
