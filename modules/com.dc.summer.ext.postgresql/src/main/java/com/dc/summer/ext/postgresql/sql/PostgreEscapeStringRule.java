
package com.dc.summer.ext.postgresql.sql;

import com.dc.summer.model.text.parser.*;
import com.dc.summer.model.sql.parser.tokens.SQLTokenType;
import com.dc.summer.model.text.parser.*;

/**
 * This rule matches string literals with C-Style escapes, as
 * described in <b>4.1.2.2</b> chapter of PostgreSQL documentation.
 *
 * @see <a href="https://www.postgresql.org/docs/current/sql-syntax-lexical.html#SQL-SYNTAX-STRINGS-ESCAPE">4.1.2.2. String Constants with C-Style Escapes</a>
 */
public class PostgreEscapeStringRule implements TPPredicateRule {
    private final TPToken stringToken = new TPTokenDefault(SQLTokenType.T_STRING);

    @Override
    public TPToken getSuccessToken() {
        return stringToken;
    }

    @Override
    public TPToken evaluate(TPCharacterScanner scanner, boolean resume) {
        int ch;
        int chRead = 2;

        if (scanner.getColumn() > 0) {
            scanner.unread();
            if (Character.isLetterOrDigit(ch = scanner.read()) || ch == '_') {
                // Previous character is a part of identifier, we
                // don't want to take a bite of it by accident
                return TPTokenAbstract.UNDEFINED;
            }
        }

        if ((ch = scanner.read()) != 'e' && ch != 'E') {
            scanner.unread();
            return TPTokenAbstract.UNDEFINED;
        }

        if (scanner.read() != '\'') {
            scanner.unread();
            scanner.unread();
            return TPTokenAbstract.UNDEFINED;
        }

        do {
            ch = scanner.read();
            chRead++;

            if (ch == '\\') {
                ch = scanner.read();
                chRead++;

                if (ch == '\'') {
                    // Don't care about other escape sequences
                    continue;
                }
            }

            if (ch == '\'') {
                return stringToken;
            }
        } while (ch != TPCharacterScanner.EOF);

        while (chRead-- > 0) {
            scanner.unread();
        }

        return TPTokenAbstract.UNDEFINED;
    }

    @Override
    public TPToken evaluate(TPCharacterScanner scanner) {
        return evaluate(scanner, false);
    }
}
