
package com.dc.summer.ext.postgresql.tasks;

import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.code.NotNull;
import com.dc.summer.ext.postgresql.model.PostgreTableBase;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.struct.DBSObject;

import java.util.List;

public class PostgreToolBaseAnalyze extends PostgreToolWithStatus<DBSObject, PostgreToolBaseAnalyzeSettings> {
    @NotNull
    @Override
    public PostgreToolBaseAnalyzeSettings createToolSettings() {
        return new PostgreToolBaseAnalyzeSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, PostgreToolBaseAnalyzeSettings settings, List<DBEPersistAction> queries, DBSObject object) throws DBCException {
        String sql = "ANALYZE VERBOSE";
        if(object instanceof PostgreTableBase){
            PostgreTableBase postObject = (PostgreTableBase) object;
            sql += " " + postObject.getFullyQualifiedName(DBPEvaluationContext.DDL);
        }
        queries.add(new SQLDatabasePersistAction(sql));
    }
}
