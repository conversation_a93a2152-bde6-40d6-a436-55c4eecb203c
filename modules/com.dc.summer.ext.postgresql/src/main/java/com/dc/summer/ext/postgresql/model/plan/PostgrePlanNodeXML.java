
package com.dc.summer.ext.postgresql.model.plan;

import com.dc.summer.model.DBPDataSource;
import com.dc.utils.xml.XMLUtils;
import org.w3c.dom.Element;
import org.w3c.dom.Node;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Postgre execution plan node
 */
public class PostgrePlanNodeXML extends PostgrePlanNodeBase<PostgrePlanNodeXML> {

    public PostgrePlanNodeXML(DBPDataSource dataSource, PostgrePlanNodeXML parent, Element element) {
        super(dataSource, parent);

        Map<String, String> attributes = new LinkedHashMap<>();
        for (Node child = element.getFirstChild(); child != null; child = child.getNextSibling()) {
            if (child instanceof Element && !"Plans".equals(child.getNodeName())) {
                attributes.put(child.getNodeName(), child.getTextContent());
            }
        }
        setAttributes(attributes);

        Element nestedPlansElement = XMLUtils.getChildElement(element, "Plans");
        if (nestedPlansElement != null) {
            for (Element planElement : XMLUtils.getChildElementList(nestedPlansElement, "Plan")) {
                nested.add(new PostgrePlanNodeXML(dataSource, null, planElement));
            }
        }
    }

}
