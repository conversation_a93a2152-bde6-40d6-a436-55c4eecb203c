
package com.dc.summer.ext.postgresql.model.session;

import com.dc.summer.model.admin.sessions.AbstractServerSession;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.utils.CommonUtils;

import java.sql.ResultSet;
import java.util.Date;
import java.util.Objects;

/**
 * PostgreSQL session
 */
public class PostgreSession extends AbstractServerSession {
    private static final String CAT_CLIENT = "Client";
    private static final String CAT_TIMING = "Timings";

    private final int pid;
    private String user;
    private String clientHost;
    private String clientPort;
    private final String db;
    private String query;
    private Date backendStart;
    private Date xactStart;
    private Date queryStart;
    private Date stateChange;
    private String state;
    private String appName;

    public PostgreSession(ResultSet dbResult) {
        this.pid = JDBCUtils.safeGetInt(dbResult, "pid");
        this.user = JDBCUtils.safeGetString(dbResult, "usename");
        this.clientHost = JDBCUtils.safeGetString(dbResult, "client_hostname");
        if (CommonUtils.isEmpty(this.clientHost)) {
            this.clientHost = JDBCUtils.safeGetString(dbResult, "client_addr");
        }
        this.clientPort = JDBCUtils.safeGetString(dbResult, "client_port");
        this.db = JDBCUtils.safeGetString(dbResult, "datname");
        this.query = JDBCUtils.safeGetString(dbResult, "query");

        this.backendStart = JDBCUtils.safeGetTimestamp(dbResult, "backend_start");
        this.xactStart = JDBCUtils.safeGetTimestamp(dbResult, "xact_start");
        this.queryStart = JDBCUtils.safeGetTimestamp(dbResult, "query_start");
        this.stateChange = JDBCUtils.safeGetTimestamp(dbResult, "state_change");

        this.state = JDBCUtils.safeGetString(dbResult, "state");
        this.appName = JDBCUtils.safeGetString(dbResult, "application_name");
    }

    @Property(viewable = true, order = 1)
    public int getPid()
    {
        return pid;
    }

    @Property(viewable = true, category = CAT_CLIENT, order = 2)
    public String getUser()
    {
        return user;
    }

    @Property(viewable = false, category = CAT_CLIENT, order = 3)
    public String getClientHost()
    {
        return clientHost;
    }

    @Property(viewable = false, category = CAT_CLIENT, order = 4)
    public String getClientPort() {
        return clientPort;
    }

    @Property(viewable = true, order = 5)
    public String getDb()
    {
        return db;
    }

    @Property(viewable = true, category = CAT_CLIENT, order = 6)
    public String getAppName() {
        return appName;
    }

    @Property(viewable = false, category = CAT_TIMING, order = 30)
    public Date getBackendStart() {
        return backendStart;
    }

    @Property(viewable = false, category = CAT_TIMING, order = 31)
    public Date getXactStart() {
        return xactStart;
    }

    @Property(viewable = true, category = CAT_TIMING, order = 32)
    public Date getQueryStart() {
        return queryStart;
    }

    @Property(viewable = false, category = CAT_TIMING, order = 33)
    public Date getStateChange() {
        return stateChange;
    }

    @Property(viewable = true, order = 7)
    public String getState()
    {
        return state;
    }

    @Property(viewable = true, order = 100)
    public String getBriefQuery() {
        if (query != null && query.length() > 50) {
            return CommonUtils.truncateString(query, 50) + " ...";
        } else {
            return query;
        }
    }

    @Override
    public String getActiveQuery()
    {
        return query;
    }

    @Override
    public String toString()
    {
        if (!CommonUtils.isEmpty(db)) {
            return pid + "@" + db;
        } else {
            return String.valueOf(pid);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PostgreSession that = (PostgreSession) o;
        return pid == that.pid && Objects.equals(db, that.db);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pid, db);
    }
}
