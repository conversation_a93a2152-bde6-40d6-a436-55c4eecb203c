
package com.dc.summer.ext.postgresql.model;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * PostgreOperatorFamily
 */
public class PostgreOperatorFamily extends PostgreInformation {

    private long oid;
    private PostgreAccessMethod accessMethod;
    private String name;
    private long namespaceId;
    private long ownerId;

    public PostgreOperatorFamily(PostgreAccessMethod accessMethod, ResultSet dbResult)
        throws SQLException
    {
        super(accessMethod.getDatabase());
        this.accessMethod = accessMethod;
        this.loadInfo(dbResult);
    }

    private void loadInfo(ResultSet dbResult)
        throws SQLException
    {
        this.oid = JDBCUtils.safeGetLong(dbResult, "oid");
        this.name = JDBCUtils.safeGetString(dbResult, "opfname");
        this.namespaceId = JDBCUtils.safeGetLong(dbResult, "opfnamespace");
        this.ownerId = JDBCUtils.safeGetLong(dbResult, "opfowner");
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName()
    {
        return name;
    }

    @Property(viewable = true, order = 2)
    @Override
    public long getObjectId() {
        return oid;
    }

    @Property(viewable = true, order = 3)
    public PostgreSchema getNamespace(DBRProgressMonitor monitor) throws DBException {
        return accessMethod.getDatabase().getSchema(monitor, namespaceId);
    }

    @Property(viewable = true, order = 4)
    public PostgreRole getOwner(DBRProgressMonitor monitor) throws DBException {
        return accessMethod.getDatabase().getRoleById(monitor, ownerId);
    }

}

