
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.PostgreUtils;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.struct.RelationalObjectType;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSObjectType;
import com.dc.utils.CommonUtils;

import java.sql.ResultSet;
import java.util.Collection;
import java.util.Map;

/**
 * PostgreMaterializedView
 */
public class PostgreMaterializedView extends PostgreViewBase
{
    private boolean withData;
    private long tablespaceId;

    public PostgreMaterializedView(PostgreSchema catalog) {
        super(catalog);
    }

    public PostgreMaterializedView(
        PostgreSchema catalog,
        ResultSet dbResult)
    {
        super(catalog, dbResult);
        this.withData = JDBCUtils.safeGetBoolean(dbResult, "relispopulated");
        this.tablespaceId = JDBCUtils.safeGetLong(dbResult, "reltablespace");
    }

    public boolean isWithData() {
        return withData;
    }

    public void setWithData(boolean withData) {
        this.withData = withData;
    }

    @Override
    public Collection<PostgreIndex> getIndexes(DBRProgressMonitor monitor) throws DBException
    {
        return getSchema().getIndexCache().getObjects(monitor, getSchema(), this);
    }

    @Override
    protected String readExtraDefinition(JDBCSession session, Map<String, Object> options) throws DBException {
        Collection<PostgreIndex> indexes = getIndexes(session.getProgressMonitor());
        if (!CommonUtils.isEmpty(indexes)) {
            StringBuilder indexDefs = new StringBuilder("\n-- View indexes:\n");
            for (PostgreIndex index : indexes) {
                String indexDefinition = index.getObjectDefinitionText(session.getProgressMonitor(), options);
                indexDefs.append(indexDefinition).append(";\n");
            }
            return indexDefs.toString();
        }
        return null;
    }

    public String getTableTypeName() {
        return "MATERIALIZED VIEW";
    }

    @Property(viewable = true, editable = true, updatable = true, order = 20, listProvider = PostgreTableBase.TablespaceListProvider.class)
    public PostgreTablespace getTablespace(DBRProgressMonitor monitor) throws DBException {
        if (tablespaceId == 0) {
            return getDatabase().getDefaultTablespace(monitor);
        }
        return PostgreUtils.getObjectById(monitor, getDatabase().tablespaceCache, getDatabase(), tablespaceId);
    }

    public void setTablespace(PostgreTablespace tablespace) {
        this.tablespaceId = tablespace.getObjectId();
    }


    @Override
    public DBSObjectType getObjectType() {
        return RelationalObjectType.TYPE_MATERIALIZED_VIEW;
    }
}
