/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2022 DBeaver Corp and others
 * Copyright (C) 2011-2012 <PERSON> (<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.postgresql.tasks;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.preferences.DBPPreferenceMap;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.tasks.nativetool.AbstractScriptExecuteSettings;
import com.dc.summer.ext.postgresql.model.PostgreDatabase;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.lang.reflect.InvocationTargetException;

public class PostgreScriptExecuteSettings extends AbstractScriptExecuteSettings<DBSObject> {

    private static final Log log = Log.getLog(PostgreScriptExecuteSettings.class);

    private PostgreDatabase database;

    public PostgreDatabase getDatabase() {
        return database;
    }

    public void setDatabase(PostgreDatabase database) {
        this.database = database;
    }

    @Override
    public void loadSettings(DBRRunnableContext runnableContext, DBPPreferenceStore store) throws DBException {
        super.loadSettings(runnableContext, store);
        if (store instanceof DBPPreferenceMap) {
            String databaseId = store.getString("pg.script.database");

            if (!CommonUtils.isEmpty(databaseId)) {
                try {
                    runnableContext.run(true, true, monitor -> {
                        try {
                            database = (PostgreDatabase) DBUtils.findObjectById(monitor, getProject(), databaseId);
                            if (database == null) {
                                throw new DBException("Database " + databaseId + " not found");
                            }
                        } catch (Throwable e) {
                            throw new InvocationTargetException(e);
                        }
                    });
                } catch (InvocationTargetException e) {
                    log.error("Error loading objects configuration", e);
                } catch (InterruptedException e) {
                    // Ignore
                }
            } else {
                for (DBSObject object : getDatabaseObjects()) {
                    if (object instanceof PostgreDatabase) {
                        database = (PostgreDatabase) object;
                        break;
                    }
                }
            }
        }

        if (database == null) {
            throw new DBException("Cannot find database for script execution");
        }
    }

    @Override
    public void saveSettings(DBRRunnableContext runnableContext, DBPPreferenceStore store) {
        super.saveSettings(runnableContext, store);

        store.setValue("pg.script.database", DBUtils.getObjectFullId(database));
    }
}
