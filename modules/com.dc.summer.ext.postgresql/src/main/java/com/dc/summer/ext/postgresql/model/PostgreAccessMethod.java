
package com.dc.summer.ext.postgresql.model;

import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.impl.jdbc.cache.JDBCObjectCache;
import com.dc.summer.model.meta.Association;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.PostgreUtils;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.meta.Property;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;

/**
 * PostgreAccessMethod
 */
public class PostgreAccessMethod extends PostgreInformation {

    public static final String CAT_ROUTINES = "Routines";
    public static final String CAT_FLAGS = "Flags";

    private long oid;
    private String name;

    private String handler;
    private String type;

    private int operatorStrategies;
    private int supportRoutines;
    private boolean canOrder;
    private boolean canOrderByOp;
    private boolean canBackward;
    private boolean canUnique;
    private boolean canMultiCol;
    private boolean optionalKey;
    private boolean searchArray;
    private boolean searchNulls;
    private boolean storage;
    private boolean clusterable;
    private boolean predLocks;
    private final OperatorFamilyCache operatorFamilyCache = new OperatorFamilyCache();
    private final OperatorClassCache operatorClassCache = new OperatorClassCache();
    
    public PostgreAccessMethod(PostgreDatabase database, ResultSet dbResult)
        throws SQLException
    {
        super(database);
        this.loadInfo(dbResult);
    }

    private void loadInfo(ResultSet dbResult)
        throws SQLException
    {
        this.oid = JDBCUtils.safeGetLong(dbResult, "oid");
        this.name = JDBCUtils.safeGetString(dbResult, "amname");
        if (getDataSource().isServerVersionAtLeast(9, 6)) {
            // New simpler version of pg_am
            this.handler = JDBCUtils.safeGetString(dbResult, "amhandler");
            this.type = JDBCUtils.safeGetString(dbResult, "amtype");
        } else {
            this.operatorStrategies = JDBCUtils.safeGetInt(dbResult, "amstrategies");
            this.supportRoutines = JDBCUtils.safeGetInt(dbResult, "amsupport");
            this.canOrder = JDBCUtils.safeGetBoolean(dbResult, "amcanorder");
            this.canOrderByOp = JDBCUtils.safeGetBoolean(dbResult, "amcanorderbyop");
            this.canBackward = JDBCUtils.safeGetBoolean(dbResult, "amcanbackward");
            this.canUnique = JDBCUtils.safeGetBoolean(dbResult, "amcanunique");
            this.canMultiCol = JDBCUtils.safeGetBoolean(dbResult, "amcanmulticol");
            this.optionalKey = JDBCUtils.safeGetBoolean(dbResult, "amoptionalkey");
            this.searchArray = JDBCUtils.safeGetBoolean(dbResult, "amsearcharray");
            this.searchNulls = JDBCUtils.safeGetBoolean(dbResult, "amsearchnulls");
            this.storage = JDBCUtils.safeGetBoolean(dbResult, "amstorage");
            this.clusterable = JDBCUtils.safeGetBoolean(dbResult, "amclusterable");
            this.predLocks = JDBCUtils.safeGetBoolean(dbResult, "ampredlocks");
        }
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName()
    {
        return name;
    }

    @Property(viewable = true, order = 2)
    @Override
    public long getObjectId() {
        return oid;
    }

    @Property(viewable = true, order = 3)
    public String getHandler() {
        return handler;
    }

    @Property(viewable = true, order = 4)
    public String getType() {
        return type;
    }

    @Property(category = CAT_ROUTINES, order = 100)
    public int getOperatorStrategies() {
        return operatorStrategies;
    }

    @Property(category = CAT_ROUTINES, order = 101)
    public int getSupportRoutines() {
        return supportRoutines;
    }

    @Property(category = CAT_FLAGS, order = 200)
    public boolean isCanOrder() {
        return canOrder;
    }

    @Property(category = CAT_FLAGS, order = 201)
    public boolean isCanOrderByOp() {
        return canOrderByOp;
    }

    @Property(category = CAT_FLAGS, order = 202)
    public boolean isCanBackward() {
        return canBackward;
    }

    @Property(category = CAT_FLAGS, order = 203)
    public boolean isCanUnique() {
        return canUnique;
    }

    @Property(category = CAT_FLAGS, order = 204)
    public boolean isCanMultiCol() {
        return canMultiCol;
    }

    @Property(category = CAT_FLAGS, order = 205)
    public boolean isOptionalKey() {
        return optionalKey;
    }

    @Property(category = CAT_FLAGS, order = 206)
    public boolean isSearchArray() {
        return searchArray;
    }

    @Property(category = CAT_FLAGS, order = 207)
    public boolean isSearchNulls() {
        return searchNulls;
    }

    @Property(category = CAT_FLAGS, order = 208)
    public boolean isStorage() {
        return storage;
    }

    @Property(category = CAT_FLAGS, order = 209)
    public boolean isClusterable() {
        return clusterable;
    }

    @Property(category = CAT_FLAGS, order = 210)
    public boolean isPredLocks() {
        return predLocks;
    }

    @Association
    public Collection<PostgreOperatorClass> getOperatorClasses(DBRProgressMonitor monitor) throws DBException {
        return operatorClassCache.getAllObjects(monitor, this);
    }

    public PostgreOperatorClass getOperatorClass(DBRProgressMonitor monitor, long oid) throws DBException {
        return PostgreUtils.getObjectById(monitor, operatorClassCache, this, oid);
    }

    @Association
    public Collection<PostgreOperatorFamily> getOperatorFamilies(DBRProgressMonitor monitor) throws DBException {
        return operatorFamilyCache.getAllObjects(monitor, this);
    }

    public PostgreOperatorFamily getOperatorFamily(DBRProgressMonitor monitor, long oid) throws DBException {
        return PostgreUtils.getObjectById(monitor, operatorFamilyCache, this, oid);
    }

    static class OperatorClassCache extends JDBCObjectCache<PostgreAccessMethod, PostgreOperatorClass> {

        @NotNull
        @Override
        protected JDBCStatement prepareObjectsStatement(@NotNull JDBCSession session, @NotNull PostgreAccessMethod owner)
                throws SQLException
        {

            return session.prepareStatement("SELECT oc.oid,oc.* FROM @_catalog.@_opclass oc ORDER BY oc.oid"
                    .replace("@", owner.getDataSource().getInstancePrefix()));
        }

        @Override
        protected PostgreOperatorClass fetchObject(@NotNull JDBCSession session, @NotNull PostgreAccessMethod owner, @NotNull JDBCResultSet dbResult)
                throws SQLException, DBException
        {
            return new PostgreOperatorClass(owner, dbResult);
        }
    }

    static class OperatorFamilyCache extends JDBCObjectCache<PostgreAccessMethod, PostgreOperatorFamily> {

        @NotNull
        @Override
        protected JDBCStatement prepareObjectsStatement(@NotNull JDBCSession session, @NotNull PostgreAccessMethod owner)
                throws SQLException
        {
            return session.prepareStatement(
                    "SELECT of.oid,of.* FROM @_catalog.@_opfamily of ORDER BY of.oid"
                            .replace("@", owner.getDataSource().getInstancePrefix()));
        }

        @Override
        protected PostgreOperatorFamily fetchObject(@NotNull JDBCSession session, @NotNull PostgreAccessMethod owner, @NotNull JDBCResultSet dbResult)
                throws SQLException, DBException
        {
            return new PostgreOperatorFamily(owner, dbResult);
        }
    }

}

