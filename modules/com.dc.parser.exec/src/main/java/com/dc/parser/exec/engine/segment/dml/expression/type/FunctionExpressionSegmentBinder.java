package com.dc.parser.exec.engine.segment.dml.expression.type;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.SegmentType;
import com.dc.parser.exec.engine.segment.dml.expression.ExpressionSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.dml.expr.FunctionSegment;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.stream.Collectors;

/**
 * Function expression binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class FunctionExpressionSegmentBinder {

    /**
     * Bind function expression.
     *
     * @param segment                  function expression segment
     * @param parentSegmentType        parent segment type
     * @param binderContext            SQL statement binder context
     * @param tableBinderContexts      table binder contexts
     * @param outerTableBinderContexts outer table binder contexts
     * @return bound function segment
     */
    public static FunctionSegment bind(final FunctionSegment segment, final SegmentType parentSegmentType, final SQLStatementBinderContext binderContext,
                                       final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                       final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        FunctionSegment result = new FunctionSegment(segment.getStartIndex(), segment.getStopIndex(), segment.getFunctionName(), segment.getText());
        result.setOwner(segment.getOwner());
        result.getParameters().addAll(segment.getParameters().stream()
                .map(each -> ExpressionSegmentBinder.bind(each, parentSegmentType, binderContext, tableBinderContexts, outerTableBinderContexts)).collect(Collectors.toList()));

        SqlActionModel sqlActionModel = binderContext.getSqlActionModel();
        sqlActionModel.getFunctions().add(segment.getFunctionName());
        return result;
    }
}
