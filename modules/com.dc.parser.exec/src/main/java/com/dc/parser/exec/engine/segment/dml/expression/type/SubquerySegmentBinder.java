package com.dc.parser.exec.engine.segment.dml.expression.type;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.exec.engine.statement.dml.SelectStatementBinder;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.summer.parser.sql.model.SqlActionModel;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Subquery segment binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SubquerySegmentBinder {

    /**
     * Bind subquery segment.
     *
     * @param segment                  subquery segment
     * @param binderContext            SQL statement binder context
     * @param outerTableBinderContexts outer table binder contexts
     * @return bound subquery segment
     */
    public static SubquerySegment bind(final SubquerySegment segment, final SQLStatementBinderContext binderContext,
                                       final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        SqlActionModel sqlActionModel = binderContext.getSqlActionModel();
        sqlActionModel.setHasSubQuery(true);

        SQLStatementBinderContext selectBinderContext =
                new SQLStatementBinderContext(binderContext.getMetaData(), binderContext.getCurrentDatabaseName(), binderContext.getCatalogName(), segment.getSelect());
        selectBinderContext.getExternalTableBinderContexts().putAll(binderContext.getExternalTableBinderContexts());
        selectBinderContext.getCommonTableExpressionsSegmentsUniqueAliases().addAll(binderContext.getCommonTableExpressionsSegmentsUniqueAliases());
        SelectStatement boundSelectStatement = new SelectStatementBinder(outerTableBinderContexts).bind(segment.getSelect(), selectBinderContext);

        // 合并权限模型和动作模型
        binderContext.getSqlAuthModels().addAll(selectBinderContext.getSqlAuthModels());
        binderContext.setSqlActionModel(SqlActionModel.merge(binderContext.getSqlActionModel(), selectBinderContext.getSqlActionModel()));

        binderContext.getCommonTableExpressionsSegmentsUniqueAliases().addAll(selectBinderContext.getCommonTableExpressionsSegmentsUniqueAliases());
        return new SubquerySegment(segment.getStartIndex(), segment.getStopIndex(), boundSelectStatement, segment.getText());
    }
}
