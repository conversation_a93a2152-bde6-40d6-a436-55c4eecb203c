package com.dc.parser.exec.engine.segment.dml.projection.type;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.SegmentType;
import com.dc.parser.exec.engine.segment.dml.expression.type.ColumnSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.item.ColumnProjectionSegment;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Column projection segment binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ColumnProjectionSegmentBinder {

    /**
     * Bind column projection segment.
     *
     * @param segment                  table segment
     * @param binderContext            SQL statement binder context
     * @param tableBinderContexts      table binder contexts
     * @param outerTableBinderContexts outer table binder contexts
     * @return bound column projection segment
     */
    public static ColumnProjectionSegment bind(final ColumnProjectionSegment segment, final SQLStatementBinderContext binderContext,
                                               final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                               final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts) {
        ColumnSegment boundColumn = ColumnSegmentBinder.bind(segment.getColumn(), SegmentType.PROJECTION, binderContext, tableBinderContexts, outerTableBinderContexts);
        ColumnProjectionSegment result = new ColumnProjectionSegment(boundColumn);
        segment.getAliasSegment().ifPresent(result::setAlias);
        result.setVisible(segment.isVisible());
        return result;
    }
}
