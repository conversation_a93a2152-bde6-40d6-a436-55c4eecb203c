package com.dc.parser.exec.engine.statement.dml;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.SegmentType;
import com.dc.parser.exec.engine.segment.dml.expression.type.ColumnSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.type.SimpleTableSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.prepare.PrepareStatementQuerySegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.statement.dml.CopyStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.SneakyThrows;

/**
 * Copy statement binder.
 */
public final class CopyStatementBinder implements SQLStatementBinder<CopyStatement> {

    @Override
    public CopyStatement bind(final CopyStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        CopyStatement result = copy(sqlStatement);
        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
        binderContext.setOperation(SqlConstant.KEY_INSERT);
        sqlStatement.getTable().ifPresent(optional -> result.setTable(SimpleTableSegmentBinder.bind(optional, binderContext, tableBinderContexts)));
        sqlStatement.getPrepareStatementQuery().ifPresent(optional -> result.setPrepareStatementQuery(PrepareStatementQuerySegmentBinder.bind(optional, binderContext)));
        sqlStatement.getColumns().forEach(each -> result.getColumns().add(ColumnSegmentBinder.bind(each, SegmentType.COPY, binderContext, tableBinderContexts, LinkedHashMultimap.create())));
        return result;
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private CopyStatement copy(final CopyStatement sqlStatement) {
        CopyStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }
}
