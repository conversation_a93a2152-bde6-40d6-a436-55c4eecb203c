package com.dc.summer.ext.redis.exec;

import com.dc.summer.ext.redis.RedisUtils;
import com.dc.summer.ext.redis.model.RedisKeyType;
import com.dc.code.NotNull;
import com.dc.summer.Log;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.qm.QMUtils;
import redis.clients.jedis.commands.JedisCommands;

public class RedisKeySizeStatement extends RedisBaseStatement {
   private static final Log log = Log.getLog(RedisKeySizeStatement.class);
   @NotNull
   private final String keyName;
   @NotNull
   private final RedisKeyType keyType;
   private long keySize;
   // $FF: synthetic field
   private static volatile int[] $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType;

   public RedisKeySizeStatement(@NotNull RedisSession session, @NotNull String key, @NotNull RedisKeyType keyType) {
      super(session, "SELECT count(*) FROM " + key);
      this.keyName = key;
      this.keyType = keyType;
   }

   public boolean executeStatement() throws DBCException {
      if (this.session.isLoggingEnabled()) {
         QMUtils.getDefaultHandler().handleStatementExecuteBegin(this);
      }

      RedisUtils.selectCurDatabase(this.getSession());

      try {
         if (!this.session.supportsCommands(JedisCommands.class)) {
            throw new DBCException("Key size commands not supported");
         }

         JedisCommands jedis = (JedisCommands)this.getSession().getCommands(JedisCommands.class);
         switch (this.keyType) {
            case string:
               this.keySize = 1L;
               break;
            case list:
               this.keySize = jedis.llen(this.keyName);
               break;
            case set:
               this.keySize = jedis.scard(this.keyName);
               break;
            case zset:
               this.keySize = jedis.zcount(this.keyName, Double.MIN_VALUE, Double.MAX_VALUE);
               break;
            case hash:
               this.keySize = jedis.hlen(this.keyName);
         }
      } catch (Throwable var5) {
         throw this.handleExecuteError(var5);
      } finally {
         if (this.session.isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleStatementExecuteEnd(this, -1L, this.executeError);
         }

      }

      return true;
   }

   public RedisBaseResultSet openResultSet() throws DBCException {
      return new RedisSimpleResultSet(this, this.keySize, this.offset);
   }

   public long getKeySize() {
      return this.keySize;
   }

   // $FF: synthetic method
   static int[] $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType() {
      int[] var10000 = $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType;
      if (var10000 != null) {
         return var10000;
      } else {
         int[] var0 = new int[RedisKeyType.values().length];

         try {
            var0[RedisKeyType.folder.ordinal()] = 6;
         } catch (NoSuchFieldError var7) {
         }

         try {
            var0[RedisKeyType.hash.ordinal()] = 5;
         } catch (NoSuchFieldError var6) {
         }

         try {
            var0[RedisKeyType.list.ordinal()] = 2;
         } catch (NoSuchFieldError var5) {
         }

         try {
            var0[RedisKeyType.none.ordinal()] = 7;
         } catch (NoSuchFieldError var4) {
         }

         try {
            var0[RedisKeyType.set.ordinal()] = 3;
         } catch (NoSuchFieldError var3) {
         }

         try {
            var0[RedisKeyType.string.ordinal()] = 1;
         } catch (NoSuchFieldError var2) {
         }

         try {
            var0[RedisKeyType.zset.ordinal()] = 4;
         } catch (NoSuchFieldError var1) {
         }

         $SWITCH_TABLE$com$dbeaver$db$redis$model$RedisKeyType = var0;
         return var0;
      }
   }
}
