<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
            class="com.dc.summer.ext.redis.RedisDataSourceProvider"
            description="Redis Connector"
            id="redis"
            label="Redis"
            icon="icons/redis_icon.png"
            dialect="redis">
            <tree path="redis" label="Redis data source">
                <folder type="com.dc.summer.ext.redis.model.RedisDatabase" label="%tree.databases.node.name" icon="#folder_database" description="Databases">
                    <items label="%tree.database.node.name" path="database" property="databases" icon="#database">
                        <folder id="list" type="com.dc.summer.ext.redis.model.RedisKey" label="%tree.keyList.node.name" icon="#folder_key" description="Key list">
                            <items label="%tree.key.node.name" path="key" property="keyList" icon="#key">
                                <items label="%tree.keyTree.subKeys.name" path="subKey" property="keys" icon="#key" navigable="false">
                                    <icon icon="#folder_key" if="object.folder"/>
                                </items>
                            </items>
                        </folder>
                        <folder id="tree" type="com.dc.summer.ext.redis.model.RedisKey" label="%tree.keyTree.node.name" icon="#folder_key" description="Key tree (hierarchy)" visibleIf="!dataSource.getContainer().getNavigatorSettings().isShowOnlyEntities()">
                            <items label="%tree.key.node.name" path="key" property="keyTree" icon="#key">
                                <icon icon="#folder_key" if="object.folder"/>
                                <items label="%tree.keyTree.subKeys.name" path="subKey" property="keys" icon="#key" recursive="..">
                                    <icon icon="#folder_key" if="object.folder"/>
                                </items>
                            </items>
                        </folder>
                    </items>
                </folder>
            </tree>

            <drivers managable="false">

                <driver
                    id="redis_jedis"
                    label="Redis"
                    class="com.dc.redis.RedisDriver"
                    sampleURL="redis://{host}[:{port}]/"
                    defaultPort="6379"
                    webURL="https://dbeaver.io/"
                    description="Redis Driver"
                    supportedConfigurationTypes="MANUAL,URL"
                    icon="icons/redis_icon.png"
                    iconBig="icons/redis_icon_big.png"
                    anonymous="false"
                    categories="nosql,keyvalue">
                    <parameter name="initOnTest" value="true"/>
                </driver>
            </drivers>
        </datasource>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.ext.redis.data.handlers.RedisValueHandlerProvider"
                description="Redis data types provider"
                id="com.dc.summer.ext.redis.data.handlers.RedisValueHandlerProvider"
                label="redis data types provider">

            <datasource id="redis"/>
            <type name="*"/>

        </provider>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
<!--
        <provider
            class="com.dbeaver.eeredis.data.handlers.RedisValueHandlerProvider"
            description="Redis data types provider"
            id="com.dbeaver.eeredis.data.handlers.RedisValueHandlerProvider"
            label="Redis data types provider">

            <datasource id="redis"/>
            <type name="*"/>

        </provider>
-->
    </extension>

    <extension point="com.dc.summer.dataManager">
<!--
        <manager class="com.dbeaver.eeredis.data.managers.RedisDocumentValueManager" id="com.dbeaver.eeredis.data.managers.RedisDocumentValueManager">
            <supports type="java.lang.Object"/>
        </manager>
-->
    </extension>

    <extension point="com.dc.summer.networkHandler">
        <handler
                type="config"
                id="redis_ssl"
                codeName="SSL"
                label="SSL"
                description="SSL settings"
                secured="false"
                order="100"
                handlerClass="com.dc.summer.ext.redis.model.RedisSSLHandlerImpl">
            <objectType name="com.dc.summer.ext.redis.RedisDataSourceProvider"/>
        </handler>
    </extension>

    <extension point="com.dc.summer.objectManager">
        <manager class="com.dc.summer.ext.redis.edit.RedisKeyManager" objectType="com.dc.summer.ext.redis.model.RedisKey"/>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="redis" parent="nosql" class="com.dc.summer.ext.redis.model.RedisSQLDialect" label="Redis" description="Redis dialect." icon="icons/redis_icon.png">
        </dialect>
    </extension>

</plugin>
