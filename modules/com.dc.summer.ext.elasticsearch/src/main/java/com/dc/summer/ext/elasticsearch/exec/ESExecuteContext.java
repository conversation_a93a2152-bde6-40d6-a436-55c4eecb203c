package com.dc.summer.ext.elasticsearch.exec;

import com.dc.summer.DBException;
import com.dc.summer.ext.elasticsearch.ESUtils;
import com.dc.summer.ext.elasticsearch.model.ESDataSource;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.AbstractExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSInstance;
import com.dc.summer.model.struct.rdb.DBSCatalog;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.HttpStatus;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpHead;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.*;

import java.io.IOException;
import java.net.ConnectException;

@Slf4j
public class ESExecuteContext extends AbstractExecutionContext<ESDataSource> implements DBCExecutionContextDefaults<DBSCatalog, DBSSchema> {

    private RestClient client;

    private DBPConnectionConfiguration configuration;

    public ESExecuteContext(ESDataSource dataSource, String purpose) {
        super(dataSource, purpose);
    }

    public void connect(DBRProgressMonitor monitor) throws DBException {
        DBExecUtils.startContextInitiation(getDataSource().getContainer());

        try {
            this.reconnect(monitor);
            super.initContextBootstrap(monitor, true);
            getDataSource().addExecutionContext(this);
        } catch (Exception var8) {
            throw new DBCException(var8, this);
        } finally {
            DBExecUtils.finishContextInitiation(getDataSource().getContainer());
        }
    }

    public void reconnect(DBRProgressMonitor monitor) throws DBException {
        monitor.beginTask("Open elasticsearch connection", 2);

        configuration = getDataSource().getContainer().getActualConnectionConfiguration();

        closeClient();

        monitor.subTask("Setting connection parameters");

        String hostName = configuration.getHostName();
        String hostPort = configuration.getHostPort();
        HttpHost httpHost = new HttpHost(hostName, Integer.parseInt(hostPort));
        RestClientBuilder builder = RestClient.builder(httpHost);

        String userName = configuration.getUserName();
        String userPassword = configuration.getUserPassword();
        if (userName != null) {
            builder.setHttpClientConfigCallback(httpAsyncClientBuilder -> {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(userName, userPassword));
                return httpAsyncClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
            });
        }

        String compressionEnabled = configuration.getProperty("compressionEnabled");
        if (compressionEnabled != null) {
            builder.setCompressionEnabled(CommonUtils.toBoolean(compressionEnabled));
        }

        String connectTimeout = configuration.getProperty("connectTimeout");
        String connectionRequestTimeout = configuration.getProperty("connectionRequestTimeout");
        String socketTimeout = configuration.getProperty("socketTimeout");
        if (connectTimeout != null || connectionRequestTimeout != null || socketTimeout != null) {
            builder.setRequestConfigCallback(
                    requestConfigBuilder -> requestConfigBuilder
                            .setConnectTimeout(connectTimeout != null ? Integer.parseInt(connectTimeout) : -1)
                            .setConnectionRequestTimeout(connectionRequestTimeout != null ? Integer.parseInt(connectionRequestTimeout) : -1)
                            .setSocketTimeout(socketTimeout != null ? Integer.parseInt(socketTimeout) : -1));
        }

        log.info("==> ConnectionURL: " + getDataSource().getConnectionURL(configuration));

        long ctm = System.currentTimeMillis();
        try {

            // TODO
//            builder.setDefaultHeaders();
//            builder.setFailureListener();
//            builder.setNodeSelector();
//            builder.setPathPrefix();
//            builder.setMetaHeaderEnabled();
//            builder.setStrictDeprecationMode();

            client = builder.build();

            monitor.worked(1);

            Request request = new Request(HttpGet.METHOD_NAME, "/");
            Response response = client.performRequest(request);

            if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                throw new DBCException(ESUtils.parseContent(response));
            }

        } catch (IOException e) {
            this.closeClient();
            String error;
            if (e instanceof ResponseException) {
                error = ESUtils.parseContent(((ResponseException) e).getResponse());
            } else {
                error = e.getMessage();
            }
            throw new DBCException(error, e);
        } finally {
            log.info("<== ConnectionTime: " + (System.currentTimeMillis() - ctm) + " ms");
            monitor.done();
        }

    }

    public RestClient getClient() {
        return client;
    }

    @Override
    public DBCExecutionContextDefaults getContextDefaults() {
        return this;
    }

    private void closeClient() {
        if (this.client != null) {
            try {
                this.client.close();
                this.client = null;
            } catch (Exception e) {
                log.error("ES this.client.close", e);
            }
        }
        super.closeContext();
    }

    @Override
    public void close() {
        synchronized (this) {
            getDataSource().removeExecutionContext(this);
            closeClient();
        }
    }

    @Override
    public DBSInstance getOwnerInstance() {
        return getDataSource();
    }

    @Override
    public boolean isConnected() {
        return true;
    }

    @Override
    public DBCSession openSession(DBRProgressMonitor monitor, DBCExecutionPurpose purpose, String task) {
        ESSessionImpl mgSession = new ESSessionImpl(monitor, purpose, task, this);
        return getProxySession(mgSession);
    }

    @Override
    public void checkContextAlive(DBRProgressMonitor monitor) throws DBException {
    }

    @Override
    public InvalidateResult invalidateContext(DBRProgressMonitor monitor, boolean closeOnFailure, DBPConnectionConfiguration configuration) throws DBException {
        this.reconnect(monitor);
        return InvalidateResult.RECONNECTED;
    }

    @Override
    public String getProcessId() {
        return null;
    }

    @Override
    public void execPrefs(DBRProgressMonitor monitor, String prefs) throws DBCException {

    }

    @Override
    public DBPConnectionConfiguration getConfiguration() {
        return configuration;
    }

    @Override
    public DBSCatalog getDefaultCatalog() {
        return null;
    }

    @Override
    public DBSSchema getDefaultSchema() {
        return null;
    }

    @Override
    public boolean supportsCatalogChange() {
        return false;
    }

    @Override
    public boolean supportsSchemaChange() {
        return false;
    }

    @Override
    public void setDefaultCatalog(DBRProgressMonitor monitor, DBSCatalog catalog, DBSSchema schema, boolean force) throws DBCException {

    }

    @Override
    public void setDefaultSchema(DBRProgressMonitor monitor, DBSSchema schema, boolean force) throws DBCException {

    }

    @Override
    public boolean refreshDefaults(DBRProgressMonitor monitor, boolean useBootstrapSettings) throws DBException {
        return false;
    }

}
