<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.dataSourceProvider">

        <!-- Elasticsearch -->

        <datasource
                class="com.dc.summer.ext.elasticsearch.ESDataSourceProvider"
                description="%datasource.es.description"
                id="elasticsearch"
                label="Elasticsearch"
                icon="icons/elasticsearch_icon.png"
                dialect="elasticsearch">
            <drivers managable="true">

                <driver
                        id="elasticsearch-rest-client"
                        label="Elasticsearch"
                        class="com.dc.elasticsearch.EsDriver"
                        sampleURL="elasticsearch://{host}:{port}/"
                        defaultPort="9200"
                        description="Elasticsearch Http driver"
                        categories="fulltext">
                </driver>

<!--                <driver
                        id="elastic_search_jdbc"
                        label="Elasticsearch"
                        class="org.elasticsearch.xpack.sql.jdbc.EsDriver"
                        icon="icons/elasticsearch_icon.png"
                        iconBig="icons/elasticsearch_icon_big.png"
                        sampleURL="jdbc:es://{host}:{port}/"
                        defaultPort="9200"
                        description="Elasticsearch JDBC driver"
                        webURL="https://www.elastic.co/guide/en/elasticsearch/reference/current/sql-jdbc.html"
                        categories="fulltext">
                    <replace provider="generic" driver="es_generic"/>
                    <file type="jar" path="maven:/org.elasticsearch.plugin:x-pack-sql-jdbc:7.9.1"/>
                    <parameter name="supports-references" value="false"/>
                    <parameter name="supports-indexes" value="false"/>
                    <parameter name="omit-catalog" value="true"/>
                    <parameter name="use-search-string-escape" value="true"/>
                </driver>-->

            </drivers>

        </datasource>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="elasticsearch" parent="nosql" class="com.dc.summer.ext.elasticsearch.model.ESSQLDialect" label="Elasticsearch" description="Elasticsearch dialect." icon="icons/elasticsearch_icon.png">
        </dialect>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.ext.elasticsearch.data.handlers.ESValueHandlerProvider"
                description="Elasticsearch data types provider"
                id="com.dc.summer.ext.elasticsearch.data.handlers.ESValueHandlerProvider"
                label="Elasticsearch data types provider">

            <datasource id="elasticsearch"/>

            <type name="*"/>

        </provider>
    </extension>

</plugin>
