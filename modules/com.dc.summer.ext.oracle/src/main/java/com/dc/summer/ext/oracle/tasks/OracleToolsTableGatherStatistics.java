
package com.dc.summer.ext.oracle.tasks;

import com.dc.summer.ext.oracle.model.OracleTable;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.sql.task.SQLToolExecuteHandler;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.struct.DBSObject;

import java.util.List;

public class OracleToolsTableGatherStatistics extends SQLToolExecuteHandler<DBSObject, OracleToolTableGatherStatisticsSettings> {
    @Override
    public OracleToolTableGatherStatisticsSettings createToolSettings() {
        return new OracleToolTableGatherStatisticsSettings();
    }

    @Override
    public void generateObjectQueries(DBCSession session, OracleToolTableGatherStatisticsSettings settings, List<DBEPersistAction> queries, DBSObject object) throws DBCException {
        if (object instanceof OracleTable) {
            OracleTable table = (OracleTable)object;
            int percent = settings.getSamplePercent();
            String sql = "BEGIN \n" +
                    " DBMS_STATS.GATHER_TABLE_STATS (\n" +
                    " OWNNAME => '" + DBUtils.getQuotedIdentifier(table.getSchema()) + "',\n" +
                    " TABNAME => '" + DBUtils.getQuotedIdentifier(table) + "'";
            if (percent > 0) {
                sql += ",\n estimate_percent => " + percent;
            }
            sql += " \n );\n END;";
            queries.add(new SQLDatabasePersistAction(sql));
        }
    }
}
