
package com.dc.summer.ext.oracle.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.model.struct.DBSObjectLazy;

import java.sql.ResultSet;

/**
 * OraclePrivRole
 */
public class OraclePrivRole extends OraclePriv implements DBSObjectLazy<OracleDataSource> {
    private Object role;
    private boolean defaultRole;

    public OraclePrivRole(OracleGrantee user, ResultSet resultSet) {
        super(user, JDBCUtils.safeGetString(resultSet, "GRANTED_ROLE"), resultSet);
        this.defaultRole = JDBCUtils.safeGetBoolean(resultSet, "DEFAULT_ROLE", "Y");
        this.role = this.name;
    }

    @NotNull
    @Override
    public String getName() {
        return super.getName();
    }

    @Property(id = DBConstants.PROP_ID_NAME, viewable = true, order = 2, supportsPreview = true)
    public Object getRole(DBRProgressMonitor monitor) throws DBException
    {
        if (monitor == null) {
            return role;
        }
        return OracleUtils.resolveLazyReference(monitor, getDataSource(), getDataSource().roleCache, this, null);
    }

    @Property(viewable = true, order = 4)
    public boolean isDefaultRole()
    {
        return defaultRole;
    }

    @Override
    public Object getLazyReference(Object propertyId)
    {
        return this.role;
    }

}
