
package com.dc.summer.ext.oracle.data;

import com.dc.code.Nullable;
import com.dc.summer.Log;
import com.dc.summer.ext.oracle.model.OracleConstants;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.data.DBDCursor;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.impl.jdbc.exec.JDBCResultSetImpl;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Cursor references (named)
 */
public class OracleRefCursor implements DBDCursor {

    private static final Log log = Log.getLog(OracleRefCursor.class);

    private final JDBCSession session;
    private final JDBCStatement sourceStatement;
    private final Object cursorValue;

    public OracleRefCursor(JDBCSession session, JDBCStatement sourceStatement, @Nullable Object cursorValue) throws SQLException {
        this.session = session;
        this.sourceStatement = sourceStatement;
        this.cursorValue = cursorValue;
    }

    @Override
    public Object getRawValue() {
        return cursorValue;
    }

    @Override
    public boolean isNull() {
        return cursorValue == null;
    }

    @Override
    public boolean isModified() {
        return false;
    }

    @Override
    public void release() {
        if (cursorValue instanceof ResultSet) {
            try {
                ((ResultSet) cursorValue).close();
            } catch (SQLException e) {
                log.error(e);
            }
        }
    }

    @Override
    public DBCResultSet openResultSet(DBCSession session) throws DBCException {
        if (cursorValue instanceof ResultSet) {
            try {
                return JDBCResultSetImpl.makeResultSet((JDBCSession) session, sourceStatement, (ResultSet) cursorValue, null, false);
            } catch (SQLException e) {
                throw new DBCException(e, session.getExecutionContext());
            }
        }
        throw new DBCException("Unsupported cursor value: " + cursorValue);
/*
        try {
            DBCTransactionManager txnManager = DBUtils.getTransactionManager(session.getExecutionContext());
            if (txnManager != null && txnManager.isAutoCommit()) {
                throw new DBCException("Ref cursors are not available in auto-commit mode");
            }
            if (cursorStatement != null) {
                cursorStatement.close();
            }
            JDBCUtils.executeStatement(this.session, "MOVE ABSOLUTE 0 IN \"" + cursorName + "\"");
            cursorStatement = this.session.createStatement();
            return cursorStatement.executeQuery("FETCH ALL IN \"" + cursorName + "\"");
        } catch (SQLException e) {
            throw new DBCException(e, session.getExecutionContext());
        }
*/
    }

    @Nullable
    @Override
    public String getCursorName() {
        if (cursorValue instanceof ResultSet) {
            try {
                return ((ResultSet) cursorValue).getCursorName();
            } catch (SQLException e) {
                log.error(e);
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return cursorValue == null ? DBConstants.NULL_VALUE_LABEL : OracleConstants.TYPE_NAME_REFCURSOR;
    }

}
