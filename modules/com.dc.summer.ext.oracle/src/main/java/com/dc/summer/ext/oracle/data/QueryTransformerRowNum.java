
package com.dc.summer.ext.oracle.data;

import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import com.dc.summer.Log;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCQueryTransformer;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.sql.SQLQuery;
import com.dc.summer.model.sql.parser.SQLSemanticProcessor;

/**
* Query transformer for ROWNUM
*/
public class QueryTransformerRowNum implements DBCQueryTransformer {

    private static final Log log = Log.getLog(QueryTransformerRowNum.class);

    private Number offset;
    private Number length;

    @Override
    public void setParameters(Object... parameters) {
        this.offset = (Number) parameters[0];
        this.length = (Number) parameters[1];
    }

    @Override
    public String transformQueryString(SQLQuery query) throws DBCException {
        long totalRows = offset.longValue() + length.longValue();
        if (query.isPlainSelect()) {
            try {
                Statement statement = query.getStatement();
                if (statement instanceof Select) {
                    Select select = (Select) statement;
                    if (select.getSelectBody() instanceof PlainSelect) {
                        SQLSemanticProcessor.addWhereToSelect(
                            (PlainSelect) select.getSelectBody(),
                            "ROWNUM <= " + totalRows);
                        return statement.toString();
                    }
                }
            } catch (Throwable e) {
                // ignore
                log.debug(e);
            }
        }
        return query.getText();
    }

    @Override
    public void transformStatement(DBCStatement statement, int parameterIndex) throws DBCException {
        statement.setLimit(0, length.longValue() - 1);
    }
}
