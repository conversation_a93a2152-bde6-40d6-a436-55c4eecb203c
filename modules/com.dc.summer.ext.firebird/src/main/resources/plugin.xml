<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.generic.meta">
        <meta id="firebird" class="com.dc.summer.ext.firebird.model.FireBirdMetaModel" driverClass="org.firebirdsql.jdbc.FBDriver"/>
    </extension>

    <extension point="com.dc.summer.dataSourceProvider">

        <!-- SQL Server -->

        <datasource
                class="com.dc.summer.ext.firebird.FireBirdDataSourceProvider"
                description="%datasource.firebird.description"
                id="jaybird"
                parent="generic"
                label="Firebird"
                icon="icons/firebird_icon.png"
                dialect="firebird">
            <drivers managable="true">

                <driver
                        id="jaybird"
                        label="Firebird"
                        icon="icons/firebird_icon.png"
                        iconBig="icons/firebird_icon_big.png"
                        class="org.firebirdsql.jdbc.FBDriver"
                        sampleURL="jdbc:firebirdsql://{host}:{port}/{file}"
                        defaultPort="3050"
                        defaultUser="SYSDBA"
                        webURL="https://firebirdsql.org/en/jdbc-driver/"
                        propertiesURL="https://github.com/FirebirdSQL/jaybird/wiki/Connection-properties"
                        description="Firebird Jaybird driver"
                        categories="sql,embedded">
                    <replace provider="generic" driver="firebird_jaybird"/>
                    <replace provider="generic" driver="firebird_jaybird3"/>

                    <file type="jar" path="maven:/org.firebirdsql.jdbc:jaybird:RELEASE[4.0.0.java8]" bundle="!drivers.firebird"/>

                    <file type="license" path="drivers/jaybird/LICENSE.txt" bundle="drivers.firebird"/>
                    <file type="jar" path="drivers/jaybird" bundle="drivers.firebird"/>

                    <property name="encoding" value="UTF8"/>

                    <parameter name="supports-scroll" value="true"/>
                    <parameter name="ddl-drop-column-short" value="true"/>
                    <parameter name="script-delimiter-redefiner" value="SET TERM"/>
                    <parameter name="supports-truncate" value="false"/>
                </driver>

<!--
                <driver
                        id="jaybird_embedded"
                        label="Firebird Embedded"
                        icon="icons/firebird_icon.png"
                        class="org.firebirdsql.jdbc.FBDriver"
                        sampleURL="jdbc:firebirdsql:embedded://{file}"
                        embedded="true"
                        webURL=""
                        description="Firebird Jaybird embedded driver">

                    <file type="jar" path="maven:/org.firebirdsql.jdbc:jaybird-jdk18:RELEASE[3.0.3]" bundle="!drivers.firebird"/>

                    <file type="license" path="drivers/firebird3/LICENSE.txt" bundle="drivers.firebird"/>
                    <file type="jar" path="drivers/firebird3/jaybird-full.jar" bundle="drivers.firebird"/>
                    <file type="jar" path="drivers/firebird3/jna.jar" bundle="drivers.firebird"/>

                    <property name="encoding" value="UTF8"/>

                    <parameter name="supports-scroll" value="true"/>
                    <parameter name="ddl-drop-column-short" value="true"/>
                    <parameter name="script-delimiter-redefiner" value="SET TERM"/>
                    <parameter name="supports-truncate" value="false"/>
                </driver>
-->

            </drivers>

        </datasource>
    </extension>

    <extension point="com.dc.summer.objectManager">
        <manager class="com.dc.summer.ext.firebird.edit.FireBirdTableManager" objectType="com.dc.summer.ext.firebird.model.FireBirdTable"/>
        <manager class="com.dc.summer.ext.firebird.edit.FireBirdTableColumnManager" objectType="com.dc.summer.ext.firebird.model.FireBirdTableColumn"/>
        <manager class="com.dc.summer.ext.firebird.edit.FireBirdProcedureManager" objectType="com.dc.summer.ext.firebird.model.FireBirdProcedure"/>
        <manager class="com.dc.summer.ext.firebird.edit.FireBirdSequenceManager" objectType="com.dc.summer.ext.firebird.model.FireBirdSequence"/>
    </extension>

    <extension point="com.dc.summer.sqlInsertMethod">
        <method id="firebirdReplaceIgnore" class="com.dc.summer.ext.firebird.model.FireBirdInsertReplaceMethod" label="UPDATE OR INSERT INTO" description="Insert replace duplicate key value"/>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="firebird" parent="generic" class="com.dc.summer.ext.firebird.model.FireBirdSQLDialect" label="Firebird" description="Firebird SQL dialect." icon="icons/firebird_icon.png">
            <property name="insertMethods" value="firebirdReplaceIgnore"/>
        </dialect>
    </extension>

</plugin>
