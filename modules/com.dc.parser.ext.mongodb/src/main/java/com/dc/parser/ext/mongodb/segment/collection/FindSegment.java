package com.dc.parser.ext.mongodb.segment.collection;

import com.dc.parser.ext.mongodb.segment.BsonObjectSegment;
import com.dc.parser.ext.mongodb.segment.CursorSegment;
import com.dc.parser.ext.mongodb.segment.MethodSegment;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class FindSegment extends MethodSegment {
    private BsonObjectSegment filter;
    private BsonObjectSegment projection;
    private BsonObjectSegment options;
    private List<CursorSegment> cursors = new ArrayList<>();

    public FindSegment(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }
}
