package com.dc.parser.ext.mongodb.check.flex.auth;

import com.dc.parser.ext.mongodb.statement.DatabaseStatement;
import com.dc.parser.model.check.flex.auth.CheckAuthParam;
import com.dc.parser.model.check.flex.auth.CheckAuthResult;
import com.dc.parser.model.check.flex.auth.SpecialAuth;
import com.dc.parser.model.context.SQLStatementContext;
import com.dc.utils.bean.ClassUtils;
import org.jetbrains.annotations.Nullable;

public class MongoDBSpecialAuth extends SpecialAuth {

    @Override
    public CheckAuthResult check(SQLStatementContext context, @Nullable CheckAuthParam parameter) {
        // 切换数据库
        if (ClassUtils.isInstanceOfMultiple(context.getSqlStatement(),
                DatabaseStatement.class
        )) {
            return check(" DATABASE ");
        }

        return null;
    }
}
