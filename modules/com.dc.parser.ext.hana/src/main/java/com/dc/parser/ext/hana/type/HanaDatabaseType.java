
package com.dc.parser.ext.hana.type;

import com.dc.infra.database.type.DatabaseType;

import java.util.Arrays;
import java.util.Collection;

/**
 * Database type of presto.
 */
public final class HanaDatabaseType implements DatabaseType {
    
    @Override
    public Collection<String> getJdbcUrlPrefixes() {
        return Arrays.asList("jdbc:presto:", "presto:");
    }
    
    @Override
    public Constant getType() {
        return Constant.HANA;
    }
}
