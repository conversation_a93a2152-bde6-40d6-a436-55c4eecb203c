
package com.dc.summer.ext.hana.model;

import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ModelPreferences;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.ext.hana.model.plan.HANAPlanAnalyser;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.DBPErrorAssistant;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.exec.plan.DBCPlan;
import com.dc.summer.model.exec.plan.DBCPlanStyle;
import com.dc.summer.model.exec.plan.DBCQueryPlanner;
import com.dc.summer.model.exec.plan.DBCQueryPlannerConfiguration;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSStructureAssistant;
import com.dc.utils.CommonUtils;
import org.eclipse.core.runtime.IAdaptable;
import com.dc.code.NotNull;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.model.connection.DBPConnectionConfiguration;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HANADataSource extends GenericDataSource implements DBCQueryPlanner, IAdaptable {

    private static final Log log = Log.getLog(HANADataSource.class);
    private static final String PROP_APPLICATION_NAME = "SESSIONVARIABLE:APPLICATION";
    private static final String PROP_READONLY = "READONLY";
    private static final String PROP_SPATIAL_OUTPUT_REPRESENTATION = "SESSIONVARIABLE:SPATIAL_OUTPUT_REPRESENTATION";
    private static final String VALUE_SPATIAL_OUTPUT_REPRESENTATION = "EWKB";
    private static final String PROP_SPATIAL_WKB_EMPTY_POINT_REPRESENTATION = "SESSIONVARIABLE:SPATIAL_WKB_EMPTY_POINT_REPRESENTATION";
    private static final String VALUE_SPATIAL_WKB_EMPTY_POINT_REPRESENTATION = "NAN_COORDINATES";
    

    private HashMap<String, String> sysViewColumnUnits; 
    
    public HANADataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel)
        throws DBException
    {
        super(monitor, container, metaModel, new HANASQLDialect());
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, @NotNull JDBCDatabaseMetaData metaData)
    {
        final HANADataSourceInfo info = new HANADataSourceInfo(metaData);
        return info;
    }
    
    /*
     * search
     */
    @Override
    public <T> T getAdapter(Class<T> adapter) {
        if (adapter == DBSStructureAssistant.class)
            return adapter.cast(new HANAStructureAssistant(this));
        return super.getAdapter(adapter);
    }
    
    /*
     * explain
     */
    @NotNull
    @Override
    public DBCPlan planQueryExecution(@NotNull DBCSession session, @NotNull String query, @NotNull DBCQueryPlannerConfiguration configuration)
    throws DBCException {
        HANAPlanAnalyser plan = new HANAPlanAnalyser(this, query);
        plan.explain(session);
        return plan;
    }

    @NotNull
    @Override
    public DBCPlanStyle getPlanStyle() {
        return DBCPlanStyle.PLAN;
    }
  
    /*
     * application
     */
    @Override
    protected boolean isPopulateClientAppName() { 
        return false; // basically true, but different property name 
    } 

    @Override
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo) throws DBCException {
        Map<String, String> props = new HashMap<>();
        if (!getContainer().getPreferenceStore().getBoolean(ModelPreferences.META_CLIENT_NAME_DISABLE)) {
            String appName = DBUtils.getClientApplicationName(getContainer(), context, purpose);
            props.put(PROP_APPLICATION_NAME, appName);
        }
        if (getContainer().isConnectionReadOnly()) {
            props.put(PROP_READONLY, "TRUE");
        }
        // Represent geometries as EWKB (instead of as WKB) so that we can extract the SRID
        props.put(PROP_SPATIAL_OUTPUT_REPRESENTATION, VALUE_SPATIAL_OUTPUT_REPRESENTATION);
        // Represent empty points using NaN-coordinates
        props.put(PROP_SPATIAL_WKB_EMPTY_POINT_REPRESENTATION, VALUE_SPATIAL_WKB_EMPTY_POINT_REPRESENTATION);
        return props;
    }
    
    /*
     * column unit for views in SYS schema
     */
    public void initializeSysViewColumnUnits(@NotNull DBRProgressMonitor monitor) throws DBException {
        if (sysViewColumnUnits != null)
            return;
        sysViewColumnUnits = new HashMap<String, String>();
        String stmt = "SELECT VIEW_NAME||'.'||VIEW_COLUMN_NAME, UNIT FROM SYS.M_MONITOR_COLUMNS WHERE UNIT IS NOT NULL";
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Read generic metadata")) {
            try {
                try (JDBCPreparedStatement dbStat = session.prepareStatement(stmt)) {
                    try (JDBCResultSet resultSet = dbStat.executeQuery()) {
                        while(resultSet.next()) {
                            sysViewColumnUnits.put(resultSet.getString(1), resultSet.getString(2));
                        }
                    }
                }
            } catch (SQLException e) {
                log.debug("Error getting SYS column units: " + e.getMessage());
            }
        }
    }

    private final Pattern ERROR_POSITION_PATTERN = Pattern.compile("line ([0-9]+) col ([0-9]+)");

    private final Pattern ERROR_POSITION_PATTERN_2 = Pattern.compile(" at line ([0-9]+)");

    private final Pattern ERROR_POSITION_PATTERN_3 = Pattern.compile(" at pos ([0-9]+)");

    @Nullable
    @Override
    public ErrorPosition[] getErrorPosition(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionContext context, @NotNull String query, @NotNull Throwable error) {
        String message = error.getMessage();
        if (!CommonUtils.isEmpty(message)) {

            List<ErrorPosition> positions = new ArrayList<>();
            Matcher matcher = ERROR_POSITION_PATTERN.matcher(message);
            while (matcher.find()) {
                DBPErrorAssistant.ErrorPosition pos = new DBPErrorAssistant.ErrorPosition();
                pos.line = Integer.parseInt(matcher.group(1)) - 1;
                pos.position = Integer.parseInt(matcher.group(2)) - 1;
                positions.add(pos);
            }

            if (positions.isEmpty()) {
                matcher = ERROR_POSITION_PATTERN_2.matcher(message);
                if (matcher.find()) {
                    DBPErrorAssistant.ErrorPosition pos = new DBPErrorAssistant.ErrorPosition();
                    pos.line = Integer.parseInt(matcher.group(1)) - 1;
                    positions.add(pos);
                }
            }

            if (positions.isEmpty()) {
                matcher = ERROR_POSITION_PATTERN_3.matcher(message);
                while (matcher.find()) {
                    DBPErrorAssistant.ErrorPosition pos = new DBPErrorAssistant.ErrorPosition();
                    pos.position = Integer.parseInt(matcher.group(1)) - 1;
                    positions.add(pos);
                }
            }

            if (!positions.isEmpty()) {
                return positions.toArray(new ErrorPosition[0]);
            }
        }
        return null;
    }

    String getSysViewColumnUnit(String objectName, String columnName)
    {
        return sysViewColumnUnits.get(objectName+"."+columnName);
    }

    @Override
    public ErrorType discoverErrorType(@NotNull Throwable error) {
        String errorMessage = error.getMessage();
        if (errorMessage.contains("Invalid argument autoGeneratedKeys") || errorMessage.contains("Invalid argument resultSetType")) {
            return ErrorType.FEATURE_UNSUPPORTED;
        }
        return super.discoverErrorType(error);
    }
}
