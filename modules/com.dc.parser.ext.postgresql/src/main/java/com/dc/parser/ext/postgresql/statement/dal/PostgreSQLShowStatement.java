package com.dc.parser.ext.postgresql.statement.dal;

import com.dc.parser.ext.postgresql.statement.PostgreSQLStatement;
import com.dc.parser.model.statement.dal.ShowStatement;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

/**
 * PostgreSQL show statement.
 */
@RequiredArgsConstructor
public final class PostgreSQLShowStatement extends ShowStatement implements PostgreSQLStatement {

    private final String name;

    @Override
    public Optional<String> getName() {
        return Optional.ofNullable(name);
    }
}
