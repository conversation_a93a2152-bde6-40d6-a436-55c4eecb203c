
package com.dc.parser.ext.postgresql.visitor.statement.type;

import com.dc.parser.ext.postgresql.statement.dml.PostgreSQLCheckpointStatement;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DALStatementVisitor;
import com.dc.parser.ext.postgresql.parser.autogen.PostgreSQLStatementParser.*;
import com.dc.parser.ext.postgresql.visitor.statement.PostgreSQLStatementVisitor;
import com.dc.parser.model.segment.dal.VariableAssignSegment;
import com.dc.parser.model.segment.dal.VariableSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.TableNameSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.value.collection.CollectionValue;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.ext.postgresql.statement.dal.*;

import java.util.Collection;
import java.util.LinkedList;

/**
 * DAL statement visitor for PostgreSQL.
 */
public final class PostgreSQLDALStatementVisitor extends PostgreSQLStatementVisitor implements DALStatementVisitor {

    @Override
    public ASTNode visitCheckpoint(final CheckpointContext ctx) {
        return new PostgreSQLCheckpointStatement();
    }
    @Override
    public ASTNode visitShow(final ShowContext ctx) {
        if (null != ctx.varName()) {
            return new PostgreSQLShowStatement(ctx.varName().getText());
        }
        if (null != ctx.ZONE()) {
            return new PostgreSQLShowStatement("timezone");
        }
        if (null != ctx.ISOLATION()) {
            return new PostgreSQLShowStatement("transaction_isolation");
        }
        if (null != ctx.AUTHORIZATION()) {
            return new PostgreSQLShowStatement("session_authorization");
        }
        return new PostgreSQLShowStatement("ALL");
    }
    
    @Override
    public ASTNode visitSet(final SetContext ctx) {
        PostgreSQLSetStatement result = new PostgreSQLSetStatement();
        Collection<VariableAssignSegment> variableAssigns = new LinkedList<>();
        if (null != ctx.configurationParameterClause()) {
            VariableAssignSegment variableAssignSegment = (VariableAssignSegment) visit(ctx.configurationParameterClause());
            if (null != ctx.runtimeScope()) {
                variableAssignSegment.getVariable().setScope(ctx.runtimeScope().getText());
            }
            variableAssigns.add(variableAssignSegment);
        }
        if (null != ctx.encoding()) {
            VariableAssignSegment variableAssignSegment = new VariableAssignSegment();
            variableAssignSegment.setVariable(new VariableSegment(ctx.NAMES().getSymbol().getStartIndex(), ctx.NAMES().getSymbol().getStopIndex(), "client_encoding"));
            String value = ctx.encoding().getText();
            variableAssignSegment.setAssignValue(value);
            variableAssigns.add(variableAssignSegment);
        }
        result.getVariableAssigns().addAll(variableAssigns);
        return result;
    }
    
    @Override
    public ASTNode visitConfigurationParameterClause(final ConfigurationParameterClauseContext ctx) {
        VariableAssignSegment result = new VariableAssignSegment();
        result.setStartIndex(ctx.start.getStartIndex());
        result.setStopIndex(ctx.stop.getStopIndex());
        result.setVariable(new VariableSegment(ctx.varName().start.getStartIndex(), ctx.varName().stop.getStopIndex(), ctx.varName().getText()));
        if (null != ctx.varList()) {
            result.setAssignValue(ctx.varList().getText());
        }
        if (null != ctx.DEFAULT()) {
            result.setAssignValue(ctx.DEFAULT().getText());
        }
        return result;
    }
    
    @Override
    public ASTNode visitResetParameter(final ResetParameterContext ctx) {
        return new PostgreSQLResetParameterStatement(null != ctx.ALL() ? "ALL" : ctx.identifier().getText());
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitAnalyzeTable(final AnalyzeTableContext ctx) {
        PostgreSQLAnalyzeTableStatement result = new PostgreSQLAnalyzeTableStatement();
        if (null != ctx.vacuumRelationList()) {
            result.getTables().addAll(((CollectionValue<SimpleTableSegment>) visit(ctx.vacuumRelationList())).getValue());
        }
        return result;
    }
    
    @Override
    public ASTNode visitVacuumRelationList(final VacuumRelationListContext ctx) {
        CollectionValue<SimpleTableSegment> result = new CollectionValue<>();
        for (VacuumRelationContext each : ctx.vacuumRelation()) {
            result.getValue().add(new SimpleTableSegment((TableNameSegment) visitTableName(each.tableName())));
        }
        return result;
    }
    
    @Override
    public ASTNode visitLoad(final LoadContext ctx) {
        return new PostgreSQLLoadStatement();
    }
    
    @Override
    public ASTNode visitVacuum(final VacuumContext ctx) {
        return new PostgreSQLVacuumStatement();
    }
    
    @Override
    public ASTNode visitExplain(final ExplainContext ctx) {
        PostgreSQLExplainStatement result = new PostgreSQLExplainStatement();
        result.setSqlStatement((SQLStatement) visit(ctx.explainableStmt()));
        return result;
    }
    
    @Override
    public ASTNode visitExplainableStmt(final ExplainableStmtContext ctx) {
        if (null != ctx.select()) {
            return visitSelect(ctx.select());
        }
        if (null != ctx.insert()) {
            return visitInsert(ctx.insert());
        }
        if (null != ctx.update()) {
            return visitUpdate(ctx.update());
        }
        if (null != ctx.delete()) {
            return visitDelete(ctx.delete());
        }
        if (null != ctx.declare()) {
            // TODO visit declare statement
            return visitDeclare(ctx.declare());
        }
        if (null != ctx.executeStmt()) {
            return visitExecuteStmt(ctx.executeStmt());
        }
        if (null != ctx.createMaterializedView()) { //todo
            PostgreSQLDDLStatementVisitor ddlVisitor = new PostgreSQLDDLStatementVisitor();
            return ddlVisitor.visitCreateMaterializedView(ctx.createMaterializedView());
        }
        if (null != ctx.refreshMaterializedView()) {    //todo
            return visitRefreshMaterializedView(ctx.refreshMaterializedView());
        }
        if (null != ctx.merge()) {  //todo
            return visitMerge(ctx.merge());
        }
        if (null != ctx.createTable()) {
            PostgreSQLDDLStatementVisitor ddlVisitor = new PostgreSQLDDLStatementVisitor();
            return ddlVisitor.visitCreateTable(ctx.createTable());
        }
        return visit(ctx);
    }
    
    @Override
    public ASTNode visitEmptyStatement(final EmptyStatementContext ctx) {
        return new PostgreSQLEmptyStatement();
    }
}
