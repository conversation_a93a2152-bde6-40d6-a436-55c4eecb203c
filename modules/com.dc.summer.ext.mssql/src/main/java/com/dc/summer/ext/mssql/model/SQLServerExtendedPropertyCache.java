
package com.dc.summer.ext.mssql.model;

import com.dc.summer.ext.mssql.SQLServerUtils;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.impl.jdbc.cache.JDBCObjectLookupCache;

import java.sql.SQLException;

public class SQLServerExtendedPropertyCache extends JDBCObjectLookupCache<SQLServerExtendedPropertyOwner, SQLServerExtendedProperty> {
    @NotNull
    @Override
    public JDBCStatement prepareLookupStatement(@NotNull JDBCSession session, @NotNull SQLServerExtendedPropertyOwner owner, @Nullable SQLServerExtendedProperty object, @Nullable String objectName) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ep.*, ");
        if (SQLServerUtils.isDriverBabelfish(session.getDataSource().getContainer().getDriver())) {
            sql.append("NULL AS value_type ");
        }
        else {
            sql.append("TYPE_ID(CAST(SQL_VARIANT_PROPERTY(value, 'BaseType') as nvarchar)) AS value_type ");
        }
        sql.append("FROM ");
        sql.append(SQLServerUtils.getExtendedPropsTableName(owner.getDatabase()));
        sql.append(" ep WHERE ep.major_id=? AND ep.minor_id=? AND ep.class=? ORDER BY ep.minor_id");
        JDBCPreparedStatement dbStat = session.prepareStatement(sql.toString());
        dbStat.setLong(1, owner.getMajorObjectId());
        dbStat.setLong(2, owner.getMinorObjectId());
        dbStat.setLong(3, owner.getExtendedPropertyObjectClass().getClassId());
        return dbStat;

    }

    @Nullable
    @Override
    protected SQLServerExtendedProperty fetchObject(@NotNull JDBCSession session, @NotNull SQLServerExtendedPropertyOwner owner, @NotNull JDBCResultSet resultSet) throws SQLException, DBException {
        return new SQLServerExtendedProperty(session.getProgressMonitor(), owner, resultSet);
    }
}
