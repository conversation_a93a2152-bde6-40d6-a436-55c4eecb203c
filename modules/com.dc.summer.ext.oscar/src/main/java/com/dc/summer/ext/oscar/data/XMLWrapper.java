package com.dc.summer.ext.oscar.data;

import com.dc.summer.ext.oscar.internal.OscarMessages;
import com.dc.utils.BeanUtils;

import javax.xml.transform.Result;
import javax.xml.transform.Source;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.io.Writer;
import java.lang.reflect.InvocationTargetException;
import java.sql.SQLException;
import java.sql.SQLXML;

public class XMLWrapper implements SQLXML {
   private final Object xmlType;

   public XMLWrapper(Object xmlType) {
      this.xmlType = xmlType;
   }

   public void free() throws SQLException {
      try {
         BeanUtils.invokeObjectMethod(this.xmlType, "close", (Class[])null, (Object[])null);
      } catch (Throwable var2) {
         throw new SQLException(OscarMessages.XMLWrapper_cannot_close_xmltype, var2);
      }
   }

   public InputStream getBinaryStream() throws SQLException {
      try {
         return (InputStream)BeanUtils.invokeObjectMethod(this.xmlType, "getInputStream", (Class[])null, (Object[])null);
      } catch (InvocationTargetException var2) {
         if (var2.getTargetException() instanceof SQLException) {
            throw (SQLException)var2.getTargetException();
         } else {
            throw new SQLException(var2);
         }
      } catch (Throwable var3) {
         throw new SQLException(var3);
      }
   }

   public OutputStream setBinaryStream() throws SQLException {
      throw new SQLException(OscarMessages.XMLWrapper_function_not_supported);
   }

   public Reader getCharacterStream() throws SQLException {
      try {
         Object clobVal = BeanUtils.invokeObjectMethod(this.xmlType, "getClobVal", (Class[])null, (Object[])null);
         return (Reader)BeanUtils.invokeObjectMethod(clobVal, "getCharacterStream", (Class[])null, (Object[])null);
      } catch (InvocationTargetException var2) {
         if (var2.getTargetException() instanceof SQLException) {
            throw (SQLException)var2.getTargetException();
         } else {
            throw new SQLException(var2);
         }
      } catch (Throwable var3) {
         throw new SQLException(var3);
      }
   }

   public Writer setCharacterStream() throws SQLException {
      throw new SQLException(OscarMessages.XMLWrapper_function_not_supported);
   }

   public String getString() throws SQLException {
      try {
         return (String)BeanUtils.invokeObjectMethod(this.xmlType, "getStringVal", (Class[])null, (Object[])null);
      } catch (InvocationTargetException var2) {
         if (var2.getTargetException() instanceof SQLException) {
            throw (SQLException)var2.getTargetException();
         } else {
            throw new SQLException(var2);
         }
      } catch (Throwable var3) {
         throw new SQLException(var3);
      }
   }

   public void setString(String value) throws SQLException {
      throw new SQLException(OscarMessages.XMLWrapper_function_not_supported);
   }

   public <T extends Source> T getSource(Class<T> sourceClass) throws SQLException {
      return null;
   }

   public <T extends Result> T setResult(Class<T> resultClass) throws SQLException {
      throw new SQLException(OscarMessages.XMLWrapper_function_not_supported);
   }
}
