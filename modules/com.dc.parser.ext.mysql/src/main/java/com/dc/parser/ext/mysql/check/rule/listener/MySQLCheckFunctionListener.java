package com.dc.parser.ext.mysql.check.rule.listener;

import com.dc.parser.ext.mysql.parser.autogen.MySQLStatementBaseListener;
import com.dc.parser.ext.mysql.parser.autogen.MySQLStatementParser;
import lombok.Getter;

@Getter
public class MySQLCheckFunctionListener extends MySQLStatementBaseListener {

    private boolean existsFuction = false;

    @Override
    public void enterRegularFunctionName(MySQLStatementParser.RegularFunctionNameContext ctx) {
        if (existsFuction) {
            return;
        }
        existsFuction = ctx.getText().equalsIgnoreCase("SYSDATE");
    }
}
