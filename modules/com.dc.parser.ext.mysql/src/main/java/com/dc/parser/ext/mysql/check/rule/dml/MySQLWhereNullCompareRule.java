package com.dc.parser.ext.mysql.check.rule.dml;

import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.DMLStatement;
import com.dc.parser.model.util.WhereNullCompareExtractor;

public class MySQLWhereNullCompareRule implements SQLRule {

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DML_CHECK_WHERE_NULL_COMPARE.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement instanceof DMLStatement) {
            WhereNullCompareExtractor extractor = new WhereNullCompareExtractor();
            extractor.extractFromDML(sqlStatement);
            if (extractor.getHasNull().get()) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }
        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }
}
