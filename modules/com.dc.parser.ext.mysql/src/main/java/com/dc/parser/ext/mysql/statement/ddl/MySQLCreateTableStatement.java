package com.dc.parser.ext.mysql.statement.ddl;

import com.dc.parser.ext.mysql.segment.PartitionPropertiesSegment;
import com.dc.parser.ext.mysql.statement.MySQLStatement;
import com.dc.parser.model.segment.ddl.table.CreateTableOptionSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.CreateTableStatement;
import com.dc.parser.model.statement.ddl.ExistsCreateTableStatement;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.Optional;

/**
 * MySQL create table statement.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class MySQLCreateTableStatement extends CreateTableStatement implements MySQLStatement, ExistsCreateTableStatement {

    private boolean ifNotExists;
    
    private SimpleTableSegment likeTable;

    private CreateTableOptionSegment createTableOption;

    private PartitionPropertiesSegment partitionProperties;

    /**
     * Get like table.
     *
     * @return like table
     */
    public Optional<SimpleTableSegment> getLikeTable() {
        return Optional.ofNullable(likeTable);
    }
    
    /**
     * Get create table option segment.
     *
     * @return create table option segment
     */
    public Optional<CreateTableOptionSegment> getCreateTableOption() {
        return Optional.ofNullable(createTableOption);
    }

    /**
     * Get partition properties segment
     *
     * @return partition properties segment
     */
    public Optional<PartitionPropertiesSegment> getPartitionProperties() {
        return Optional.ofNullable(partitionProperties);
    }

}
