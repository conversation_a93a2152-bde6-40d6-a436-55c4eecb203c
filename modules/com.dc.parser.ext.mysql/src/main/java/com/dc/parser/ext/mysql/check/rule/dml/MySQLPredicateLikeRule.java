package com.dc.parser.ext.mysql.check.rule.dml;

import com.dc.parser.ext.mysql.check.rule.listener.MySQLPredicateLikeListener;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import org.antlr.v4.runtime.tree.ParseTreeWalker;

public class MySQLPredicateLikeRule implements SQLRule {

    private final ParseTreeWalker parseTreeWalker = ParseTreeWalker.DEFAULT;

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DML_CHECK_LIKE_USE_PATTERN_MATCH.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement.getParseTree() != null) {
            MySQLPredicateLikeListener listener = new MySQLPredicateLikeListener();
            parseTreeWalker.walk(listener, sqlStatement.getParseTree());
            if (listener.isNoExistxLikePattern()) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }
}
