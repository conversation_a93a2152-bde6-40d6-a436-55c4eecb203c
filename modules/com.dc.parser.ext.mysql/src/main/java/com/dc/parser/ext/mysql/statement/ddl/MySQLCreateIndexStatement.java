package com.dc.parser.ext.mysql.statement.ddl;

import com.dc.parser.ext.mysql.segment.IndexSpecificationEnum;
import com.dc.parser.ext.mysql.statement.MySQLStatement;
import com.dc.parser.model.segment.ddl.table.AlgorithmTypeSegment;
import com.dc.parser.model.segment.ddl.table.LockTableSegment;
import com.dc.parser.model.statement.ddl.CreateIndexStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * MySQL create index statement.
 */
@Setter
public final class MySQLCreateIndexStatement extends CreateIndexStatement implements MySQLStatement {
    
    private AlgorithmTypeSegment algorithmType;
    
    private LockTableSegment lockTable;

    private IndexSpecificationEnum indexSpecification;

    /**
     * Get algorithm segment.
     *
     * @return algorithm segment
     */
    @Override
    public Optional<AlgorithmTypeSegment> getAlgorithmType() {
        return Optional.ofNullable(algorithmType);
    }
    
    /**
     * Get lock table Segment.
     *
     * @return lock table segment
     */
    @Override
    public Optional<LockTableSegment> getLockTable() {
        return Optional.ofNullable(lockTable);
    }

    public Optional<IndexSpecificationEnum> getIndexSpecification() {
        return Optional.ofNullable(indexSpecification);
    }
}
