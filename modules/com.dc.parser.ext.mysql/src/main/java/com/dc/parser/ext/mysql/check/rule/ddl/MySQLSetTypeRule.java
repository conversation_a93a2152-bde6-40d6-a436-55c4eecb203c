package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;

import java.util.Set;

public class MySQLSetTypeRule extends NoSuggestTypeRule {

    @Override
    protected Set<String> noSuggestTypes(String param) {
        return Set.of("SET");
    }

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DDL_CHECK_SET.getValue();
    }
}
