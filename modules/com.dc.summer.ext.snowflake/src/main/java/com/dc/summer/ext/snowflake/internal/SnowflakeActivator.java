
package com.dc.summer.ext.snowflake.internal;

import com.dc.summer.model.preferences.DBPPreferenceStore;
import org.eclipse.core.runtime.Plugin;
import com.dc.summer.model.impl.preferences.BundlePreferenceStore;
import org.osgi.framework.BundleContext;

/**
 * The activator class controls the plug-in life cycle
 */
public class SnowflakeActivator extends Plugin {

    // The plug-in ID
    public static final String PLUGIN_ID = "com.dc.summer.ext.snowflake";

    // The shared instance
    private static SnowflakeActivator plugin;

    // The preferences
    private DBPPreferenceStore preferences;

    public SnowflakeActivator() {
    }

    @Override
    public void start(BundleContext context) throws Exception {
        super.start(context);
        plugin = this;
        preferences = new BundlePreferenceStore(getBundle());
    }

    @Override
    public void stop(BundleContext context) throws Exception {
        plugin = null;
        super.stop(context);
    }

    public static SnowflakeActivator getDefault() {
        return plugin;
    }

    public DBPPreferenceStore getPreferences() {
        return preferences;
    }
}