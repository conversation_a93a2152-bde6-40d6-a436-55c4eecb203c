
package com.dc.summer.ext.clickhouse.model;

import com.dc.summer.ext.generic.model.GenericDataTypeArray;
import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.model.DBUtils;
import com.dc.summer.ext.generic.model.GenericDataType;
import com.dc.summer.ext.generic.model.GenericDataTypeCache;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

class ClickhouseDataTypeCache extends GenericDataTypeCache {

    public ClickhouseDataTypeCache(GenericStructContainer container) {
        super(container);
    }

    @Override
    protected void addCustomObjects(List<GenericDataType> genericDataTypes) {
        if (DBUtils.findObject(genericDataTypes, "Int128") == null) {
            genericDataTypes.add(new GenericDataType(owner, Types.NUMERIC, "Int128", "Int128", false, false, 0, 0, 0));
        }
        if (DBUtils.findObject(genericDataTypes, "Int256") == null) {
            genericDataTypes.add(new GenericDataType(owner, Types.NUMERIC, "Int256", "Int256", false, false, 0, 0, 0));
        }
        if (DBUtils.findObject(genericDataTypes, "UInt128") == null) {
            genericDataTypes.add(new GenericDataType(owner, Types.NUMERIC, "Int128", "Int128", false, false, 0, 0, 0));
        }
        if (DBUtils.findObject(genericDataTypes, "UInt256") == null) {
            genericDataTypes.add(new GenericDataType(owner, Types.NUMERIC, "Int256", "Int256", false, false, 0, 0, 0));
        }
        if (DBUtils.findObject(genericDataTypes, "Decimal") == null) {
            genericDataTypes.add(new GenericDataType(owner, Types.DECIMAL, "Decimal", "Decimal", false, false, 0, 0, 0));
        }
        if (DBUtils.findObject(genericDataTypes, "Bool") == null) {
            genericDataTypes.add(new GenericDataType(owner, Types.BOOLEAN, "Bool", "Bool", false, false, 0, 0, 0));
        }
        // Add array data types
        for (GenericDataType dt : new ArrayList<>(genericDataTypes)) {
            genericDataTypes.add(new GenericDataTypeArray(dt.getParentObject(), Types.ARRAY, "Array(" + dt.getName() + ")", "Array of " + dt.getName(), dt));
        }
        // Driver error - missing data types
        if (DBUtils.findObject(genericDataTypes, "DateTime64") == null) {
            genericDataTypes.add(new GenericDataType(owner, Types.TIMESTAMP, "DateTime64", "DateTime64", false, false, 0, 0, 0));
        }
    }

}
