package com.dc.parser.ext.informix.statement.ddl;

import com.dc.parser.ext.informix.segment.InformixObjectNameSegment;
import com.dc.parser.ext.informix.statement.InformixStatement;
import com.dc.parser.model.statement.ddl.CreateSynonymStatement;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class InformixCreateSynonymStatement extends CreateSynonymStatement implements InformixStatement {
    InformixObjectNameSegment name;
    InformixObjectNameSegment fromName;
}
