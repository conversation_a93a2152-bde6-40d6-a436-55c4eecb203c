package com.dc.parser.ext.informix.statement.ddl;

import com.dc.parser.ext.informix.segment.InformixObjectNameSegment;
import com.dc.parser.ext.informix.statement.InformixStatement;
import com.dc.parser.model.statement.ddl.AlterSequenceStatement;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class InformixAlterSequenceStatement extends AlterSequenceStatement implements InformixStatement {
    InformixObjectNameSegment name;
}
