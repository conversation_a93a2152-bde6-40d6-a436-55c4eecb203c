package com.dc.parser.ext.informix.statement.dml;

import com.dc.parser.ext.informix.statement.InformixStatement;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class InformixSaveExternalDirectivesStatement extends AbstractSQLStatement implements InformixStatement {

    private SubquerySegment select;

}
