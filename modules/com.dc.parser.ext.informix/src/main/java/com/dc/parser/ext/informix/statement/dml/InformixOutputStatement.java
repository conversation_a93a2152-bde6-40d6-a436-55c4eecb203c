

package com.dc.parser.ext.informix.statement.dml;

import com.dc.parser.ext.informix.statement.InformixStatement;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.statement.dml.DeleteStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Setter
@Getter
public final class InformixOutputStatement extends AbstractSQLStatement implements InformixStatement {
    
    private SubquerySegment select;
    
}
