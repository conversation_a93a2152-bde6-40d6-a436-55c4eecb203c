package com.dc.parser.ext.informix.statement.ddl;

import com.dc.parser.ext.informix.segment.DatabaseNameSegment;
import com.dc.parser.ext.informix.segment.InformixObjectNameSegment;
import com.dc.parser.ext.informix.statement.InformixStatement;
import com.dc.parser.model.statement.ddl.DropDatabaseStatement;
import com.dc.parser.model.statement.ddl.DropFunctionStatement;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class InformixDropFunctionStatement extends DropFunctionStatement implements InformixStatement {
    InformixObjectNameSegment name;
}
