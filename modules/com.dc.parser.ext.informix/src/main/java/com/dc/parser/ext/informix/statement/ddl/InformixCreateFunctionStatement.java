package com.dc.parser.ext.informix.statement.ddl;

import com.dc.parser.ext.informix.segment.InformixObjectNameSegment;
import com.dc.parser.ext.informix.statement.InformixStatement;
import com.dc.parser.model.statement.ddl.CreateFunctionStatement;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class InformixCreateFunctionStatement extends CreateFunctionStatement implements InformixStatement {
    InformixObjectNameSegment name;
}
