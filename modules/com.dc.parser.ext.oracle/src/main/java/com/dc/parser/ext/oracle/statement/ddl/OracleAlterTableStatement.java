
package com.dc.parser.ext.oracle.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.ddl.column.alter.ModifyCollectionRetrievalSegment;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.parser.ext.oracle.statement.OracleStatement;

import java.util.Optional;

/**
 * Oracle alter table statement.
 */
@Getter
@Setter
public final class OracleAlterTableStatement extends AlterTableStatement implements OracleStatement {
    
    private ModifyCollectionRetrievalSegment modifyCollectionRetrieval;
    
    /**
     * Get modify collection retrieval segment.
     * 
     * @return modify collection retrieval
     */
    public Optional<ModifyCollectionRetrievalSegment> getModifyCollectionRetrieval() {
        return Optional.ofNullable(modifyCollectionRetrieval);
    }
}
