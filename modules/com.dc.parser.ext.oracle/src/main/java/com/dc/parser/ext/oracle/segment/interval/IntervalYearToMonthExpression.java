
package com.dc.parser.ext.oracle.segment.interval;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;

/**
 * Interval year to month expression.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class IntervalYearToMonthExpression implements ExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final String year;
    
    private final String to;
    
    private final String month;
    
    @Setter
    private Integer leadingFieldPrecision;
    
    @Override
    public String getText() {
        return null;
    }
}
