package com.dc.parser.ext.oracle.segment;

import com.dc.parser.ext.oracle.enums.HintSegmentType;
import com.dc.parser.model.segment.SQLSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;


@RequiredArgsConstructor
@Getter
@Setter
public class HintSegment implements SQLSegment {
    private final int startIndex;

    private final int stopIndex;

    private final String text;

    private HintSegmentType hintSegmentType;

    public String getHint() {
        int start = hintSegmentType.getStart().length();
        int end = hintSegmentType.getEnd().length();
        return text.substring(start, text.length() - end).trim();
    }

}
