package com.dc.parser.ext.oracle.statement.dcl;

import com.dc.parser.ext.oracle.statement.OracleStatement;
import com.dc.parser.model.statement.dcl.CreateUserStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.RequiredArgsConstructor;

/**
 * Oracle create user statement.
 */
@RequiredArgsConstructor
public final class OracleCreateUserStatement extends CreateUserStatement implements OracleStatement {

    private final IdentifierValue username;
}
