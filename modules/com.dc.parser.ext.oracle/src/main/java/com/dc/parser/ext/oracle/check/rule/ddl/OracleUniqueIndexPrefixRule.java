package com.dc.parser.ext.oracle.check.rule.ddl;

import com.dc.parser.ext.oracle.enums.IndexSpecification;
import com.dc.parser.ext.oracle.statement.ddl.OracleAlterIndexStatement;
import com.dc.parser.ext.oracle.statement.ddl.OracleCreateIndexStatement;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.ddl.UniqueIndexPrefixRule;
import com.dc.parser.model.segment.ddl.index.IndexNameSegment;
import com.dc.parser.model.statement.SQLStatement;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;

public class OracleUniqueIndexPrefixRule extends UniqueIndexPrefixRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof OracleCreateIndexStatement) && !(sqlStatement instanceof OracleAlterIndexStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        if (sqlStatement instanceof OracleCreateIndexStatement) {
            OracleCreateIndexStatement checkStatement = (OracleCreateIndexStatement) sqlStatement;
            if (!IndexSpecification.UNIQUE.equals(checkStatement.getSpecification())) {
                return CheckResult.DEFAULT_SUCCESS_RESULT;
            }
        } else {
            OracleAlterIndexStatement checkStatement = (OracleAlterIndexStatement) sqlStatement;
            if (parameter.isUniqueIndex() && checkStatement.getRenameIndex().isPresent() && checkStatement.getRenameIndex().get().getIndexName() != null && checkStatement.getRenameIndex().get().getIndexName().getIdentifier() != null) {
                String indexName = checkStatement.getRenameIndex().get().getIndexName().getIdentifier().getValue().toLowerCase(Locale.ROOT);
                String prefix = parameter.getCheckRuleContent().getValue().toLowerCase(Locale.ROOT);
                return indexName.startsWith(prefix) ? CheckResult.DEFAULT_SUCCESS_RESULT : CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }

        return super.check(sqlStatement, parameter);
    }

    @Override
    public void prepare(SQLStatement sqlStatement, CheckRuleParameter parameter, Function<String, List<Map<String, Object>>> function) {

        if (!(sqlStatement instanceof OracleAlterIndexStatement) || parameter.isUniqueIndex()) {
            return;
        }

        OracleAlterIndexStatement checkStatement = (OracleAlterIndexStatement) sqlStatement;

        if (checkStatement.getIndex().isPresent() && checkStatement.getIndex().get().getIndexName() != null) {
            IndexNameSegment indexNameSegment = checkStatement.getIndex().get().getIndexName();

            if (indexNameSegment.getIdentifier() != null ) {
                String indexName = indexNameSegment.getIdentifier().getValue();

                String schemaName = parameter.getDefaultSchemaName();
                if (checkStatement.getIndex().get().getOwner().isPresent() && checkStatement.getIndex().get().getOwner().get().getIdentifier() != null) {
                    schemaName = checkStatement.getIndex().get().getOwner().get().getIdentifier().getValue();
                }

                String queryIndexCountSql = getQueryUniqueIndexCountSql(schemaName, indexName);

                List<Map<String, Object>> apply = function.apply(queryIndexCountSql);
                if (!apply.isEmpty() && apply.get(0).get("COUNT") != null) {
                    int count = Integer.parseInt(apply.get(0).get("COUNT").toString()) ;
                    parameter.setUniqueIndex(count > 0);
                }
            }
        }
    }

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.ORACLE_.getValue() + super.getType();
    }

    public String getQueryUniqueIndexCountSql(String schemaName, String indexName) {
        return String.format("SELECT COUNT(*) as \"COUNT\"\n" +
                "FROM all_indexes\n" +
                "WHERE upper(INDEX_NAME) = upper('%s')\n" +
                "AND upper(owner) = upper('%s')\n" +
                "AND uniqueness = 'UNIQUE'", indexName, schemaName);
    }

}
