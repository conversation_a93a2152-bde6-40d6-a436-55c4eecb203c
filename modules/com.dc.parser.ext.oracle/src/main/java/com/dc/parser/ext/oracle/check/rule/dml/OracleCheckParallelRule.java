package com.dc.parser.ext.oracle.check.rule.dml;

import com.dc.parser.ext.oracle.check.rule.listener.OracleCheckParalleListener;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.dml.CheckParallelRule;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.statement.SQLStatement;
import org.antlr.v4.runtime.tree.ParseTreeWalker;

public class OracleCheckParallelRule extends CheckParallelRule {

    private final ParseTreeWalker parseTreeWalker = ParseTreeWalker.DEFAULT;

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement.getParseTree() != null) {
            String parallellimitCount = parameter.getCheckRuleContent().getFirstValue();
            OracleCheckParalleListener listener = new OracleCheckParalleListener(Integer.parseInt(parallellimitCount));
            parseTreeWalker.walk(listener, sqlStatement.getParseTree());
            if (listener.isExceedParallelLimitCount()) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.ORACLE_.getValue() + super.getType();
    }
}
