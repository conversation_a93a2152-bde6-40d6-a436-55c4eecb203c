package com.dc.summer.ext.mongodb.model;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.CommonUtils;

public class MGDataSourceMongoDB extends MGDataSource {
   public MGDataSourceMongoDB(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
      super(monitor, container);
   }

   public boolean isEvalSupported() {
      return !this.isServerVersionAtLeast(4, 2) && !CommonUtils.toBoolean(this.getContainer().getConnectionConfiguration().getProperty("forceClientSideJS"));
   }

   public String getDatabaseType() {
      return "MongoDB";
   }
}
