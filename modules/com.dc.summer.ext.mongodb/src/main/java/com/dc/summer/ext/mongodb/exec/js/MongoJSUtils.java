package com.dc.summer.ext.mongodb.exec.js;

import com.dc.summer.ext.mongodb.data.format.RegExp;
import com.dc.summer.model.exec.DBCException;
import com.mongodb.BasicDBList;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.mongodb.WriteConcern;
import com.mongodb.client.model.*;
import lombok.SneakyThrows;
import org.bson.BsonRegularExpression;
import org.bson.BsonValue;
import org.bson.Document;
import com.dc.code.NotNull;
import com.dc.summer.Log;
import org.bson.conversions.Bson;
import org.bson.types.Code;
import org.mozilla.javascript.*;
import org.mozilla.javascript.regexp.NativeRegExp;

public class MongoJSUtils {
    private static final Log log = Log.getLog(MongoJSUtils.class);

    public static List<IndexModel> createIndexModelList(List<Map<String, Object>> documents) {
        List<IndexModel> result = new ArrayList<>();
        for (Map<String, Object> document : documents) {
            result.add(new IndexModel(new Document(unwrapMap(document))));
        }
        return result;
    }

    public static List<Document> createDocumentList(List<Map<String, Object>> documents) throws DBCException {
        List<Document> result = new ArrayList<>();
        try {
            for (Map<String, Object> document : documents) {
                result.add(new Document(unwrapMap(document)));
            }
        } catch (Exception e) {
            throw new DBCException(String.format("创建文档列表异常，请您检查参数后重试。(%s)", e.getMessage()));
        }
        return result;
    }

    public static @NotNull Map<String, Object> unwrapMap(@NotNull Map<String, Object> map) {
        Map<String, Object> uMap = new LinkedHashMap<>(map);

        for (Map.Entry<String, Object> stringObjectEntry : uMap.entrySet()) {
            Object value = unwrapObject(stringObjectEntry.getValue());
            stringObjectEntry.setValue(value);
        }

        return uMap;
    }

    static @NotNull Document createCommandDocument(String command, Map<String, Object> document) {
        Document commandDocument = new Document(command, 1);
        commandDocument.putAll(unwrapMap(document));
        return commandDocument;
    }

    static @NotNull Document createDocument(Map<String, Object> document) {
        return new Document(unwrapMap(document));
    }

    private static Collation getCollation(Map<String, Object> collation) {
        Collation.Builder builder = Collation.builder();
        if (collation.get("locale") != null) {
            builder.locale((String) collation.get("locale"));
        }
        if (collation.get("caseLevel") != null) {
            builder.caseLevel((Boolean) collation.get("caseLevel"));
        }
        if (collation.get("caseFirst") != null) {
            builder.collationCaseFirst(CollationCaseFirst.fromString((String) collation.get("caseFirst")));
        }
        if (collation.get("numericOrdering") != null) {
            builder.numericOrdering((Boolean) collation.get("numericOrdering"));
        }
        if (collation.get("alternate") != null) {
            builder.collationAlternate(CollationAlternate.fromString((String) collation.get("alternate")));
        }
        if (collation.get("maxVariable") != null) {
            builder.collationMaxVariable(CollationMaxVariable.fromString((String) collation.get("maxVariable")));
        }
        if (collation.get("backwards") != null) {
            builder.backwards((Boolean) collation.get("backwards"));
        }
        if (collation.get("normalization") != null) {
            builder.backwards((Boolean) collation.get("normalization"));
        }
        return builder.build();
    }

    @SuppressWarnings({"deprecation", "unchecked"})
    @SneakyThrows({InstantiationException.class, IllegalAccessException.class})
    static <T> T createOptions(Map<String, Object> document, Class<T> clazz) {
        T t = clazz.newInstance();
        for (String key : document.keySet()) {
            Object value = document.get(key);
            List<Class<?>> parameterTypes = new ArrayList<>();
            List<Object> args = new ArrayList<>();
            String methodName = key;
            try {
                switch (key) {
                    // boolean
                    case "background":
                    case "hidden":
                    case "sparse":
                    case "unique":
                    case "upsert":
                    case "bypassDocumentValidation":
                    case "ordered":
                    case "capped":
                        parameterTypes.add(boolean.class);
                        args.add(value);
                        break;
                    // String
                    case "textIndexVersion":
                        methodName = "textVersion";
                        parameterTypes.add(String.class);
                        args.add(value);
                        break;
                    case "language_override":
                        methodName = "languageOverride";
                        parameterTypes.add(String.class);
                        args.add(value);
                        break;
                    case "default_language":
                        methodName = "defaultLanguage";
                        parameterTypes.add(String.class);
                        args.add(value);
                        break;
                    case "defaultLanguage":
                    case "languageOverride":
                    case "name":
                    case "hintString":
                        parameterTypes.add(String.class);
                        args.add(value);
                        break;
                    // Bson
                    case "storageEngine":
                        if (clazz == CreateCollectionOptions.class) {
                            methodName = "storageEngineOptions";
                        }
                    case "wildcardProjection":
                    case "weights":
                    case "partialFilterExpression":
                    case "hint":
                    case "let": // variables
                    case "projection":
                    case "sort":
                        parameterTypes.add(Bson.class);
                        args.add(createDocument((Map<String, Object>) value));
                        break;
                    // List
                    case "arrayFilters":
                        parameterTypes.add(List.class);
                        args.add(createDocumentList((List<Map<String, Object>>) value));
                        break;
                    // Collation
                    case "collation":
                        parameterTypes.add(Collation.class);
                        args.add(getCollation((Map<String, Object>) value));
                        break;
                    // ReturnDocument
                    case "returnDocument":
                        parameterTypes.add(ReturnDocument.class);
                        args.add(((boolean) value) ? ReturnDocument.AFTER : ReturnDocument.BEFORE);
                        break;
                    // Integer
                    case "2dsphereIndexVersion":
                        methodName = "sphereVersion";
                        parameterTypes.add(Integer.class);
                        args.add(value);
                        break;
                    case "version":
                    case "sphereVersion":
                    case "bits":
                        parameterTypes.add(Integer.class);
                        args.add(value);
                        break;
                    // Double
                    case "bucketSize":
                    case "min":
                        parameterTypes.add(Double.class);
                        args.add(value);
                        break;
                    case "max":
                        if (clazz == CreateCollectionOptions.class) {
                            methodName = "maxDocuments";
                            parameterTypes.add(long.class);
                        } else {
                            parameterTypes.add(Double.class);
                        }
                        args.add(value);
                        break;
                    // long
                    case "size":
                        methodName = "sizeInBytes";
                        parameterTypes.add(long.class);
                        args.add(value);
                        break;
                    case "expireAfterSeconds":
                        methodName = "expireAfter";
                        if (clazz == IndexOptions.class) {
                            parameterTypes.add(Long.class);
                        } else {
                            parameterTypes.add(long.class);
                        }
                        parameterTypes.add(TimeUnit.class);
                        args.add(value);
                        args.add(TimeUnit.SECONDS);
                        break;
                    case "maxTimeMS":
                        methodName = "maxTime";
                        parameterTypes.add(long.class);
                        parameterTypes.add(TimeUnit.class);
                        args.add(value);
                        args.add(TimeUnit.MILLISECONDS);
                        break;
                    // IndexOptionDefaults
                    case "indexOptionDefaults":
                        parameterTypes.add(IndexOptionDefaults.class);
                        args.add(new IndexOptionDefaults().storageEngine(createDocument((Map<String, Object>) value)));
                        break;
                    // TimeSeriesOptions
                    case "timeseries":
                        parameterTypes.add(TimeSeriesOptions.class);
                        args.add(getTimeSeriesOptions((Map<String, Object>) value));
                        break;
                    // BsonValue
                    case "comment":
                        parameterTypes.add(BsonValue.class);
                        args.add("");
                        throw new NoSuchMethodException("[comment] function no such [BsonValue] method.");
                    // ValidationOptions
                    case "validator":
                    case "validationLevel":
                    case "validationAction":
                        setValidationOptions(t, document);
                        continue;
                    default:
                        log.debug(String.format("[%s] 没有方法 : %s", clazz.getSimpleName(), key));
                }
                clazz.getDeclaredMethod(methodName, parameterTypes.toArray(new Class[0])).invoke(t, args.toArray());
            } catch (Exception e) {
                log.info(String.format("[%s] 转换错误 : %s", clazz.getSimpleName(), e.getMessage()));
            }
        }
        return t;
    }
    static WriteConcern getWriteConcern(Map<String, Object> options) {
        if (options.get("writeConcern") != null) {
            Object w = null;
            Boolean j = null;
            Integer wtimeout = null;
            Map<String, Object> writeConcern = (Map<String, Object>) options.get("writeConcern");
            if (writeConcern.get("w") != null) {
                w = writeConcern.get("w");
            }
            if (writeConcern.get("j") != null) {
                j = (Boolean) writeConcern.get("j");
            }
            if (writeConcern.get("wtimeout") != null) {
                wtimeout = (Integer) writeConcern.get("wtimeout");
            }
            try {
                Constructor<WriteConcern> constructor = WriteConcern.class.getConstructor(Object.class, Integer.class, Boolean.class);
                constructor.setAccessible(true);
                return constructor.newInstance(w, wtimeout, j);
            } catch (Exception e) {
                log.error(e);
            }
        }
        return null;
    }

    private static TimeSeriesOptions getTimeSeriesOptions(Map<String, Object> map) {
        TimeSeriesOptions timeSeriesOptions = new TimeSeriesOptions((String) map.get("timeField"));
        if (map.get("metaField") != null) {
            timeSeriesOptions.metaField(map.get("metaField").toString());
        }
        if (map.get("granularity") != null) {
            timeSeriesOptions.granularity(TimeSeriesGranularity.valueOf(map.get("granularity").toString().toUpperCase()));
        }
        return timeSeriesOptions;
    }

    @SneakyThrows
    private static <T> void setValidationOptions(T t, Map<String, Object> map) {
        Class<?> clazz = t.getClass();
        ValidationOptions validationOptions = (ValidationOptions) clazz.getDeclaredMethod("getValidationOptions").invoke(t);
        if (map.get("validator") != null) {
            validationOptions.validator(createDocument((Map<String, Object>) map.get("validator")));
        }
        if (map.get("validationLevel") != null) {
            validationOptions.validationLevel(ValidationLevel.fromString(map.get("validationLevel").toString()));
        }
        if (map.get("validationAction") != null) {
            validationOptions.validationAction(ValidationAction.fromString(map.get("validationAction").toString()));
        }
    }

    static @NotNull Document createOptionsDocument(Map<String, Object> document, String options) {
        return new Document(unwrapMap((Map<String, Object>) document.get(options)));
    }

    @SuppressWarnings("unchecked")
    public static void filterRegExp(Object command) {
        if (command instanceof NativeObject) {
            Map<String, Object> map = (Map<String, Object>) command;
            NativeObject nativeObject = (NativeObject) command;
            for (String key : map.keySet()) {
                Object value = map.get(key);
                if (value instanceof Map) {
                    filterRegExp(value);
                } else if (value instanceof List) {
                    ((List<?>) value).forEach(MongoJSUtils::filterRegExp);
                } else if (value instanceof NativeRegExp) {
                    BsonRegularExpression expression = new RegExp(String.valueOf(value)).getBsonRegularExpression();
                    nativeObject.put(key, nativeObject, expression);
                }
            }
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public static Object unwrapObject(Object obj) {
        if (obj instanceof Map) {
            return unwrapMap((Map) obj);
        } else if (!(obj instanceof Scriptable)) {
            return obj;
        } else {
            Object value = null;
            Scriptable so = (Scriptable) obj;
            BasicDBList uList;
            Object item;
            Iterator var7;
            List<Object> list;
            switch (so.getClassName()) {
                case "JavaList":
                    list = (List) ((NativeJavaList) so).unwrap();
                    uList = new BasicDBList();
                    var7 = list.iterator();

                    while (var7.hasNext()) {
                        item = var7.next();
                        uList.add(unwrapObject(item));
                    }

                    value = uList;
                    break;
                case "JavaMap":
                    value = unwrapMap((Map) ((NativeJavaMap) so).unwrap());
                    break;
                case "Date":
                    value = new Date((long) ScriptRuntime.toNumber(so));
                    break;
                case "Array":
                    list = (NativeArray) so;
                    uList = new BasicDBList();
                    var7 = list.iterator();

                    while (var7.hasNext()) {
                        item = var7.next();
                        uList.add(unwrapObject(item));
                    }

                    value = uList;
                    break;
                case "RegExp":
                    String[] expressions = so.toString().split("/");
                    value = new BsonRegularExpression(expressions[1], expressions.length > 2 ? expressions[2] : null);
                    break;
                case "JavaObject":
                    value = ((NativeJavaObject) so).unwrap();
                    break;
                case "Function":
                    try {
                        Method decompile = NativeFunction.class.getDeclaredMethod("decompile", int.class, int.class);
                        decompile.setAccessible(true);
                        Object function = decompile.invoke(so, 0, 0);
                        value = new Code(String.valueOf(function));
                    } catch (Exception e) {
                        log.error(e);
                    }
            }

            if (value == null) {
                log.debug("WARNING: Cannot unwrap JS object " + so.getClassName() + " [" + so + "]. Convert to null.");
            }

            return value;
        }
    }

}
