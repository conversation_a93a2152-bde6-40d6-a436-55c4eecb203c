package com.dc.summer.ext.mongodb.model;

import com.dc.code.NotNull;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPSaveableObject;
import com.dc.summer.model.access.DBAUser;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.struct.DBSObject;

public class MG<PERSON>ser implements DBAUser, DBPSaveableObject {
   private static final Log log = Log.getLog(MGUser.class);
   private MGDatabase database;
   private String name;

   public MGUser(MGDatabase database, String name) {
      this.database = database;
      this.name = name;
   }

   @Property(
      viewable = true,
      order = 1
   )
   public @NotNull String getName() {
      return this.name;
   }

   public boolean isPersisted() {
      return true;
   }

   public void setPersisted(boolean persisted) {
   }

   public String getDescription() {
      return null;
   }

   public DBSObject getParentObject() {
      return this.database;
   }

   public @NotNull DBPDataSource getDataSource() {
      return this.database.getDataSource();
   }
}
