package com.dc.summer.ext.mongodb.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.mongodb.data.MGDocument;
import com.dc.summer.ext.mongodb.data.MGListValue;
import com.dc.summer.ext.mongodb.exec.MGExecutionContext;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.document.data.DBMapValue;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.mongodb.client.MongoClient;

import java.util.*;
import java.util.function.Function;

import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.eclipse.core.runtime.Platform;
import com.dc.code.NotNull;
import com.dc.summer.Log;
import com.dc.summer.model.DBPTransactionIsolation;
import com.dc.summer.model.impl.AbstractDataSourceInfo;
import com.dc.utils.CommonUtils;
import org.osgi.framework.Bundle;
import org.osgi.framework.Version;

public class MGDataSourceInfo extends AbstractDataSourceInfo {
    private static final Log log = Log.getLog(MGDataSourceInfo.class);
    private final MGDataSource dataSource;
    private final MongoClient client;
    private Map<String, Object> databaseVersion;

    public MGDataSourceInfo(MGDataSource dataSource, MongoClient client) {
        this.dataSource = dataSource;
        this.client = client;
    }

    public boolean isReadOnlyData() {
        return false;
    }

    public boolean isReadOnlyMetaData() {
        return false;
    }

    public String getDatabaseProductName() {
        return this.dataSource.getDatabaseType();
    }

    public String getDatabaseProductVersion() {
        Map<String, Object> productDetails = this.getDatabaseProductDetails();
        return CommonUtils.toString(productDetails.get("version"));
    }

    public @NotNull Map<String, Object> getDatabaseProductDetails() {
        if (this.databaseVersion == null) {
            try {
                this.databaseVersion = this.client.getDatabase("local").runCommand(new Document("buildInfo", 1));
            } catch (Exception var2) {
                log.warn("Error getting build info", var2);
            }

            if (this.databaseVersion == null) {
                this.databaseVersion = new HashMap();
            }
        }

        return this.databaseVersion;
    }

    public boolean supportsBatchUpdates() {
        return true;
    }

    public Version getDatabaseVersion() {
        return new Version(this.getDatabaseProductVersion());
    }

    public String getDriverName() {
        return "mongo-java-driver";
    }

    public String getDriverVersion() {
        return "4.5.1";
    }

    private Bundle getDriverBundle() {
        Bundle bundle = Platform.getBundle("org.mongodb.mongo-java-driver");
        if (bundle == null) {
            throw new IllegalStateException("Mongo driver bundle 'org.mongodb.mongo-java-driver' not found");
        } else {
            return bundle;
        }
    }

    public String getSchemaTerm() {
        return null;
    }

    public String getProcedureTerm() {
        return null;
    }

    public String getCatalogTerm() {
        return "Database";
    }

    public boolean supportsTransactions() {
        return true;
    }

    public boolean supportsSavepoints() {
        return false;
    }

    public boolean supportsReferentialIntegrity() {
        return false;
    }

    public boolean supportsIndexes() {
        return true;
    }

    public boolean supportsStoredCode() {
        return true;
    }

    public Collection<DBPTransactionIsolation> getSupportedTransactionsIsolation() {
        return null;
    }

    public boolean isDynamicMetadata() {
        return true;
    }

    @Override
    public boolean supportDDLCallable() {
        return false;
    }

    @Override
    public String getTableRealNameSql(String schemaName, String tableName) {
        StringBuilder sb = new StringBuilder();
        sb.append("db.runCommand({\n" + "                listCollections : 1,\n" + "                filter : { \n" + "                    name : '").append(tableName).append("',\n");
        if (dataSource.isServerVersionAtLeast(3, 4)) {
            sb.append("                    type : 'collection' \n");
        }
        sb.append("                }\n" +
                "})");

        return sb.toString();
    }

    @Override
    public String getTableRealName(List<Map<String, Object>> list) {

        String realName = "";

        try {

            if (list.size() > 0) {
                Map<String, Object> map = list.get(0);
                if (map.get("##document") instanceof Document) {
                    Document document = (Document) map.get("##document");
                    if (document.get("cursor") instanceof Map) {
                        Map<String, Object> hashMap = (Map) document.get("cursor");
                        if (hashMap.get("firstBatch") instanceof List) {
                            List firstBatch = (List) hashMap.get("firstBatch");
                            if (firstBatch.size() > 0) {
                                Document object = (Document) (firstBatch.get(0));
                                if (object.get("name") != null) {
                                    realName = object.get("name").toString();
                                }
                            }
                        }
                    }
                } else if (map.get("##document") instanceof MGDocument) {
                    MGDocument document = (MGDocument) map.get("##document");
                    if (document.getAttributeValue("cursor") instanceof DBMapValue) {
                        DBMapValue cursor = (DBMapValue) document.getAttributeValue("cursor");
                        if (cursor.getAttributeValue("firstBatch") instanceof MGListValue) {
                            MGListValue firstBatch = (MGListValue) cursor.getAttributeValue("firstBatch");
                            if (firstBatch.size() > 0) {
                                DBMapValue object = (DBMapValue) (firstBatch.get(0));
                                if (object.getAttributeValue("name") != null) {
                                    realName = object.getAttributeValue("name").toString();
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("getTableRealName error : ", e);
        }

        return realName;
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.size(contents) == 1) {
            return generateInsertSql(schemaName, tableName, null, contents.get(0));
        }
        return generateInsertSqlBatch(schemaName, tableName, list, contents);
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {
        if (StringUtils.isNotBlank(content)) {
            return String.format("db.%s.insertOne(%s)", tableName, content);
        }
        return "";
    }

    @Override
    public String generateInsertSqlBatch(String schemaName, String tableName, List<List<SqlFieldData>> list, List<String> contents) {
        if (CollectionUtils.isNotEmpty(contents)) {
            String content = StringUtils.join(contents, ",");
            return String.format("db.%s.insertMany([%s])", tableName, content);
        }
        return "";
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {
        return String.format("db.getCollection(\"%s\").remove({})", tableName);
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {

        if (context instanceof  MGExecutionContext) {
            MongoClient mongoClient = ((MGExecutionContext) context).getClient();
            ArrayList<Map<String, Object>> returnList = new ArrayList<>();
            MongoIterable<String> strings = mongoClient.listDatabaseNames();

            for (String name : strings) {
                Map<String, Object> returnMap = new LinkedHashMap<>();
                returnMap.put("addLabel", "dc_mongo_db_schema");
                returnMap.put("username", name);
                returnMap.put("charset", "UTF-8");

                if (Arrays.asList("admin", "config", "local").contains(name)) {
                    returnMap.put("is_sys", 1);
                } else {
                    returnMap.put("is_sys", 0);
                }

                long count = 0L;
                try {
                    MongoDatabase database = mongoClient.getDatabase(name);
                    for (Document collection : database.listCollections()) {
                        if ("collection".equals(collection.getString("type"))) {
                            count++;
                        }
                    }
                } catch (Exception e) {
                    log.error("getSchemasInfo error : ", e);
                }
                returnMap.put("count", count);

                returnList.add(returnMap);
            }

            return returnList;
        }

        return new ArrayList<>();
    }

    @Override
    public boolean showInteger(DBDAttributeBinding column) {
        return !List.of("DOUBLE", "NUMBER").contains(column.getTypeName().toUpperCase(Locale.ROOT));
    }
}
