package com.dc.summer.ext.mongodb.exec;

import com.dc.config.ConfigInstance;
import com.dc.config.ConfigInstanceType;
import com.dc.summer.ext.mongodb.MGConstants;
import com.dc.summer.ext.mongodb.MGUtils;
import com.dc.summer.ext.mongodb.config.MGConfig;
import com.dc.summer.ext.mongodb.model.MGDataSource;
import com.dc.summer.ext.mongodb.model.MGDatabase;
import com.dc.summer.model.DBPExclusiveResource;
import com.dc.summer.model.exec.*;
import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;

import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.TimeUnit;
import javax.net.ssl.SSLContext;

import com.mongodb.client.internal.MongoClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPTransactionIsolation;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.impl.AbstractExecutionContext;
import com.dc.summer.model.impl.net.SSLHandlerTrustStoreImpl;
import com.dc.summer.model.net.DBWHandlerConfiguration;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSInstance;
import com.dc.summer.model.struct.rdb.DBSSchema;
import com.dc.utils.CommonUtils;

@Slf4j
public class MGExecutionContext extends AbstractExecutionContext<MGDataSource> implements DBCExecutionContextDefaults<MGDatabase, DBSSchema>, DBCTransactionManager {
    private boolean autoCommitState;
    private MongoClient client;
    private ClientSession clientSession;
    private Document buildInfo;
    private String selectedDatabase;

    private DBPConnectionConfiguration configuration;

    private boolean allowCloseClient;

    public MGExecutionContext(MGDataSource dataSource, String purpose, String selectedDatabase) {
        super(dataSource, purpose);
        this.selectedDatabase = selectedDatabase;
    }

    public MongoClient getClient() {
        return this.client;
    }

    public @Nullable ClientSession getClientSession() {
        return this.clientSession;
    }

    public void connect(DBRProgressMonitor monitor) throws DBCException {
        this.connect(monitor, null, null);
    }

    public void connect(DBRProgressMonitor monitor, Boolean autoCommit, @Nullable Integer txnLevel) throws DBCException {
        DBExecUtils.startContextInitiation(getDataSource().getContainer());

        try {
            this.autoCommitState = true;
            monitor.subTask("Open cluster session");
            this.reconnect(monitor);
            super.initContextBootstrap(monitor, this.autoCommitState);
            getDataSource().addExecutionContext(this);
        } catch (Exception var8) {
            throw new DBCException(var8, this);
        } finally {
            DBExecUtils.finishContextInitiation(getDataSource().getContainer());
        }

    }

    public @NotNull MGSession openSession(@NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionPurpose purpose, @NotNull String taskTitle) {
        MGSessionImpl mgSession = new MGSessionImpl(monitor, purpose, taskTitle, this);
        return getProxySession(mgSession);
    }

    public void checkContextAlive(DBRProgressMonitor monitor) throws DBException {
    }

    public DBSInstance getOwnerInstance() {
        return (DBSInstance) getDataSource();
    }

    public boolean isConnected() {
        return true;
    }

    @NotNull
    public InvalidateResult invalidateContext(@NotNull DBRProgressMonitor monitor, boolean closeOnFailure, DBPConnectionConfiguration configuration) throws DBException {
        this.reconnect(monitor);
        getDataSource().refreshObject(monitor);
        return InvalidateResult.RECONNECTED;
    }

    public void close() {
        synchronized (this) {
            getDataSource().removeExecutionContext(this);
            this.closeClient();
        }
    }

    public void reconnect(DBRProgressMonitor monitor) throws DBException {
        monitor.beginTask("Open " + getDataSource().getDatabaseType() + " cluster connection", 3);

        configuration = getDataSource().getContainer().getActualConnectionConfiguration();
        String hostName = configuration.getHostName();
        String hostPort = configuration.getHostPort();

        MGConfig mgConfig = ConfigInstance.getInstance(MGConfig.class);
        ConfigInstanceType mgConfigType = mgConfig.getType();
        this.allowCloseClient = mgConfigType.isAllowCloseClient();

        this.closeClient();

        String selectedDatabase = getDataSource().getDefaultDatabase();

        try {
            monitor.subTask("Setting connection parameters");
            MongoCredential credential = MGUtils.getMongoCredential(getDataSource(), configuration);
            final String conURL = getDataSource().getConnectionURL(configuration);
            log.info("==> ConnectionURL: " + conURL);

            MGClientPoolListener listener = new MGClientPoolListener();

            MongoClientSettings.Builder csBuilder = MongoClientSettings.builder();
            if (credential != null) {
                csBuilder.credential(credential);
            }

            if (configuration.getProperty("retryReads") != null) {
                csBuilder.retryReads(CommonUtils.toBoolean(configuration.getProperty("retryReads")));
            } else {
                csBuilder.retryReads(mgConfig.getRetryReads());
            }

            if (configuration.getProperty("retryWrites") != null) {
                csBuilder.retryWrites(CommonUtils.toBoolean(configuration.getProperty("retryWrites")));
            } else {
                csBuilder.retryWrites(mgConfig.getRetryWrites());
            }

            csBuilder.applyToClusterSettings((builder) -> {
                if (configuration.getProperty("serverSelectTimeout") != null) {
                    builder.serverSelectionTimeout(CommonUtils.toInt(configuration.getProperty("serverSelectTimeout")), TimeUnit.MILLISECONDS);
                } else {
                    builder.serverSelectionTimeout(mgConfig.getServerSelectionTimeoutMS(), TimeUnit.MILLISECONDS);
                }

                String replicaSet = CommonUtils.toString(configuration.getProviderProperty("replicaSet"), null);
                if (!CommonUtils.isEmpty(replicaSet)) {
                    builder.requiredReplicaSetName(replicaSet);
                }

                if (!MGUtils.isValidURL(conURL)) {
                    ServerAddress address = new ServerAddress(hostName, CommonUtils.toInt(hostPort, 27017));
                    log.debug("Connect to MongoDB server address [" + address + "]");
                    builder.hosts(Collections.singletonList(address));
                }

            });
            csBuilder.applyToSocketSettings((builder) -> {
                if (configuration.getProperty("connectTimeout") != null) {
                    builder.connectTimeout(CommonUtils.toInt(configuration.getProperty("connectTimeout")), TimeUnit.MILLISECONDS);
                } else {
                    builder.connectTimeout(mgConfig.getConnectTimeoutMS(), TimeUnit.MILLISECONDS);
                }

                if (configuration.getProperty("socketTimeout") != null) {
                    builder.readTimeout(CommonUtils.toInt(configuration.getProperty("socketTimeout")), TimeUnit.MILLISECONDS);
                } else {
                    builder.readTimeout(mgConfig.getReadTimeoutMS(), TimeUnit.MILLISECONDS);
                }

            });
            csBuilder.applyToConnectionPoolSettings((builder) -> {
                if (configuration.getProperty("maxConnectionIdleTime") != null) {
                    builder.maxConnectionIdleTime(CommonUtils.toInt(configuration.getProperty("maxConnectionIdleTime")), TimeUnit.SECONDS);
                } else {
                    builder.maxConnectionIdleTime(mgConfig.getMaxConnectionIdleTimeMS(), TimeUnit.MILLISECONDS);
                }

                if (configuration.getProperty("maxConnectionLifeTime") != null) {
                    builder.maxConnectionLifeTime(CommonUtils.toInt(configuration.getProperty("maxConnectionLifeTime")), TimeUnit.SECONDS);
                } else {
                    builder.maxConnectionLifeTime(mgConfig.getMaxConnectionLifeTimeMS(), TimeUnit.MILLISECONDS);
                }

                if (configuration.getProperty("maxWaitTime") != null) {
                    builder.maxWaitTime(CommonUtils.toInt(configuration.getProperty("maxWaitTime")), TimeUnit.MILLISECONDS);
                } else {
                    builder.maxWaitTime(mgConfig.getMaxWaitTimeMS(), TimeUnit.MILLISECONDS);
                }

                builder.maxSize(mgConfig.getMaxSize());

                builder.minSize(mgConfig.getMinSize());

                builder.maxConnecting(mgConfig.getMaxConnecting());

                builder.addConnectionPoolListener(listener);
            });
            csBuilder.applyToServerSettings((builder) -> {
                if (configuration.getProperty("heartbeatFrequency") != null) {
                    builder.heartbeatFrequency(CommonUtils.toInt(configuration.getProperty("heartbeatFrequency")), TimeUnit.MILLISECONDS);
                } else {
                    builder.heartbeatFrequency(mgConfig.getHeartbeatFrequencyMS(), TimeUnit.MILLISECONDS);
                }

            });
            monitor.worked(1);
            DBWHandlerConfiguration sslConfig = configuration.getHandler("mongo_ssl");
            if (sslConfig == null) {
                sslConfig = getDataSource().getCustomSSLConfiguration(monitor, configuration);
            }

            if (sslConfig != null && sslConfig.isEnabled()) {
                DBWHandlerConfiguration finalSslConfig = sslConfig;
                csBuilder.applyToSslSettings((sslBuilder) -> {
                    monitor.subTask("Initializing SSL");
                    sslBuilder.enabled(true);
                    boolean skipHostValidation = finalSslConfig.getBooleanProperty("sslInvalidHostNameAllowed");
                    if (skipHostValidation) {
                        sslBuilder.invalidHostNameAllowed(true);
                    }

                    try {
                        SSLHandlerTrustStoreImpl.initializeTrustStore(monitor, getDataSource(), finalSslConfig);
                        SSLContext sslContext = SSLHandlerTrustStoreImpl.createTrustStoreSslContext(getDataSource(), finalSslConfig);
                        sslBuilder.context(sslContext);
                        log.debug("Initialized " + sslContext.getProtocol() + " protocol: " + sslContext.getProvider());
                    } catch (Exception var6) {
                        log.error("Error initializing SSL trust store", var6);
                    }

                });
            }

            monitor.worked(1);
            monitor.subTask("Creating client connection");
            if (MGUtils.isValidURL(conURL)) {
                String url = MGUtils.transformURL(credential, conURL);
                log.debug("Connect to MongoDB server using URL [" + url + "]");
                ConnectionString connectionString = new ConnectionString(url);
                csBuilder.applyConnectionString(connectionString);
                if (!CommonUtils.isEmpty(connectionString.getDatabase())) {
                    selectedDatabase = connectionString.getDatabase();
                }
            }

            long ctm = System.currentTimeMillis();
            try {
                this.client = newMongoClient(mgConfigType, getDataSource().getContainer().getId(), csBuilder.build(), listener);

                monitor.subTask("Connecting to the " + getDataSource().getDatabaseType() + " server");
                if (CommonUtils.isEmpty(selectedDatabase)) {
                    selectedDatabase = "local";
                }

                MongoDatabase database = this.client.getDatabase(selectedDatabase);
                this.buildInfo = database.runCommand(new Document("buildInfo", 1));

                try {
                    this.clientSession = this.client.startSession();
                } catch (Exception var17) {
                    log.debug("Sessions are not supported by cluster: {}", var17.getMessage());
                }
            } finally {
                log.info("<== ConnectionTime: " + (System.currentTimeMillis() - ctm) + " ms");
            }

        } catch (Throwable e) {
            this.closeClient();
            if (e instanceof DBException) {
                throw e;
            }
            throw new DBException("Error connecting to " + getDataSource().getDatabaseType() + " instance [" + hostName + "]", e);
        } finally {
            monitor.done();
        }

    }

    private void closeClient() {
        if (this.clientSession != null && this.allowCloseClient) {
            try {
                this.clientSession.close();
            } catch (Exception e) {
                log.error("MG this.clientSession.close", e);
            }
            this.clientSession = null;
        }
        if (this.client != null && this.allowCloseClient) {
            try {
                this.client.close();
            } catch (Exception e) {
                log.error("MG this.client.close", e);
            }
            this.client = null;
        }
        super.closeContext();
    }

    private MongoClient newMongoClient(ConfigInstanceType mgConfigType, String id, MongoClientSettings settings, MGClientPoolListener listener) throws DBCException {
        if (mgConfigType.equals(ConfigInstanceType.SINGLE)) {
            return MongoClients.create(settings);
        }

        MGClientPool pool = MGClientPool.getInstance();

        if (!pool.hasClient(id) || configuration.isRefreshPool() || !equalsPassword(settings, pool.getClient(id))) {
            MongoClient mongoClient = null;
            DBPExclusiveResource exclusiveLock = getDataSource().getContainer().getExclusiveLock();
            Object lock = exclusiveLock.acquireExclusiveLock();
            try {
                if (!pool.hasClient(id) || configuration.isRefreshPool() || !equalsPassword(settings, pool.getClient(id))) {

                    mongoClient = MongoClients.create(settings);
                    MongoDatabase database = mongoClient.getDatabase("admin");
                    database.runCommand(new Document("ping", 1));

                    if (configuration.isRefreshPool()) {
                        closeClient(pool.getClient(id));
                    }

                    pool.registerClient(id, mongoClient, listener);
                    configuration.setRefreshPool(false);
                }
            } catch (Exception e) {
                if (mongoClient != null) {
                    closeClient(mongoClient);
                }
                throw new DBCException("无法打开 MongoDB 客户端。", e);
            } finally {
                exclusiveLock.releaseExclusiveLock(lock);
            }
        }
        listener = pool.getListener(id);
        log.info("Get dataSource: {}'s createConnectionThread: {}", listener.toString(), listener.getThread());
        return pool.getClient(id);
    }

    private boolean equalsPassword(MongoClientSettings settings, MongoClient mongoClient) {
        MongoCredential oldCredential = ((MongoClientImpl) mongoClient).getSettings().getCredential();
        if (oldCredential == null) {
            return false;
        }
        MongoCredential newCredential = settings.getCredential();
        if (newCredential == null) {
            return false;
        }
        return Arrays.equals(oldCredential.getPassword(), newCredential.getPassword());
    }

    private void closeClient(MongoClient mongoClient) {
        try {
            mongoClient.close();
        } catch (Exception e) {
            log.error("无法关闭 MongoDB 客户端。", e);
        }
    }

    public Document getBuildInfo() {
        return this.buildInfo;
    }

    public @Nullable MGExecutionContext getContextDefaults() {
        return this;
    }

    @Override
    public String getProcessId() {
        // TODO mongodb
        return null;
    }

    @Override
    public void execPrefs(DBRProgressMonitor monitor, String prefs) throws DBCException {
    }

    @Override
    public DBPConnectionConfiguration getConfiguration() {
        return configuration;
    }

    public String getSelectedDatabase() {
        return this.selectedDatabase;
    }

    public MGDatabase getDefaultCatalog() {
        return CommonUtils.isEmpty(this.selectedDatabase) ? null : this.getDataSource().getDatabase(this.selectedDatabase);
    }

    public DBSSchema getDefaultSchema() {
        return null;
    }

    public boolean supportsCatalogChange() {
        return true;
    }

    public boolean supportsSchemaChange() {
        return false;
    }

    @Override
    public void setDefaultCatalog(DBRProgressMonitor monitor, MGDatabase catalog, DBSSchema schema, boolean force) throws DBCException {
        MGDatabase oldSelectedEntity = this.getDefaultCatalog();
        this.selectedDatabase = catalog.getName();
        DBUtils.fireObjectSelectionChange(oldSelectedEntity, catalog, force);
    }

    @Override
    public void setDefaultSchema(DBRProgressMonitor monitor, DBSSchema schema, boolean force) throws DBCException {
        throw new DBCFeatureNotSupportedException();
    }

    @Override
    public boolean refreshDefaults(DBRProgressMonitor monitor, boolean useBootstrapSettings) throws DBException {
        return true;
    }

    @Override
    public DBPTransactionIsolation getTransactionIsolation() throws DBCException {
        return MGConstants.DEFAULT_TXN_ISOLATION;
    }

    @Override
    public void setTransactionIsolation(@NotNull DBRProgressMonitor monitor, @NotNull DBPTransactionIsolation transactionIsolation) throws DBCException {
        throw new DBCFeatureNotSupportedException();
    }

    public boolean isAutoCommit() throws DBCException {
        return this.clientSession == null || !this.clientSession.hasActiveTransaction();
    }

    public void setAutoCommit(@NotNull DBRProgressMonitor monitor, boolean autoCommit) throws DBCException {
        if (this.clientSession == null) {
            throw new DBCFeatureNotSupportedException();
        } else {
            try {
                if (autoCommit) {
                    if (!this.isAutoCommit()) {
                        this.clientSession.abortTransaction();
                    }
                } else if (this.isAutoCommit()) {
                    this.clientSession.startTransaction();
                }
            } finally {
                QMUtils.getDefaultHandler().handleTransactionAutocommit(this, autoCommit);
            }

        }
    }

    public boolean supportsSavepoints() {
        return false;
    }

    public DBCSavepoint setSavepoint(@NotNull DBRProgressMonitor monitor, String name) throws DBCException {
        throw new DBCFeatureNotSupportedException();
    }

    public void releaseSavepoint(@NotNull DBRProgressMonitor monitor, @NotNull DBCSavepoint savepoint) throws DBCException {
        throw new DBCFeatureNotSupportedException();
    }

    public void commit() throws DBCException {
        if (this.clientSession == null) {
            throw new DBCFeatureNotSupportedException();
        } else {
            this.clientSession.commitTransaction();
        }
    }

    public void rollback() throws DBCException {
        if (this.clientSession == null) {
            throw new DBCFeatureNotSupportedException();
        } else {
            this.clientSession.abortTransaction();
        }
    }

    public boolean isSupportsTransactions() {
        return this.clientSession != null;
    }
}
