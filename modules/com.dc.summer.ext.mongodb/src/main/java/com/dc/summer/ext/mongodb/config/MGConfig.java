package com.dc.summer.ext.mongodb.config;

import com.dc.config.ConfigInstance;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class MGConfig extends ConfigInstance {

    private Boolean retryReads;
    private Boolean retryWrites;
    private Long serverSelectionTimeoutMS;
    private Integer connectTimeoutMS;
    private Integer readTimeoutMS;
    private Integer maxSize;
    private Integer minSize;
    private Integer maxConnecting;
    private Long maxWaitTimeMS;
    private Long maxConnectionLifeTimeMS;
    private Long maxConnectionIdleTimeMS;
    private Long heartbeatFrequencyMS;

    @Override
    protected String getFolder() {
        return "mongodb";
    }

}
