package com.dc.summer.ext.tidb.model;

import com.dc.summer.ext.mysql.model.MySQLDataSourceInfo;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;

public class TidbDataSourceInfo extends MySQLDataSourceInfo {
    public TidbDataSourceInfo(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public boolean isQueryStatementLockTable() {
        return false;
    }
}
