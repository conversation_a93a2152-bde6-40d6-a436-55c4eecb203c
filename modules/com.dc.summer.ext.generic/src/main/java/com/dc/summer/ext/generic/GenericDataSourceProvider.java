
package com.dc.summer.ext.generic;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.PropertyDescriptor;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;
import com.dc.code.NotNull;
import com.dc.summer.ext.generic.model.meta.GenericMetaModelDescriptor;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.impl.jdbc.JDBCDataSourceProvider;
import com.dc.summer.model.impl.jdbc.JDBCURL;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GenericDataSourceProvider extends JDBCDataSourceProvider {

    private final Map<String, GenericMetaModelDescriptor> metaModels = new HashMap<>();
    private static final String EXTENSION_ID = "com.dc.summer.generic.meta";

    public GenericDataSourceProvider()
    {
        metaModels.put(GenericConstants.META_MODEL_STANDARD, new GenericMetaModelDescriptor());

        List<String> replacedModels = new ArrayList<>();
        IExtensionRegistry extensionRegistry = Global.getExtensionRegistry();
        IConfigurationElement[] extElements = extensionRegistry.getConfigurationElementsFor(EXTENSION_ID);
        for (IConfigurationElement ext : extElements) {
            GenericMetaModelDescriptor metaModel = new GenericMetaModelDescriptor(ext);
            metaModels.put(metaModel.getId(), metaModel);
            replacedModels.addAll(metaModel.getModelReplacements());
        }
        for (String rm : replacedModels) {
            metaModels.remove(rm);
        }
        for (GenericMetaModelDescriptor metaModel : new ArrayList<>(metaModels.values())) {
            for (String driverClass : ArrayUtils.safeArray(metaModel.getDriverClass())) {
                metaModels.put(driverClass, metaModel);
            }
        }
    }

    @Override
    public long getFeatures()
    {
        return FEATURE_CATALOGS | FEATURE_SCHEMAS;
    }

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo)
    {
        return JDBCURL.generateUrlByTemplate(driver, connectionInfo);
    }

    @NotNull
    @Override
    public DBPDataSource openDataSource(
        @NotNull DBRProgressMonitor monitor,
        @NotNull DBPDataSourceContainer container)
        throws DBException
    {
        GenericMetaModelDescriptor metaModel = null;
        Object metaModelId = container.getDriver().getDriverParameter(GenericConstants.PARAM_META_MODEL);
        if (metaModelId != null && !GenericConstants.META_MODEL_STANDARD.equals(metaModelId)) {
            metaModel = metaModels.get(metaModelId.toString());
            if (metaModel == null) {
                log.warn("Meta model '" + metaModelId + "' not recognized. Default one will be used");
            }
        }
        if (metaModel == null) {
            // Try to get model by driver class
            metaModel = metaModels.get(container.getDriver().getDriverClassName());
        }
        if (metaModel == null) {
            metaModel = getStandardMetaModel();
        }
        GenericMetaModel metaModelInstance = metaModel.getInstance();
        return metaModelInstance.createDataSourceImpl(monitor, container);
    }

    protected GenericMetaModelDescriptor getStandardMetaModel() {
        return metaModels.get(GenericConstants.META_MODEL_STANDARD);
    }

    @Override
    public DBPPropertyDescriptor[] getConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, DBPConnectionConfiguration connectionInfo) throws DBException {
        DBPPropertyDescriptor[] connectionProperties = super.getConnectionProperties(monitor, driver, connectionInfo);
        if (connectionProperties == null || connectionProperties.length == 0) {
            // Try to get list of supported properties from custom driver config
            String driverParametersString = CommonUtils.toString(driver.getDriverParameter(GenericConstants.PARAM_DRIVER_PROPERTIES));
            if (!driverParametersString.isEmpty()) {
                String[] propList = driverParametersString.split(",");
                connectionProperties = new DBPPropertyDescriptor[propList.length];
                for (int i = 0; i < propList.length; i++) {
                    String propName = propList[i].trim();
                    connectionProperties[i] = new PropertyDescriptor(
                        ModelMessages.model_jdbc_driver_properties,
                        propName,
                        propName,
                        null,
                        String.class,
                        false,
                        null,
                        null,
                        true);
                }
            }
        }
        return connectionProperties;
    }
}
