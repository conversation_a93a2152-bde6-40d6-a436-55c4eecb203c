package com.dc.summer.ext.gbase8s.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.informix.model.InformixDataSourceInfo;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.*;
import java.util.function.Function;

public class GBase8sDataSourceInfo extends InformixDataSourceInfo {

    public GBase8sDataSourceInfo(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {
        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get schema list",
                "select name as owner from sysmaster:sysdatabases");

        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            Map<String, Object> returnMap = new LinkedHashMap<>();
            returnMap.put("addLabel", "dc_gbase8s_db_schema");
            returnMap.put("username", ((String) map.get("owner")).trim());

            try {
                List<Map<String, Object>> listCount = DBExecUtils.executeQuery(monitor, context, "get schema count",
                        "select count(0) as count from " + ((String) map.get("owner")).trim() + ":systables where tabtype = 'T'");
                returnMap.put("count", listCount.size() > 0 ? Long.parseLong(listCount.get(0).get("count").toString()) : 0L);
            } catch (DBException e) {
                returnMap.put("count", 0L);
            }

            List<Map<String, Object>> listCharset = DBExecUtils.executeQuery(monitor, context, "get schema count",
                    "select dbs_collate as value from sysmaster:sysdbslocale " +
                            "where dbs_dbsname = '" + ((String) map.get("owner")).trim() + "'");

            returnMap.put("charset", listCharset.size() > 0 ? ((String) listCharset.get(0).get("value")).trim() : "");

            if (Arrays.asList("sysadmin", "sysmaster", "sysuser", "sysutils")
                    .contains(((String) map.get("owner")).trim())) {
                returnMap.put("is_sys", 1);
            } else {
                returnMap.put("is_sys", 0);
            }

            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public String getTableColumnSql(String schemaName, String tableName) {
        String query = "SELECT      \n" +
                "            t.tabname as table_name,\n" +
                "            c.colname as column_name,\n" +
                "            c.collength as collength,\n" +
                "            schema_precision(c.coltype, c.extended_id, c.collength) as data_length,\n" +
                "            schema_numscale(c.coltype, c.collength) as area,\n" +
                "            schema_numprecradix(c.coltype) as precision,\n" +
                "            lower(schema_coltypename(coltype, extended_id)) as data_type,\n" +
                "            schema_coltypename(c.coltype,c.extended_id) as col_type,\n" +
                "            schema_isnullable(c.coltype) as nullable,\n" +
                "            (\n" +
                "                CASE\n" +
                "                    d.type\n" +
                "                WHEN 'L' THEN\n" +
                "                    get_default_value(c.coltype, c.extended_id, c.collength, d.default::lvarchar(256))::VARCHAR(254)\n" +
                "                WHEN 'C' THEN\n" +
                "                    'current'::VARCHAR(254)\n" +
                "                WHEN 'S' THEN\n" +
                "                    'dbservername'::VARCHAR(254)\n" +
                "                WHEN 'U' THEN\n" +
                "                    'user'::VARCHAR(254)\n" +
                "                WHEN 'T' THEN\n" +
                "                    'today'::VARCHAR(254)\n" +
                "                ELSE\n" +
                "                    NULL::VARCHAR(254)\n" +
                "                END\n" +
                "            ) as column_default,\n" +
                "            (\n" +
                "                case\n" +
                "                when pk.colno > 0 then\n" +
                "                    1\n" +
                "                else\n" +
                "                    0\n" +
                "                end\n" +
                "            ) as is_primary_key,\n" +
                "            '' as special_column\n";
        if (isQueryComment()) {
            query += "       , (select co.comments from " + schemaName + ":syscolcomms co where c.tabid = co.tabid and c.colno = co.colno) as comments\n";
        }
        query += "        FROM\n" +
                schemaName + ":systables t,\n" +
                "            outer " + schemaName + ":sysdefaults d,\n" +
                schemaName + ":syscolumns c,\n" +
                "            outer (\n" +
                "                select\n" +
                "                    c.colno, t.tabid\n" +
                "                from\n" +
                schemaName + ":systables as t,\n" +
                schemaName + ":sysindexes as si,\n" +
                schemaName + ":sysconstraints as so,\n" +
                schemaName + ":syscolumns as c\n" +
                "                where\n" +
                "                    t.tabid = so.tabid\n" +
                "                    and so.idxname = si.idxname\n" +
                "                    and c.tabid = t.tabid\n" +
                "                    and so.constrtype = 'P'\n" +
                "                    and t.tabname = '" + tableName + "'\n" +
                "                    and (\n" +
                "                        c.colno = ABS(si.part1)\n" +
                "                        or c.colno = ABS(si.part2)\n" +
                "                        or c.colno = ABS(si.part3)\n" +
                "                        or c.colno = ABS(si.part4)\n" +
                "                        or c.colno = ABS(si.part5)\n" +
                "                        or c.colno = ABS(si.part6)\n" +
                "                        or c.colno = ABS(si.part7)\n" +
                "                        or c.colno = ABS(si.part8)\n" +
                "                        or c.colno = ABS(si.part9)\n" +
                "                        or c.colno = ABS(si.part10)\n" +
                "                        or c.colno = ABS(si.part11)\n" +
                "                        or c.colno = ABS(si.part12)\n" +
                "                        or c.colno = ABS(si.part13)\n" +
                "                        or c.colno = ABS(si.part14)\n" +
                "                        or c.colno = ABS(si.part15)\n" +
                "                        or c.colno = ABS(si.part16)\n" +
                "                    )\n" +
                "            ) as pk\n" +
                "        where t.tabid = c.tabid and d.tabid = t.tabid AND c.colno = d.colno\n" +
                "            and pk.colno = c.colno and t.tabid = pk.tabid\n" +
                "            and t.tabname = '" + tableName + "'\n" +
                "        order by c.colno";
        return query;
    }

}
