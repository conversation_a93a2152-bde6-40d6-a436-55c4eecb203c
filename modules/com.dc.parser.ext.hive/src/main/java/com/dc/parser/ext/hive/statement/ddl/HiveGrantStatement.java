package com.dc.parser.ext.hive.statement.ddl;

import com.dc.parser.ext.hive.segment.ddl.HivePrivilegeTypeSpecSegment;
import com.dc.parser.ext.hive.statement.HiveStatement;
import com.dc.parser.model.statement.dcl.GrantStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;

/**
 * Hive grant privileges statement.
 */
@Getter
@Setter
public final class HiveGrantStatement extends GrantStatement implements HiveStatement {

    private Collection<HivePrivilegeTypeSpecSegment> privileges;

    private Collection<String> usernames;

    private Collection<String> groupNames;

    private Collection<String> granteeRoleNames;

    private String privilegeObject;
}
