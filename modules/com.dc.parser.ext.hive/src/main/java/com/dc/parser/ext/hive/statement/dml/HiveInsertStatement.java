package com.dc.parser.ext.hive.statement.dml;

import com.dc.parser.ext.hive.statement.HiveStatement;
import com.dc.parser.model.segment.dml.assignment.SetAssignmentSegment;
import com.dc.parser.model.segment.dml.column.OnDuplicateKeyColumnsSegment;
import com.dc.parser.model.statement.dml.InsertStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * Hive insert statement.
 */
@Setter
public final class HiveInsertStatement extends InsertStatement implements HiveStatement {
    
    private SetAssignmentSegment setAssignment;
    
    private OnDuplicateKeyColumnsSegment onDuplicateKeyColumns;

    /**
     * Get set assignment segment.
     *
     * @return set assignment segment
     */
    @Override
    public Optional<SetAssignmentSegment> getSetAssignment() {
        return Optional.ofNullable(setAssignment);
    }

    /**
     * Get On duplicate key columns segment.
     *
     * @return on duplicate key columns segment
     */
    @Override
    public Optional<OnDuplicateKeyColumnsSegment> getOnDuplicateKeyColumns() {
        return Optional.ofNullable(onDuplicateKeyColumns);
    }
}
