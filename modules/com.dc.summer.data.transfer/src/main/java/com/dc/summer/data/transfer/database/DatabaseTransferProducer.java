

package com.dc.summer.data.transfer.database;

import org.eclipse.core.runtime.IAdaptable;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.*;
import com.dc.summer.model.app.DBPProject;
import com.dc.summer.model.data.DBDDataFilter;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.impl.AbstractExecutionSource;
import com.dc.summer.model.impl.DataSourceContextProvider;
import com.dc.summer.model.meta.DBSerializable;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.sql.SQLQuery;
import com.dc.summer.model.sql.SQLQueryContainer;
import com.dc.summer.model.sql.SQLScriptContext;
import com.dc.summer.model.sql.SQLScriptElement;
import com.dc.summer.model.sql.data.SQLQueryDataContainer;
import com.dc.summer.model.struct.DBSDataContainer;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.summer.model.task.DBTTask;
import com.dc.summer.model.task.DBTaskUtils;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.summer.runtime.serialize.DBPObjectSerializer;
import com.dc.summer.data.transfer.IDataTransferConsumer;
import com.dc.summer.data.transfer.IDataTransferNodePrimary;
import com.dc.summer.data.transfer.IDataTransferProcessor;
import com.dc.summer.data.transfer.IDataTransferProducer;
import com.dc.summer.data.transfer.internal.DTMessages;
import com.dc.summer.utils.GeneralUtils;
import com.dc.utils.CommonUtils;

import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Data container transfer producer
 */
@DBSerializable("databaseTransferProducer")
public class DatabaseTransferProducer implements IDataTransferProducer<DatabaseProducerSettings>, IDataTransferNodePrimary {

    private static final Log log = Log.getLog(DatabaseTransferProducer.class);

    private final DBCStatistics producerStatistics = new DBCStatistics();

    protected DBSDataContainer dataContainer;
    @Nullable
    protected DBDDataFilter dataFilter;
    @Nullable
    protected String defaultCatalog;
    @Nullable
    protected String defaultSchema;

    public DatabaseTransferProducer() {
    }

    public DatabaseTransferProducer(@NotNull DBSDataContainer dataContainer) {
        this.dataContainer = dataContainer;
    }

    public DatabaseTransferProducer(@NotNull DBSDataContainer dataContainer, @Nullable DBDDataFilter dataFilter) {
        this.dataContainer = dataContainer;
        this.dataFilter = dataFilter;
    }

    public void setDataContainer(@NotNull DBSDataContainer dataContainer) {
        this.dataContainer = dataContainer;
    }

    @Override
    public DBSDataContainer getDatabaseObject() {
        return dataContainer;
    }

    @Override
    public String getObjectName() {
        final SQLQueryContainer queryContainer = GeneralUtils.adapt(dataContainer, SQLQueryContainer.class);
        if (queryContainer != null) {
            return CommonUtils.getSingleLineString(queryContainer.getQuery().toString());
        }
        return dataContainer == null ? "?" : DBUtils.getObjectFullName(dataContainer, DBPEvaluationContext.DML);
    }

    @Override
    public String getObjectContainerName() {
        DBPDataSourceContainer container = getDataSourceContainer();
        return container != null ? container.getName() : "?";
    }

    @Override
    public boolean isConfigurationComplete() {
        return dataContainer != null;
    }

    private DBPDataSourceContainer getDataSourceContainer() {
        if (dataContainer != null) {
            return dataContainer.getDataSource().getContainer();
        }
        return null;
    }

    @Nullable
    public String getDefaultCatalog() {
        return defaultCatalog;
    }

    public void setDefaultCatalog(@Nullable String defaultCatalog) {
        this.defaultCatalog = defaultCatalog;
    }

    @Nullable
    public String getDefaultSchema() {
        return defaultSchema;
    }

    public void setDefaultSchema(@Nullable String defaultSchema) {
        this.defaultSchema = defaultSchema;
    }

    @Override
    public void transferData(
            @NotNull DBRProgressMonitor monitor,
            @NotNull IDataTransferConsumer consumer,
            @Nullable IDataTransferProcessor processor,
            @NotNull DatabaseProducerSettings settings,
            @Nullable DBTTask task,
            Runnable recoverBefore, Runnable recoverAfter)
            throws DBException {
        String contextTask = DTMessages.data_transfer_wizard_job_task_export;

        DBSDataContainer databaseObject = getDatabaseObject();
        if (databaseObject == null) {
            throw new DBException("No input database object found");
        }
        DBPDataSource dataSource = databaseObject.getDataSource();
        assert (dataSource != null);

        long readFlags = DBSDataContainer.FLAG_NONE;
        if (settings.isSelectedColumnsOnly()) {
            readFlags |= DBSDataContainer.FLAG_USE_SELECTED_COLUMNS;
        }
        if (settings.isSelectedRowsOnly()) {
            readFlags |= DBSDataContainer.FLAG_USE_SELECTED_ROWS;
        }

        boolean newConnection = settings.isOpenNewConnections() && !getDatabaseObject().getDataSource().getContainer().getDriver().isEmbedded();
        boolean forceDataReadTransactions = Boolean.TRUE.equals(dataSource.getDataSourceFeature(DBPDataSource.FEATURE_LOB_REQUIRE_TRANSACTIONS));
        boolean selectiveExportFromUI = settings.isSelectedColumnsOnly() || settings.isSelectedRowsOnly();

        DBCExecutionContext context;
        if (dataContainer instanceof DBPContextProvider) {
            context = ((DBPContextProvider) dataContainer).getExecutionContext();
        } else {
            context = DBUtils.getDefaultContext(dataContainer, false);
        }
        if (context == null) {
            throw new DBCException("Can't retrieve execution context from data container " + dataContainer);
        }
        if (!selectiveExportFromUI && newConnection) {
            context = DBUtils.getObjectOwnerInstance(getDatabaseObject()).openIsolatedContext(monitor, "Data transfer producer", context, true, null);
            DBExecUtils.setExecutionContextDefaults(monitor, dataSource, context, defaultCatalog, null, defaultSchema);
        }
        if (task != null) {
            DBTaskUtils.initFromContext(monitor, task, context);
        }

        long finalReadFlags = readFlags;
        DBExecUtils.tryExecuteRecover(context, dataSource, param -> {

            try (DBCSession session = param.openSession(monitor, DBCExecutionPurpose.USER_SCRIPT, contextTask)) {

                Boolean oldAutoCommit = null;
                DBCSavepoint savepoint = null;
                try {
                    AbstractExecutionSource transferSource = new AbstractExecutionSource(dataContainer, param, consumer);
                    session.enableLogging(false);
                    if (!selectiveExportFromUI && (newConnection || forceDataReadTransactions)) {
                        // Turn off auto-commit in source DB
                        // Auto-commit has to be turned off because some drivers allows to read LOBs and
                        // other complex structures only in transactional mode
                        try {
                            DBCTransactionManager txnManager = DBUtils.getTransactionManager(param);
                            if (txnManager != null && txnManager.isSupportsTransactions()) {
                                oldAutoCommit = txnManager.isAutoCommit();
                                txnManager.setAutoCommit(monitor, false);
                                if (txnManager.supportsSavepoints()) {
                                    savepoint = txnManager.setSavepoint(monitor, "Data transfer start");
                                }
                            }
                        } catch (DBCException e) {
                            log.warn("Can't change auto-commit", e);
                        }

                    }
                    long totalRows = 0;
                    if (settings.isQueryRowCount() && dataContainer.isFeatureSupported(DBSDataContainer.FEATURE_DATA_COUNT)) {
                        monitor.beginTask(DTMessages.data_transfer_wizard_job_task_retrieve, 1);
                        try {
                            totalRows = dataContainer.countData(transferSource, session, dataFilter, finalReadFlags);
                        } catch (Throwable e) {
                            log.warn("Can't retrieve row count from '" + dataContainer.getName() + "'", e);
                            try {
                                DBCTransactionManager txnManager = DBUtils.getTransactionManager(session.getExecutionContext());
                                if (txnManager != null && !txnManager.isAutoCommit()) {
                                    txnManager.rollback();
                                }
                            } catch (Throwable e1) {
                                log.warn("Error rolling back transaction", e1);
                            }
                        } finally {
                            monitor.done();
                        }
                    }

                    monitor.beginTask(DTMessages.data_transfer_wizard_job_task_export_table_data, (int) totalRows);

                    try {
                        monitor.subTask("Read data");

                        // Perform export
                        if (settings.getExtractType() == DatabaseProducerSettings.ExtractType.SINGLE_QUERY) {
                            // Just do it in single query
                            DBCStatistics stat = dataContainer.readData(transferSource, session, consumer, dataFilter, settings.getOffset(), settings.getExportLimit(), finalReadFlags, settings.getFetchSize(), settings.getStage(), settings.getData());
                            producerStatistics.setQueryText(stat.getQueryText());
                            producerStatistics.accumulate(stat);
                            producerStatistics.setMetaData(stat.getMetaData());
                            producerStatistics.setError(stat.getError());
                        } else {
                            // Read all data by segments
                            long offset = 0;
                            long segmentSize = settings.getSegmentSize();
                            for (; ; ) {
                                DBCStatistics statistics = dataContainer.readData(
                                        transferSource, session, consumer, dataFilter, offset, segmentSize, finalReadFlags, settings.getFetchSize(), settings.getData());
                                if (statistics == null || statistics.getRowsFetched() < segmentSize) {
                                    // Done
                                    break;
                                }
                                producerStatistics.accumulate(statistics);
                                offset += statistics.getRowsFetched();
                            }
                        }
                    } finally {
                        monitor.done();
                    }

                } catch (DBException e) {
                    throw new InvocationTargetException(e);
                } finally {
                    try {
                        if (!selectiveExportFromUI && (newConnection || forceDataReadTransactions)) {
                            DBCTransactionManager txnManager = DBUtils.getTransactionManager(param);
                            if (txnManager != null && txnManager.isSupportsTransactions()) {
                                if (!txnManager.isAutoCommit()) {
                                    txnManager.rollback();
                                }
                                if (savepoint != null) {
                                    txnManager.releaseSavepoint(monitor, savepoint);
                                }
                                if (oldAutoCommit != null) {
                                    txnManager.setAutoCommit(monitor, oldAutoCommit);
                                }
                            }
                        }
                        if (!selectiveExportFromUI && newConnection) {
                            param.close();
                        }
                    } catch (DBException dbe) {
                        log.error("transfer data finally error.", dbe);
                    }
                }
            }
        }, recoverBefore, recoverAfter);

    }

    @Override
    public boolean equals(Object obj) {
        return obj instanceof DatabaseTransferProducer &&
                CommonUtils.equalObjects(dataContainer, ((DatabaseTransferProducer) obj).dataContainer) &&
                CommonUtils.equalObjects(dataFilter, ((DatabaseTransferProducer) obj).dataFilter);
    }

    @Override
    public int hashCode() {
        int result = 1;
        result = 31 * result + (dataContainer != null ? dataContainer.hashCode() : 0);
        result = 31 * result + (dataFilter != null ? dataFilter.hashCode() : 0);
        return result;
    }

    @Override
    @NotNull
    public DBCStatistics getStatistics() {
        return producerStatistics;
    }

    public static class ObjectSerializer implements DBPObjectSerializer<DBTTask, DatabaseTransferProducer> {

        @Override
        public void serializeObject(DBRRunnableContext runnableContext, DBTTask context, DatabaseTransferProducer object, Map<String, Object> state) {
            DBSDataContainer dataContainer = object.dataContainer;
            if (dataContainer instanceof IAdaptable) {
                DBSDataContainer nestedDataContainer = ((IAdaptable) dataContainer).getAdapter(DBSDataContainer.class);
                if (nestedDataContainer != null) {
                    dataContainer = nestedDataContainer;
                }
            }
            if (dataContainer instanceof DBSEntity) {
                state.put("type", "entity");
                if (dataContainer.getDataSource() != null) {
                    state.put("project", dataContainer.getDataSource().getContainer().getProject().getName());
                }
                state.put("entityId", DBUtils.getObjectFullId(dataContainer));
            } else if (dataContainer instanceof SQLQueryContainer) {
                state.put("type", "query");
                SQLQueryContainer queryContainer = (SQLQueryContainer) dataContainer;
                DBPDataSourceContainer dataSource = queryContainer.getDataSourceContainer();
                if (dataSource != null) {
                    state.put("project", dataSource.getProject().getName());
                    state.put("dataSource", dataSource.getId());
                    if (object.defaultCatalog != null) {
                        state.put("defaultCatalog", object.defaultCatalog);
                    }
                    if (object.defaultSchema != null) {
                        state.put("defaultSchema", object.defaultSchema);
                    }
                }
                SQLScriptElement query = queryContainer.getQuery();
                state.put("query", query.getOriginalText());
            } else {
                state.put("type", "unknown");
                log.error("Unsupported producer data container: " + dataContainer);
            }
            if (object.dataFilter != null) {
                Map<String, Object> dataFilterState = new LinkedHashMap<>();
                object.dataFilter.serialize(dataFilterState);
                state.put("dataFilter", dataFilterState);
            }
        }

        @Override
        public DatabaseTransferProducer deserializeObject(DBRRunnableContext runnableContext, DBTTask objectContext, Map<String, Object> state) throws DBCException {
            DatabaseTransferProducer producer = new DatabaseTransferProducer();
            try {
                runnableContext.run(true, true, monitor -> {
                    try {
                        String selType = CommonUtils.toString(state.get("type"));
                        String projectName = CommonUtils.toString(state.get("project"));
                        DBPProject project = CommonUtils.isEmpty(projectName) ? null : DBWorkbench.getPlatform().getWorkspace().getProject(projectName);
                        if (project == null) {
                            project = objectContext.getProject();
                        }
                        switch (selType) {
                            case "entity": {
                                String id = CommonUtils.toString(state.get("entityId"));
                                producer.dataContainer = (DBSDataContainer) DBUtils.findObjectById(monitor, project, id);
                                if (producer.dataContainer == null) {
                                    throw new DBException("Can't find database object '" + id + "'");
                                }
                                break;
                            }
                            case "query": {
                                String dsId = CommonUtils.toString(state.get("dataSource"));
                                String queryText = CommonUtils.toString(state.get("query"));
                                DBPDataSourceContainer ds = project.getDataSourceRegistry().getDataSource(dsId);
                                if (ds == null) {
                                    throw new DBCException("Can't find datasource " + dsId);
                                }
                                if (!ds.isConnected()) {
                                    ds.connect(monitor, true, true);
                                }
                                DBPDataSource dataSource = ds.getDataSource();
                                SQLQuery query = new SQLQuery(dataSource, queryText);
                                DataSourceContextProvider taskContextProvider = new DataSourceContextProvider(dataSource);
                                SQLScriptContext scriptContext = new SQLScriptContext(null,
                                        taskContextProvider, null, new PrintWriter(System.err, true), null);
                                scriptContext.setVariables(DBTaskUtils.getVariables(objectContext));
                                producer.defaultCatalog = CommonUtils.toString(state.get("defaultCatalog"), null);
                                producer.defaultSchema = CommonUtils.toString(state.get("defaultSchema"), null);
                                producer.dataContainer = new SQLQueryDataContainer(
                                        taskContextProvider,
                                        query,
                                        scriptContext,
                                        log,
                                        0,
                                        null);
                                break;
                            }
                            default:
                                log.warn("Unsupported selector type: " + selType);
                        }
                    } catch (Exception e) {
                        throw new InvocationTargetException(e);
                    }
                });
            } catch (InvocationTargetException e) {
                throw new DBCException("Error instantiating data producer", e.getTargetException());
            } catch (InterruptedException e) {
                throw new DBCException("Deserialization canceled", e);
            }

            return producer;
        }
    }
}
