
package com.dc.summer.data.transfer.task;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.exec.DBCStatistics;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.summer.model.task.DBTTask;
import com.dc.summer.model.task.DBTTaskExecutionListener;
import com.dc.summer.model.task.DBTTaskHandler;
import com.dc.summer.model.task.DBTTaskRunStatus;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.summer.data.transfer.*;
import com.dc.summer.data.transfer.database.DatabaseConsumerSettings;
import com.dc.summer.data.transfer.database.DatabaseTransferConsumer;
import com.dc.summer.data.transfer.internal.DTMessages;

import java.io.PrintStream;
import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

/**
 * DTTaskHandlerTransfer
 */
public class DTTaskHandlerTransfer implements DBTTaskHandler {
    private static final Log log = Log.getLog(DTTaskHandlerTransfer.class);

    private final DBCStatistics totalStatistics = new DBCStatistics();

    @Override
    @NotNull
    public DBTTaskRunStatus executeTask(
        @NotNull DBRRunnableContext runnableContext,
        @NotNull DBTTask task,
        @NotNull Locale locale,
        @NotNull Log log,
        @NotNull PrintStream logStream,
        @NotNull DBTTaskExecutionListener listener) throws DBException
    {
        DataTransferSettings[] settings = new DataTransferSettings[1];
        try {
            runnableContext.run(true, true, monitor -> {
                settings[0] = new DataTransferSettings(monitor, task, log, Collections.emptyMap(), new DataTransferState());
                settings[0].loadNodeSettings(monitor);
            });
        } catch (InvocationTargetException e) {
            throw new DBException("Error loading task settings", e.getTargetException());
        } catch (InterruptedException e) {
            return new DBTTaskRunStatus();
        }
        executeWithSettings(runnableContext, task, locale, log, listener, settings[0]);

        return DBTTaskRunStatus.makeStatisticsStatus(totalStatistics);
    }

    public void executeWithSettings(@NotNull DBRRunnableContext runnableContext, @Nullable DBTTask task, @NotNull Locale locale,
                                    @NotNull Log log, @NotNull DBTTaskExecutionListener listener,
                                    DataTransferSettings settings) throws DBException {
        listener.taskStarted(task);
        int indexOfLastPipeWithDisabledReferentialIntegrity = -1;
        try {
            indexOfLastPipeWithDisabledReferentialIntegrity = initializePipes(runnableContext, settings);
            Throwable error = runDataTransferJobs(runnableContext, task, locale, log, listener, settings);
            listener.taskFinished(task, null, error, settings);
        } catch (InvocationTargetException e) {
            DBWorkbench.getPlatformUI().showError(
                DTMessages.data_transfer_task_handler_unexpected_error_title,
                DTMessages.data_transfer_task_handler_unexpected_error_message,
                e.getCause()
            );
            throw new DBException("Error starting data transfer", e);
        } catch (InterruptedException e) {
            //ignore
        } finally {
            restoreReferentialIntegrity(
                runnableContext,
                settings.getDataPipes().subList(0, indexOfLastPipeWithDisabledReferentialIntegrity + 1)
            );
        }
    }

    private int initializePipes(@NotNull DBRRunnableContext runnableContext, @NotNull DataTransferSettings settings)
            throws InvocationTargetException, InterruptedException, DBException {
        int[] indexOfLastPipeWithDisabledReferentialIntegrity = new int[]{-1};
        DBException[] dbException = {null};
        List<DataTransferPipe> dataPipes = settings.getDataPipes();

        runnableContext.run(true, false, monitor -> {
            monitor.beginTask("Initialize pipes", dataPipes.size());
            try {
                for (int i = 0; i < dataPipes.size(); i++) {
                    DataTransferPipe pipe = dataPipes.get(i);
                    pipe.initPipe(settings, i, dataPipes.size());
                    IDataTransferConsumer<?, ?> consumer = pipe.getConsumer();
                    consumer.startTransfer(monitor);
                    if (enableReferentialIntegrity(consumer, monitor, false)) {
                        indexOfLastPipeWithDisabledReferentialIntegrity[0] = i;
                    }
                    monitor.worked(1);
                }
            } catch (DBException e) {
                dbException[0] = e;
            } finally {
                monitor.done();
            }
        });
        if (dbException[0] != null) {
            throw dbException[0];
        }

        return indexOfLastPipeWithDisabledReferentialIntegrity[0];
    }

    @Nullable
    private Throwable runDataTransferJobs(@NotNull DBRRunnableContext runnableContext, DBTTask task, @NotNull Locale locale,
                                     @NotNull Log log, @NotNull DBTTaskExecutionListener listener,
                                     @NotNull DataTransferSettings settings) {
        int totalJobs = settings.getDataPipes().size();
        if (totalJobs > settings.getMaxJobCount()) {
            totalJobs = settings.getMaxJobCount();
        }
        Throwable error = null;
        for (int i = 0; i < totalJobs; i++) {
            DataTransferJob job = new DataTransferJob(settings, task, locale, log, listener);
            try {
                runnableContext.run(true, true, job);
                totalStatistics.accumulate(job.getTotalStatistics());
            } catch (InvocationTargetException e) {
                error = e.getTargetException();
            } catch (InterruptedException e) {
                break;
            }
        }
        return error;
    }

    private void restoreReferentialIntegrity(@NotNull DBRRunnableContext runnableContext,
                                             @NotNull List<DataTransferPipe> pipes) throws DBException {
        DBException[] firstDBException = {null};
        try {
            runnableContext.run(true, false, monitor -> {
                try {
                    monitor.beginTask("Post transfer work", pipes.size());
                    for (DataTransferPipe pipe: pipes) {
                        try {
                            enableReferentialIntegrity(pipe.getConsumer(), monitor, true);
                        } catch (DBException e) {
                            log.debug("enabling referential integrity unexpectedly failed", e);
                            if (firstDBException[0] == null) {
                                firstDBException[0] = e;
                            }
                        }
                        monitor.worked(1);
                    }
                } finally {
                    monitor.done();
                }
            });
        } catch (InterruptedException e) {
            //ignore
        } catch (InvocationTargetException e) {
            DBWorkbench.getPlatformUI().showError(
                DTMessages.data_transfer_task_handler_resoring_referential_integrity_unexpected_error_title,
                DTMessages.data_transfer_task_handler_resoring_referential_integrity_unexpected_error_message,
                e.getCause()
            );
        }
        if (firstDBException[0] != null) {
            throw new DBException("Unable to restore referential integrity properly", firstDBException[0]);
        }
    }

    private static boolean enableReferentialIntegrity(@NotNull IDataTransferConsumer<?, ?> consumer,
                                                   @NotNull DBRProgressMonitor monitor, boolean enable) throws DBException {
        if (!(consumer instanceof DatabaseTransferConsumer)) {
            return false;
        }
        DatabaseTransferConsumer databaseTransferConsumer = (DatabaseTransferConsumer) consumer;
        DatabaseConsumerSettings settings = databaseTransferConsumer.getSettings();
        if (settings.isDisableReferentialIntegrity() && databaseTransferConsumer.supportsChangingReferentialIntegrity(monitor)) {
            databaseTransferConsumer.enableReferentialIntegrity(monitor, enable);
            return true;
        }
        return false;
    }
}
