/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2022 DBeaver Corp and others
 * Copyright (C) 2012 <PERSON> (<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.data.transfer.stream.exporter;

import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.data.transfer.DTUtils;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.data.DBDContent;
import com.dc.summer.model.data.DBDContentStorage;
import com.dc.summer.model.data.DBDDocument;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.document.data.DBListValue;
import com.dc.summer.model.document.data.DBMapValue;
import com.dc.summer.model.document.data.DBNullValue;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.data.transfer.stream.IDocumentDataExporter;
import com.dc.summer.data.transfer.stream.IStreamDataExporterSite;
import com.dc.summer.utils.ContentUtils;
import com.dc.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * JSON Exporter
 */
@Slf4j
public class DataExporterJSON extends StreamExporterAbstract implements IDocumentDataExporter {

    public static final String PROP_FORMAT_DATE_ISO = "formatDateISO";
    public static final String PROP_PRINT_TABLE_NAME = "printTableName";

    private DBDAttributeBinding[] columns;
    private String tableName;
    private int rowNum = 0;

    private boolean printTableName = true;
    private boolean formatDateISO = true;

    @Override
    public void init(IStreamDataExporterSite site) throws DBException
    {
        super.init(site);
        formatDateISO = CommonUtils.getBoolean(site.getProperties().get(PROP_FORMAT_DATE_ISO), true);
        printTableName = CommonUtils.getBoolean(site.getProperties().get(PROP_PRINT_TABLE_NAME), true);
    }

    @Override
    public void dispose()
    {
        super.dispose();
    }

    @Override
    public void createWaterMark() {

    }

    @Override
    public void exportHeader(DBCSession session) throws DBException, IOException
    {
        columns = getSite().getAttributes();
        tableName = getSite().getSource().getName();
        printHeader();
    }

    private void printHeader()
    {
        PrintWriter out = getWriter();
        if (printTableName) {
            out.write("{\n");
            out.write("\"" + JSONUtils.escapeJsonString(tableName) + "\": ");
        }
        out.write("[\n");
        rowNum = 0;
    }

    @Override
    public void exportRow(DBCSession session, DBCResultSet resultSet, Object[] row) throws DBException, IOException
    {
        PrintWriter out = getWriter();
        if (rowNum > 0) {
            out.write(",\n");
        }
        rowNum++;
        boolean hasValuedContent = false;
        if (DTUtils.isJsonDocumentResults(columns, row)) {
            DBDDocument document = (DBDDocument) row[0];
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            document.serializeDocument(session.getProgressMonitor(), buffer, StandardCharsets.UTF_8, DBDDocument.DBDDocumentType.JSON, false);
            String json = DTUtils.formatPrettyJson(buffer.toString(StandardCharsets.UTF_8), true);
            getWriter().write(json);
        } else {
            out.write("{\n");
            for (int i = 0; i < columns.length; i++) {
                DBDAttributeBinding column = columns[i];
                String columnName = column.getLabel();
                if (CommonUtils.isEmpty(columnName)) {
                    columnName = column.getName();
                }
                Object cellValue = row[i];
                if (DBUtils.isNullValue(cellValue)) {
                    continue;
                }
                if (hasValuedContent) {
                    out.write(",");
                    out.write("\n");
                }
                out.write(DTUtils.PLACEHOLDER + "\"" + JSONUtils.escapeJsonString(columnName) + "\" : ");
                if (cellValue instanceof DBNullValue) {
                    writeTextCell(out, null);
                } else if (cellValue instanceof DBDContent) {
                    // Content
                    // Inline textual content and handle binaries in some special way
                    DBDContent content = (DBDContent) cellValue;
                    try {
                        DBDContentStorage cs = content.getContents(session.getProgressMonitor());
                        if (cs != null) {
                            if (ContentUtils.isTextContent(content)) {
                                try (Reader in = cs.getContentReader()) {
                                    out.write("\"");
                                    writeCellValue(out, in);
                                    out.write("\"");
                                }
                            } else {
                                getSite().writeBinaryData(cs);
                            }
                        }
                    } finally {
                        content.release();
                    }
                } else if (cellValue instanceof DBMapValue || cellValue instanceof DBListValue) {
                    String json = DTUtils.formatPrettyJson(super.getValueDisplayString(column, cellValue), false);
                    getWriter().write(json);
                } else if (cellValue instanceof Number || cellValue instanceof Boolean) {
                    out.write(cellValue.toString());
                } else if (cellValue instanceof Date && formatDateISO) {
                    writeTextCell(out, JSONUtils.formatDate((Date) cellValue));
                } else {
                    writeTextCell(out, super.getValueDisplayString(column, cellValue));
                }
                hasValuedContent = true;
            }
            out.write("\n");
            out.write("}");
        }
    }

    @Override
    public void exportFooter(DBRProgressMonitor monitor) throws IOException
    {
        PrintWriter out = getWriter();
        out.write("\n]");
        if (printTableName) {
            out.write("}");
        }
        out.write("\n");
    }

    private static void writeTextCell(PrintWriter out, @Nullable String value)
    {
        if (value != null) {
            out.write("\"" + JSONUtils.escapeJsonString(value) + "\"");
        } else {
            out.write("null");
        }
    }

    public static void writeCellValue(PrintWriter out, Reader reader) throws IOException
    {
        // Copy reader
        char buffer[] = new char[2000];
        for (;;) {
            int count = reader.read(buffer);
            if (count <= 0) {
                break;
            }
            out.write(JSONUtils.escapeJsonString(new String(buffer, 0, count)));
        }
    }

}
