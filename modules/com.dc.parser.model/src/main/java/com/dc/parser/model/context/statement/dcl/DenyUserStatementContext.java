package com.dc.parser.model.context.statement.dcl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dcl.DenyUserStatement;
import lombok.Getter;

/**
 * Deny user statement context.
 */
@Getter
public final class DenyUserStatementContext extends CommonSQLStatementContext {

    public DenyUserStatementContext(final DenyUserStatement sqlStatement) {
        super(sqlStatement);
    }

    @Override
    public DenyUserStatement getSqlStatement() {
        return (DenyUserStatement) super.getSqlStatement();
    }
}
