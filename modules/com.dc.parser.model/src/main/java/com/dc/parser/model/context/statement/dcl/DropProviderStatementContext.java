package com.dc.parser.model.context.statement.dcl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dcl.DropProviderStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

public final class DropProviderStatementContext extends CommonSQLStatementContext {

    public DropProviderStatementContext(DropProviderStatement sqlStatement, String currentDatabaseName) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    @Override
    public DropProviderStatement getSqlStatement() {
        return (DropProviderStatement) super.getSqlStatement();
    }

    public void extractSqlAuthModel(final DropProviderStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType("PROVIDER");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }


}
