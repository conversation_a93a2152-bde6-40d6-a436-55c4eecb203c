package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.CreateRuleStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Create rule statement context.
 */
@Getter
public final class CreateRuleStatementContext extends CommonSQLStatementContext {

    private static final String RULE_TYPE = "RULE";

    public CreateRuleStatementContext(final CreateRuleStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final CreateRuleStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType(RULE_TYPE);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CreateRuleStatement getSqlStatement() {
        return (CreateRuleStatement) super.getSqlStatement();
    }
}
