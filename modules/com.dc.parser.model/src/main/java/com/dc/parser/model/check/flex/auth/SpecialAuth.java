package com.dc.parser.model.check.flex.auth;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.context.statement.ddl.CreateTableStatementContext;
import com.dc.parser.model.context.type.SchemaAvailable;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dal.UseStatement;
import com.dc.parser.model.statement.dcl.AlterUserStatement;
import com.dc.parser.model.statement.dcl.CreateUserStatement;
import com.dc.parser.model.statement.dcl.DropUserStatement;
import com.dc.parser.model.statement.ddl.*;
import com.dc.utils.bean.ClassUtils;
import org.jetbrains.annotations.Nullable;

public class SpecialAuth implements SQLAuth {

    /**
     * 检查当前的 sql，是否是特殊权限鉴权
     * 如果是，返回XXX,用来拼接：没有XXX的执行权限!
     */
    @Override
    public CheckAuthResult check(SQLStatementContext context, @Nullable CheckAuthParam parameter) {

        // 只允许范围和操作都是不限制的角色执行: 切换、创建、删除、修改数据库的操作,以及执行begin开头的sql

        if (context instanceof SchemaAvailable && ((SchemaAvailable) context).getSchemaContext() != null) {
            return check("切换 SCHEMA ");
        }

        final SQLStatement sqlStatement = context.getSqlStatement();

        // 切换数据库
        if (sqlStatement instanceof UseStatement) {
            return check(" USE ");
        }

        if (sqlStatement instanceof PLSQLBlockStatement) {
            return check(" BEGIN END ");
        }

        if (ClassUtils.isInstanceOfMultiple(sqlStatement,
                CreateUserStatement.class,
                CreateDatabaseStatement.class,
                CreateSchemaStatement.class)) {
            return check(" CREATE 数据库");
        }

        if (ClassUtils.isInstanceOfMultiple(sqlStatement,
                DropUserStatement.class,
                DropDatabaseStatement.class,
                DropSchemaStatement.class)) {
            return check(" DROP 数据库");
        }

        if (ClassUtils.isInstanceOfMultiple(sqlStatement,
                AlterUserStatement.class,
                AlterDatabaseStatement.class,
                AlterSchemaStatement.class)) {
            return check(" ALTER 数据库");
        }

        // db2 建表时指定一个不存在的schema,会先创建这个schema
        // TODO SqlActionParserServiceImpl.getSchemaUniqueKeyBySchemaName
        if (context instanceof CreateTableStatementContext && ((CreateTableStatementContext) context).isNeedCreateSchema()) {
            return check(" CREATE 数据库");
        }

        return null;
    }

    protected CheckAuthResult check(String message) {

        if (true) {
            return CheckAuthResult.buildFailResult(String.format("没有%s的执行权限!", message));
        }

        return null;
    }
}
