package com.dc.parser.model.segment.dml.exec;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.ddl.routine.FunctionNameSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

/**
 *  Execute segment.
 */
@RequiredArgsConstructor
@Getter
public class ExecSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    @Setter
    private FunctionNameSegment procedureName;
    
    private final Collection<ExpressionSegment> expressionSegments = new LinkedList<>();
}
