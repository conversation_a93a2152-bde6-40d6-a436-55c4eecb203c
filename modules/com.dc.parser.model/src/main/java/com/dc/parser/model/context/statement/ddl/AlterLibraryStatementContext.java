package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.AlterLibraryStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Alter library statement context.
 */
@Getter
public final class AlterLibraryStatementContext extends CommonSQLStatementContext {

    public AlterLibraryStatementContext(final AlterLibraryStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterLibraryStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType("LIBRARY");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        if (sqlStatement.getLibraryName() != null) {
            sqlAuthModel.setName(sqlStatement.getLibraryName().getTableName().getIdentifier().getValue());
        }
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterLibraryStatement getSqlStatement() {
        return (AlterLibraryStatement) super.getSqlStatement();
    }
}
