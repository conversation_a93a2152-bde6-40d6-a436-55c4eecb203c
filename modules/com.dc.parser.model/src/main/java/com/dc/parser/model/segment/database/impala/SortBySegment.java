package com.dc.parser.model.segment.database.impala;

import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;

@Setter
@Getter
@RequiredArgsConstructor
public class SortBySegment implements SQLSegment {

    private final int startIndex;
    private final int stopIndex;
    private final List<ColumnSegment> columns = new LinkedList<>();
}
