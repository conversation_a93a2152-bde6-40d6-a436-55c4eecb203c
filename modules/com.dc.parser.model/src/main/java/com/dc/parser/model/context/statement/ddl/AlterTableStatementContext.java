package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.ConstraintAvailable;
import com.dc.parser.model.context.type.IndexAvailable;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.AddColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.ModifyColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.ConstraintSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.AddConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.DropConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.ValidateConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.index.DropIndexDefinitionSegment;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.ddl.index.RenameIndexDefinitionSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import lombok.Getter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Alter table statement context.
 */
@Getter
public final class AlterTableStatementContext extends CommonSQLStatementContext implements TableAvailable, IndexAvailable, ConstraintAvailable {

    private final TablesContext tablesContext;

    public AlterTableStatementContext(final AlterTableStatement sqlStatement) {
        super(sqlStatement);
        tablesContext = new TablesContext(getTables(sqlStatement));
    }

    private Collection<SimpleTableSegment> getTables(final AlterTableStatement sqlStatement) {
        Collection<SimpleTableSegment> result = new LinkedList<>();
        result.add(sqlStatement.getTable());
        if (sqlStatement.getRenameTable().isPresent()) {
            result.add(sqlStatement.getRenameTable().get());
        }
        for (AddColumnDefinitionSegment each : sqlStatement.getAddColumnDefinitions()) {
            for (ColumnDefinitionSegment columnDefinition : each.getColumnDefinitions()) {
                result.addAll(columnDefinition.getReferencedTables());
            }
        }
        for (ModifyColumnDefinitionSegment each : sqlStatement.getModifyColumnDefinitions()) {
            result.addAll(each.getColumnDefinition().getReferencedTables());
        }
        for (AddConstraintDefinitionSegment each : sqlStatement.getAddConstraintDefinitions()) {
            each.getConstraintDefinition().getReferencedTable().ifPresent(result::add);
        }
        return result;
    }

    @Override
    public AlterTableStatement getSqlStatement() {
        return (AlterTableStatement) super.getSqlStatement();
    }

    @Override
    public Collection<IndexSegment> getIndexes() {
        Collection<IndexSegment> result = new LinkedList<>();
        for (AddConstraintDefinitionSegment each : getSqlStatement().getAddConstraintDefinitions()) {
            each.getConstraintDefinition().getIndexName().ifPresent(result::add);
        }
        getSqlStatement().getDropIndexDefinitions().stream().map(DropIndexDefinitionSegment::getIndexSegment).forEach(result::add);
        for (RenameIndexDefinitionSegment each : getSqlStatement().getRenameIndexDefinitions()) {
            result.add(each.getIndexSegment());
            result.add(each.getRenameIndexSegment());
        }
        return result;
    }

    @Override
    public Collection<ConstraintSegment> getConstraints() {
        Collection<ConstraintSegment> result = new LinkedList<>();
        for (AddConstraintDefinitionSegment each : getSqlStatement().getAddConstraintDefinitions()) {
            each.getConstraintDefinition().getConstraintName().ifPresent(result::add);
        }
        getSqlStatement().getValidateConstraintDefinitions().stream().map(ValidateConstraintDefinitionSegment::getConstraintName).forEach(result::add);
        getSqlStatement().getDropConstraintDefinitions().stream().map(DropConstraintDefinitionSegment::getConstraintName).forEach(result::add);
        return result;
    }

    @Override
    public Collection<ColumnSegment> getIndexColumns() {
        Collection<ColumnSegment> result = new LinkedList<>();
        for (AddConstraintDefinitionSegment each : getSqlStatement().getAddConstraintDefinitions()) {
            result.addAll(each.getConstraintDefinition().getIndexColumns());
        }
        return result;
    }
}
