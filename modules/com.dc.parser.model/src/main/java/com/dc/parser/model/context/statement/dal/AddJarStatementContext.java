package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.AddJarStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

public final class AddJarStatementContext extends CommonSQLStatementContext {


    public AddJarStatementContext(AddJarStatement sqlStatement, String currentDatabaseName) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AddJarStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_BACKUP);
        sqlAuthModel.setType(SqlConstant.KEY_DATABASE);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AddJarStatement getSqlStatement() {
        return (AddJarStatement) super.getSqlStatement();
    }
}
