package com.dc.parser.model.context.statement.dcl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dcl.CreateRoleStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Create role statement context.
 */
@Getter
public final class CreateRoleStatementContext extends CommonSQLStatementContext {

    public CreateRoleStatementContext(final CreateRoleStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final CreateRoleStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType("ROLE");
        sqlAuthModel.setSchemaName(currentDatabaseName);

        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CreateRoleStatement getSqlStatement() {
        return (CreateRoleStatement) super.getSqlStatement();
    }
}
