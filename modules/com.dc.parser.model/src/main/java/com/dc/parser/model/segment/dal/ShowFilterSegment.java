
package com.dc.parser.model.segment.dal;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;

import java.util.Optional;

/**
 * Show filter segment.
 */
@RequiredArgsConstructor
@Setter
public final class ShowFilterSegment implements SQLSegment {
    
    @Getter
    private final int startIndex;
    
    @Getter
    private final int stopIndex;
    
    private ShowLikeSegment like;
    
    private WhereSegment where;
    
    /**
     * Get like segment.
     *
     * @return like segment
     */
    public Optional<ShowLikeSegment> getLike() {
        return Optional.ofNullable(like);
    }
    
    /**
     * Get where segment.
     *
     * @return where segment
     */
    public Optional<WhereSegment> getWhere() {
        return Optional.ofNullable(where);
    }
}
