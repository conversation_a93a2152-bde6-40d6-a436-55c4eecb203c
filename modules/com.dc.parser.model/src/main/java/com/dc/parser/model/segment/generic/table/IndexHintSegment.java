
package com.dc.parser.model.segment.generic.table;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.SQLSegment;

import java.util.Collection;

/**
 * Index hint segment.
 */
@RequiredArgsConstructor
@Getter
public final class IndexHintSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final Collection<String> indexNames;
    
    private final String useType;
    
    private final String indexType;
    
    @Setter
    private String hintScope;
    
    private final String originText;
}
