
package com.dc.parser.model.segment.ddl.table;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.ddl.AlterDefinitionSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;

/**
 * Rename table definition segment.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class RenameTableDefinitionSegment implements AlterDefinitionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private SimpleTableSegment table;
    
    private SimpleTableSegment renameTable;
}
