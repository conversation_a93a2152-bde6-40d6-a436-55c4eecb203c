
package com.dc.parser.model.segment.generic.table;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.generic.AliasSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Optional;

@RequiredArgsConstructor
@Getter
public final class CollectionTableSegment implements TableSegment {
    
    private final ExpressionSegment expressionSegment;
    
    @Setter
    private AliasSegment alias;
    
    @Override
    public Optional<String> getAliasName() {
        return null == alias ? Optional.empty() : Optional.ofNullable(alias.getIdentifier().getValue());
    }
    
    @Override
    public Optional<IdentifierValue> getAlias() {
        return Optional.ofNullable(alias).map(AliasSegment::getIdentifier);
    }
    
    /**
     * Get alias segment.
     *
     * @return alias segment
     */
    public Optional<AliasSegment> getAliasSegment() {
        return Optional.ofNullable(alias);
    }
    
    @Override
    public int getStartIndex() {
        return expressionSegment.getStartIndex();
    }
    
    @Override
    public int getStopIndex() {
        return expressionSegment.getStopIndex();
    }
}
