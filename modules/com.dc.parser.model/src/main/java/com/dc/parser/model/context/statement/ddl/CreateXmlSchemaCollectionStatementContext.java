package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.CreateXmlSchemaCollectionStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Create xml schema collection statement context.
 */
@Getter
public final class CreateXmlSchemaCollectionStatementContext extends CommonSQLStatementContext {

    public CreateXmlSchemaCollectionStatementContext(final CreateXmlSchemaCollectionStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final CreateXmlSchemaCollectionStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType("XML_SCHEMA_COLLECTION");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CreateXmlSchemaCollectionStatement getSqlStatement() {
        return (CreateXmlSchemaCollectionStatement) super.getSqlStatement();
    }
}
