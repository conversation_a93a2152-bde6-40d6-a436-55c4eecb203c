package com.dc.parser.model.segment.dml.expr;

import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.List;

@RequiredArgsConstructor
@Setter
@Getter
public class NamedExpression implements ExpressionSegment {

    private final int startIndex;
    private final int stopIndex;

    private final ExpressionSegment expression;

    private final List<IdentifierValue> identifierList;

    private final String text;
}
