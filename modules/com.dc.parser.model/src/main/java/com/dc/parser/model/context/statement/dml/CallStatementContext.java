package com.dc.parser.model.context.statement.dml;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dml.CallStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Call statement context.
 */
@Getter
public final class CallStatementContext extends CommonSQLStatementContext {

    public CallStatementContext(CallStatement sqlStatement) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement);
    }

    public void extractSqlAuthModel(final CallStatement sqlStatement) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CALL);
        sqlAuthModel.setType(SqlConstant.PROCEDURE);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CallStatement getSqlStatement() {
        return (CallStatement) super.getSqlStatement();
    }
}
