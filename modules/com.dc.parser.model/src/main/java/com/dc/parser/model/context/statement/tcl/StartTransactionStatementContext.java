package com.dc.parser.model.context.statement.tcl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.tcl.StartTransactionStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Start transaction statement context.
 */
@Getter
public final class StartTransactionStatementContext extends CommonSQLStatementContext {

    public StartTransactionStatementContext(final StartTransactionStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(currentDatabaseName);
    }

    private void extractSqlAuthModel(final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_BEGIN);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public StartTransactionStatement getSqlStatement() {
        return (StartTransactionStatement) super.getSqlStatement();
    }
}
