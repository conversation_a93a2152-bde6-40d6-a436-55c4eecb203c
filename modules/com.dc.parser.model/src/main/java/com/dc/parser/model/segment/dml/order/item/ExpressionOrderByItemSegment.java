
package com.dc.parser.model.segment.dml.order.item;

import lombok.Getter;
import com.dc.infra.database.enums.NullsOrderType;
import com.dc.parser.model.enums.OrderDirection;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;

/**
 * Order by item segment for expression.
 */
@Getter
public final class ExpressionOrderByItemSegment extends TextOrderByItemSegment {
    
    private final String expression;
    
    private final ExpressionSegment expr;
    
    public ExpressionOrderByItemSegment(final int startIndex, final int stopIndex, final String expression, final OrderDirection orderDirection, final NullsOrderType nullsOrderType) {
        super(startIndex, stopIndex, orderDirection, nullsOrderType);
        this.expression = expression;
        this.expr = null;
    }
    
    public ExpressionOrderByItemSegment(final int startIndex, final int stopIndex, final String expression, final OrderDirection orderDirection, final NullsOrderType nullsOrderType,
                                        final ExpressionSegment expr) {
        super(startIndex, stopIndex, orderDirection, nullsOrderType);
        this.expression = expression;
        this.expr = expr;
    }
    
    @Override
    public String getText() {
        return expression;
    }
}
