package com.dc.parser.model.context.statement.tcl;

import com.dc.parser.model.context.segment.table.TablesContext;
import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.context.type.TableAvailable;
import com.dc.parser.model.statement.tcl.LockStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Lock statement context.
 */
@Getter
public final class LockStatementContext extends CommonSQLStatementContext implements TableAvailable {

    private final TablesContext tablesContext;

    public LockStatementContext(final LockStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);
        tablesContext = new TablesContext(sqlStatement.getTables());

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    private void extractSqlAuthModel(final LockStatement sqlStatement, final String currentDatabaseName) {
        sqlStatement.getTables().forEach(simpleTableSegment -> {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModel.setOperation(SqlConstant.KEY_LOCK);
            sqlAuthModel.setType(SqlConstant.KEY_TABLE);
            sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
            String schemaName = simpleTableSegment.getOwner().map(ownerSegment -> ownerSegment.getIdentifier().getValue()).orElse(currentDatabaseName);
            sqlAuthModel.setSchemaName(schemaName);
            addSqlAuthModel(sqlAuthModel);
        });
    }

    @Override
    public LockStatement getSqlStatement() {
        return (LockStatement) super.getSqlStatement();
    }
}
