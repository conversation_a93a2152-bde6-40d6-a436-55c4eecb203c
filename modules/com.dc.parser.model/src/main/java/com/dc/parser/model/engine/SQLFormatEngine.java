
package com.dc.parser.model.engine;

import com.dc.parser.model.api.visitor.format.SQLFormatVisitor;
import lombok.RequiredArgsConstructor;
import org.antlr.v4.runtime.tree.ParseTree;
import com.dc.infra.database.DatabaseTypedSPILoader;
import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;

import java.util.Properties;

/**
 * SQL format engine.
 */
@RequiredArgsConstructor
public final class SQLFormatEngine {
    
    private final DatabaseType databaseType;
    
    private final CacheOption cacheOption;
    
    public SQLFormatEngine(final String databaseType, final CacheOption cacheOption) {
        this(TypedSPILoader.getService(DatabaseType.class, databaseType), cacheOption);
    }
    
    /**
     * Format SQL.
     * 
     * @param sql SQL to be formatted
     * @param useCache whether to use cache
     * @param props properties
     * @return formatted SQL
     */
    public String format(final String sql, final boolean useCache, final Properties props) {
        ParseTree parseTree = new SQLParserEngine(databaseType, cacheOption).parse(sql, useCache).getRootNode();
        return DatabaseTypedSPILoader.getService(SQLFormatVisitor.class, databaseType, props).visit(parseTree);
    }
}
