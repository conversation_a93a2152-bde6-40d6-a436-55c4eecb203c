
package com.dc.parser.model.segment.ddl.constraint;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

/**
 * Constraint segment.
 */
@RequiredArgsConstructor
@Getter
public final class ConstraintSegment implements SQLSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final IdentifierValue identifier;
}
