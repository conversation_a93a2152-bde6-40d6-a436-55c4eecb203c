package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.table.RelationalTableSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateIndexStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.Collections;

public class CompositeIndexMaxRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (sqlStatement instanceof CreateIndexStatement) {
            CreateIndexStatement createIndexStatement = (CreateIndexStatement) sqlStatement;
            if (createIndexStatement.getColumns().size() > Integer.parseInt(parameter.getCheckRuleContent().getValue())) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        } else if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement createTableStatement = (CreateTableStatement) sqlStatement;

            int count = Integer.parseInt(parameter.getCheckRuleContent().getValue());

            boolean isValid = createTableStatement.getRelationalTable()
                    .map(RelationalTableSegment::getConstraintDefinitions)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(constraintDefinitionSegment -> constraintDefinitionSegment.getIndexColumns().size())
                    .allMatch(columnSize -> columnSize <= count);

            if (!isValid) {
                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_COMPOSITE_INDEX_MAX.getValue();
    }

}
