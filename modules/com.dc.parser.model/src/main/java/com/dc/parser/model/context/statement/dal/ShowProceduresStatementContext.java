package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowProceduresStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

public final class ShowProceduresStatementContext extends CommonSQLStatementContext {

    public ShowProceduresStatementContext(ShowProceduresStatement sqlStatement, String currentDatabase) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabase);
    }

    public void extractSqlAuthModel(final ShowProceduresStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.KEY_SHOW);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowProceduresStatement getSqlStatement() {
        return (ShowProceduresStatement) super.getSqlStatement();
    }
}
