package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.DropLibraryStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

public final class DropLibraryStatementContext extends CommonSQLStatementContext {

    public DropLibraryStatementContext(DropLibraryStatement sqlStatement, String currentDatabaseName) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final DropLibraryStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
        sqlAuthModel.setType("LIBRARY");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public DropLibraryStatement getSqlStatement() {
        return (DropLibraryStatement) super.getSqlStatement();
    }
}
