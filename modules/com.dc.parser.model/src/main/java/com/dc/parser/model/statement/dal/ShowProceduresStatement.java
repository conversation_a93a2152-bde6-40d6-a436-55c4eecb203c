package com.dc.parser.model.statement.dal;

import com.dc.parser.model.segment.dal.FromDatabaseSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Optional;

public abstract class ShowProceduresStatement extends AbstractSQLStatement implements DALStatement {

    public Optional<FromDatabaseSegment> getFromDatabase() {
        return Optional.empty();
    }

    public void setFromDatabase(FromDatabaseSegment fromDatabase) {
    }
}
