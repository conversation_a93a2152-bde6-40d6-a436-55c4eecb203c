package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.AlterXmlSchemaCollectionStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Alter xml schema collection statement context.
 */
@Getter
public final class AlterXmlSchemaCollectionStatementContext extends CommonSQLStatementContext {

    public AlterXmlSchemaCollectionStatementContext(final AlterXmlSchemaCollectionStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterXmlSchemaCollectionStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType("XML_SCHEMA_COLLECTION");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterXmlSchemaCollectionStatement getSqlStatement() {
        return (AlterXmlSchemaCollectionStatement) super.getSqlStatement();
    }
}
