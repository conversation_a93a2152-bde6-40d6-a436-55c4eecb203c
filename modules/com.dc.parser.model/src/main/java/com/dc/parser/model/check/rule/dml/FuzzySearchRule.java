package com.dc.parser.model.check.rule.dml;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.LiteralExpressionSegment;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import com.dc.parser.model.util.RowFilterRewriteUtil;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class FuzzySearchRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        List<ExpressionSegment> list = new ArrayList<>();

        if (sqlStatement instanceof DeleteStatement || sqlStatement instanceof UpdateStatement) {
            WhereSegment whereSegment;
            if (sqlStatement instanceof UpdateStatement) {
                whereSegment = ((UpdateStatement) sqlStatement).getWhere().orElse(null);
            } else {
                whereSegment = ((DeleteStatement) sqlStatement).getWhere().orElse(null);
            }

            if (whereSegment == null) {
                return CheckResult.DEFAULT_SUCCESS_RESULT;
            } else {
                List<LiteralExpressionSegment> literalExpressionSegments = RowFilterRewriteUtil.traverseAllLiteralExpressionSegment(whereSegment.getExpr());
                return this.checkSub(literalExpressionSegments, parameter);
            }
        } else {
            SelectStatement selectStatement = null;
            if (sqlStatement instanceof SelectStatement) {
                selectStatement = (SelectStatement) sqlStatement;
                if (selectStatement.getCombine().isPresent()) {
                    List<SelectStatement> selectStatements = RowFilterRewriteUtil.extractAllSelectFromSelectUnion(selectStatement, 0);
                    for (SelectStatement subSelect : selectStatements) {
                        RowFilterRewriteUtil.getAllExpressionSegmentFromSelectWhere(subSelect, list);
                    }
                }
            } else if (sqlStatement instanceof InsertStatement) {
                InsertStatement insertStatement = (InsertStatement) sqlStatement;
                selectStatement = insertStatement.getInsertSelect().isPresent() ? insertStatement.getInsertSelect().get().getSelect() : null;
            }

            if (selectStatement != null) {
                RowFilterRewriteUtil.getAllExpressionSegmentFromSelectWhere(selectStatement, list);
                List<LiteralExpressionSegment> literalExpressionSegments = RowFilterRewriteUtil.traverseAllLiteralExpressionSegment(list);
                return this.checkSub(literalExpressionSegments, parameter);
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    private CheckResult checkSub(List<LiteralExpressionSegment> literalExpressionSegments, CheckRuleParameter parameter) {
        Iterator<LiteralExpressionSegment> expressionSegmentIterator = literalExpressionSegments.iterator();

        String value;
        do {
            LiteralExpressionSegment literalExpressionSegment;
            do {
                if (!expressionSegmentIterator.hasNext()) {
                    return CheckResult.DEFAULT_SUCCESS_RESULT;
                }
                literalExpressionSegment = expressionSegmentIterator.next();
            } while (literalExpressionSegment.getLiterals() == null);

            value = literalExpressionSegment.getLiterals().toString();
        } while (!value.startsWith("%") && !value.startsWith("_"));

        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DML_CHECK_FUZZY_SEARCH.getValue();
    }

}
