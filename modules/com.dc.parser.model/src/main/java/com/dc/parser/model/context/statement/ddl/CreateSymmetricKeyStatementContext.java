package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.CreateSymmetricKeyStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Create symmetric key statement context.
 */
@Getter
public final class CreateSymmetricKeyStatementContext extends CommonSQLStatementContext {

    public CreateSymmetricKeyStatementContext(final CreateSymmetricKeyStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final CreateSymmetricKeyStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_CREATE);
        sqlAuthModel.setType("SYMMETRIC_KEY");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CreateSymmetricKeyStatement getSqlStatement() {
        return (CreateSymmetricKeyStatement) super.getSqlStatement();
    }
}
