
package com.dc.parser.model.segment.ddl.type;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.generic.OwnerAvailable;
import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Optional;

/**
 * Type segment.
 */
@RequiredArgsConstructor
@Getter
@Setter
public final class TypeSegment implements SQLSegment, OwnerAvailable {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final IdentifierValue identifier;
    
    private OwnerSegment owner;
    
    @Override
    public Optional<OwnerSegment> getOwner() {
        return Optional.ofNullable(owner);
    }
}
