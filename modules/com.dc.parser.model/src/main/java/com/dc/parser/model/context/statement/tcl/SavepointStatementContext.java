package com.dc.parser.model.context.statement.tcl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.tcl.SavepointStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Savepoint statement context.
 */
@Getter
public final class SavepointStatementContext extends CommonSQLStatementContext {

    public SavepointStatementContext(final SavepointStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    private void extractSqlAuthModel(final SavepointStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SAVE);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        sqlAuthModel.setName(sqlStatement.getSavepointName());
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public SavepointStatement getSqlStatement() {
        return (SavepointStatement) super.getSqlStatement();
    }
}
