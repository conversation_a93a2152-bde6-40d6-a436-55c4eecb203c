package com.dc.parser.model.check.flex.auth;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class CheckAuthResult {

    public static CheckAuthResult DEFAULT_SUCCESS_RESULT = new CheckAuthResult(true);

    private boolean success;

    private String message;

    public CheckAuthResult(boolean success) {
        this.success = success;
    }

    public static CheckAuthResult buildFailResult(String message) {
        return new CheckAuthResult(false, message);
    }
}
