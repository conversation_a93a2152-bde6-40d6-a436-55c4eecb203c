package com.dc.parser.model.exception;

import com.dc.infra.exception.sqlstate.XOpenSQLState;
import com.dc.infra.exception.type.kernel.category.MetaDataSQLException;

/**
 * Table not found exception.
 */
public final class TableNotFoundException extends MetaDataSQLException {

    private static final long serialVersionUID = -2507596759730534895L;

    public TableNotFoundException(final String tableName) {
        super(XOpenSQLState.NOT_FOUND, 2, "Table or view '%s' does not exist.", tableName);
    }

    public TableNotFoundException(final String tableName, final String storageUnitName) {
        super(XOpenSQLState.NOT_FOUND, 2, "Table or view '%s' does not exist in storage unit '%s'.", tableName, storageUnitName);
    }
}
