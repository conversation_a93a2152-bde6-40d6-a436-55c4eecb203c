package com.dc.parser.model.context.statement.dal;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dal.ShowCreateRoleStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

public final class ShowCreateRoleStatementContext extends CommonSQLStatementContext {

    public ShowCreateRoleStatementContext(ShowCreateRoleStatement sqlStatement, String currentDatabaseName) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final ShowCreateRoleStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_SHOW);
        sqlAuthModel.setType(SqlConstant.KEY_SHOW);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public ShowCreateRoleStatement getSqlStatement() {
        return (ShowCreateRoleStatement) super.getSqlStatement();
    }
}
