
package com.dc.parser.model.segment.ddl.index;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import com.dc.parser.model.segment.ddl.AlterDefinitionSegment;

/**
 * Rename index definition segment.
 */
@RequiredArgsConstructor
@Getter
public final class RenameIndexDefinitionSegment implements AlterDefinitionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final IndexSegment indexSegment;
    
    private final IndexSegment renameIndexSegment;
}
