package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Truncate table statement.
 */
@Getter
public abstract class TruncateStatement extends AbstractSQLStatement implements DDLStatement {
    
    private final Collection<SimpleTableSegment> tables = new LinkedList<>();
}
