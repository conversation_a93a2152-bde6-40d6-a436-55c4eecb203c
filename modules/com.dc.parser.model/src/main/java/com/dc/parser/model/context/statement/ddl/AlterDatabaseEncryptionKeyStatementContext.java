package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.AlterDatabaseEncryptionKeyStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Alter database encryption key statement context.
 */
@Getter
public final class AlterDatabaseEncryptionKeyStatementContext extends CommonSQLStatementContext {

    public AlterDatabaseEncryptionKeyStatementContext(final AlterDatabaseEncryptionKeyStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterDatabaseEncryptionKeyStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType("DATABASE_ENCRYPTION_KEY");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterDatabaseEncryptionKeyStatement getSqlStatement() {
        return (AlterDatabaseEncryptionKeyStatement) super.getSqlStatement();
    }
}
