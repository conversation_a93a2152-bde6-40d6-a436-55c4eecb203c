package com.dc.parser.model.check.rule.ddl;

import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.AddColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.ModifyColumnDefinitionSegment;
import com.dc.parser.model.segment.generic.DataTypeSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.Collection;

public class CharLengthRule implements SQLRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof CreateTableStatement) && !(sqlStatement instanceof AlterTableStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement checkStatement = (CreateTableStatement) sqlStatement;
            if (checkStatement.getRelationalTable().isPresent() && !checkStatement.getRelationalTable().get().getColumnDefinitions().isEmpty()) {
                Collection<ColumnDefinitionSegment> collect = checkStatement.getRelationalTable().get().getColumnDefinitions();
                for (ColumnDefinitionSegment columnDefinitionSegment : collect) {
                    if (buildFailResult(columnDefinitionSegment.getDataType())) {
                        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                    }
                }
            }
        } else {
            AlterTableStatement checkStatement = (AlterTableStatement) sqlStatement;
            for (AddColumnDefinitionSegment addColumnDefinition : checkStatement.getAddColumnDefinitions()) {
                if (addColumnDefinition.getColumnDefinitions() != null) {
                    for (ColumnDefinitionSegment columnDefinition : addColumnDefinition.getColumnDefinitions()) {
                        if (buildFailResult(columnDefinition.getDataType())) {
                            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                        }
                    }
                }
            }
            for (ModifyColumnDefinitionSegment modifyColumnDefinition : checkStatement.getModifyColumnDefinitions()) {
                if (modifyColumnDefinition.getColumnDefinition() != null) {
                    if (buildFailResult(modifyColumnDefinition.getColumnDefinition().getDataType())) {
                        return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                    }
                }
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    public static boolean buildFailResult(DataTypeSegment dataType) {
        if (dataType != null && "CHAR".equalsIgnoreCase(dataType.getDataTypeName()) && dataType.getDataLength() != null && dataType.getDataLength().getPrecision() != 0) {
            int len = dataType.getDataLength().getPrecision();
            return len > 20;
        }
        return false;
    }

    @Override
    public String getType() {
        return CheckRuleUniqueKey.DDL_CHECK_CHAR_LENGTH.getValue();
    }

}
