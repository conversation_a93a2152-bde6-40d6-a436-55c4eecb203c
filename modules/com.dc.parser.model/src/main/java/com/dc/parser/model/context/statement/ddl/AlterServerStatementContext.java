package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.AlterServerStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Alter server statement context.
 */
@Getter
public final class AlterServerStatementContext extends CommonSQLStatementContext {

    public AlterServerStatementContext(final AlterServerStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterServerStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType("SERVER");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterServerStatement getSqlStatement() {
        return (AlterServerStatement) super.getSqlStatement();
    }
}
