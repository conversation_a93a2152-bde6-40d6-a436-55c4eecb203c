
package com.dc.parser.model.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Logical operator.
 */
public enum LogicalOperator {
    
    AND, OR;
    
    private static final Map<String, LogicalOperator> MAPS = new HashMap<>(16, 1F);
    
    static {
        MAPS.put("and", AND);
        MAPS.put("And", AND);
        MAPS.put("aNd", AND);
        MAPS.put("anD", AND);
        MAPS.put("ANd", AND);
        MAPS.put("AnD", AND);
        MAPS.put("aND", AND);
        MAPS.put("AND", AND);
        MAPS.put("&&", AND);
        MAPS.put("or", OR);
        MAPS.put("Or", OR);
        MAPS.put("oR", OR);
        MAPS.put("OR", OR);
        MAPS.put("||", OR);
    }
    
    /**
     * Get logical operator value from text.
     *
     * @param text text
     * @return logical operator value
     */
    public static Optional<LogicalOperator> valueFrom(final String text) {
        return Optional.ofNullable(MAPS.get(text));
    }
}
