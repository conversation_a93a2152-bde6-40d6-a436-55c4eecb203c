package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Optional;

public abstract class RefreshStatement extends AbstractSQLStatement implements DDLStatement {

    public Optional<IdentifierValue> getRefreshTarget() {
        return Optional.empty();
    }

    public void setRefreshTarget(IdentifierValue refreshTarget) {
    }

    public Optional<SimpleTableSegment> getSimpleTable() {
        return Optional.empty();
    }

    public void setSimpleTable(SimpleTableSegment simpleTable) {
    }

    public Optional<PartitionPropertiesSegment> getPartitionProperties() {
        return Optional.empty();
    }

    public void setPartitionProperties(PartitionPropertiesSegment partitionProperties) {
    }
}
