package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.AlterWorkloadStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

public final class AlterWorkloadStatementContext extends CommonSQLStatementContext {

    public AlterWorkloadStatementContext(AlterWorkloadStatement sqlStatement, String currentDatabaseName) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterWorkloadStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType("WORKLOAD");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterWorkloadStatement getSqlStatement() {
        return (AlterWorkloadStatement) super.getSqlStatement();
    }
}
