package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.model.segment.database.impala.TableSampleSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

public abstract class ComputeStatsStatement extends AbstractSQLStatement implements DDLStatement {

    public Optional<SimpleTableSegment> getTable() {
        return Optional.empty();
    }

    public void setTable(SimpleTableSegment table) {
    }

    public List<ColumnSegment> getColumns() {
        return Collections.emptyList();
    }

    public void setColumns(List<ColumnSegment> columns) {
    }

    public boolean isIncremental() {
        return false;
    }

    public void setIncremental(boolean incremental) {
    }

    public Optional<PartitionPropertiesSegment> getPartitionProperties() {
        return Optional.empty();
    }

    public void setPartitionProperties(PartitionPropertiesSegment partitionProperties) {
    }

    public Optional<TableSampleSegment> getTableSample() {
        return Optional.empty();
    }

    public void setTableSample(TableSampleSegment tableSample) {
    }
}
