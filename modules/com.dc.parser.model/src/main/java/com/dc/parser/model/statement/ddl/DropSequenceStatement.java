package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.Optional;

/**
 * Drop sequence statement.
 */
@Getter
@Setter
public abstract class DropSequenceStatement extends AbstractSQLStatement implements DDLStatement {

    // TODO Postgre,GaussDB 适配
    private Collection<String> sequenceNames;

    private IdentifierValue sequenceName;

    private OwnerSegment ownerSegment;

    /**
     * Get owner.
     *
     * @return owner
     */
    public Optional<OwnerSegment> getOwner() {
        return Optional.ofNullable(ownerSegment);
    }
}
