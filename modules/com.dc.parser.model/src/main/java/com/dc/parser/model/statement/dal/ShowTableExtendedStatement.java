package com.dc.parser.model.statement.dal;

import com.dc.parser.model.segment.dal.FromDatabaseSegment;
import com.dc.parser.model.segment.ddl.partition.PartitionSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Optional;

public abstract class ShowTableExtendedStatement extends AbstractSQLStatement implements DALStatement {

    public Optional<FromDatabaseSegment> getFromDatabase() {
        return Optional.empty();
    }

    public void setFromDatabase(FromDatabaseSegment fromDatabase) {
    }

    public Optional<String> getLikePattern() {
        return Optional.empty();
    }

    public void setLikePattern(String likePattern) {
    }

    public Optional<PartitionSegment> getPartition() {
        return Optional.empty();
    }

    public void setPartition(PartitionSegment partition) {
    }
}
