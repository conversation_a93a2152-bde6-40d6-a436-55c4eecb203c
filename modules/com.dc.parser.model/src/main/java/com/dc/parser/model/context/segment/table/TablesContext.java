package com.dc.parser.model.context.segment.table;

import com.dc.infra.utils.CaseInsensitiveMap;
import com.dc.infra.utils.CaseInsensitiveSet;
import com.dc.parser.model.context.segment.select.subquery.SubqueryTableContext;
import com.dc.parser.model.context.segment.select.subquery.engine.SubqueryTableContextEngine;
import com.dc.parser.model.context.statement.dml.SelectStatementContext;
import com.dc.parser.model.annotation.HighFrequencyInvocation;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.SubqueryTableSegment;
import com.dc.parser.model.segment.generic.table.TableNameSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.ToString;

import java.util.*;

/**
 * Tables context.
 */
@Getter
@ToString
public final class TablesContext {

    @Getter(AccessLevel.NONE)
    private final Collection<TableSegment> tables = new LinkedList<>();

    private final Collection<SimpleTableSegment> simpleTables = new LinkedList<>();

    private final Collection<String> tableNames = new CaseInsensitiveSet<>();

    private final Collection<String> schemaNames = new CaseInsensitiveSet<>();

    private final Collection<String> databaseNames = new CaseInsensitiveSet<>();

    @Getter(AccessLevel.NONE)
    private final Map<String, Collection<SubqueryTableContext>> subqueryTables = new HashMap<>();

    public TablesContext(final SimpleTableSegment table) {
        this(null == table ? Collections.emptyList() : Collections.singletonList(table));
    }

    public TablesContext(final Collection<SimpleTableSegment> tables) {
        this(tables, Collections.emptyMap());
    }

    public TablesContext(final Collection<? extends TableSegment> tables, final Map<Integer, SelectStatementContext> subqueryContexts) {
        if (tables.isEmpty()) {
            return;
        }
        this.tables.addAll(tables);
        for (TableSegment each : tables) {
            if (each instanceof SimpleTableSegment) {
                SimpleTableSegment simpleTableSegment = (SimpleTableSegment) each;
                TableNameSegment tableName = simpleTableSegment.getTableName();
                if (!"DUAL".equalsIgnoreCase(tableName.getIdentifier().getValue())) {
                    simpleTables.add(simpleTableSegment);
                    tableNames.add(tableName.getIdentifier().getValue());
                    // TODO support bind with all statement contains table segement @duanzhengqiang
                    tableName.getTableBoundInfo().ifPresent(optional -> schemaNames.add(optional.getOriginalSchema().getValue()));
                    tableName.getTableBoundInfo().ifPresent(optional -> databaseNames.add(optional.getOriginalDatabase().getValue()));
                }
            }
            if (each instanceof SubqueryTableSegment) {
                subqueryTables.putAll(createSubqueryTables(subqueryContexts, (SubqueryTableSegment) each));
            }
        }
    }

    private Map<String, Collection<SubqueryTableContext>> createSubqueryTables(final Map<Integer, SelectStatementContext> subqueryContexts, final SubqueryTableSegment subqueryTable) {
        if (!subqueryContexts.containsKey(subqueryTable.getSubquery().getStartIndex())) {
            return Collections.emptyMap();
        }
        SelectStatementContext subqueryContext = subqueryContexts.get(subqueryTable.getSubquery().getStartIndex());
        Map<String, SubqueryTableContext> subqueryTableContexts = new SubqueryTableContextEngine().createSubqueryTableContexts(subqueryContext, subqueryTable.getAliasName().orElse(null));
        Map<String, Collection<SubqueryTableContext>> result = new HashMap<>(subqueryTableContexts.size(), 1F);
        for (SubqueryTableContext each : subqueryTableContexts.values()) {
            if (null != each.getAliasName()) {
                result.computeIfAbsent(each.getAliasName(), unused -> new LinkedList<>()).add(each);
            }
        }
        return result;
    }

    /**
     * Get database name.
     *
     * @return database name
     */
    public Optional<String> getDatabaseName() {
        return databaseNames.isEmpty() ? Optional.empty() : Optional.of(databaseNames.iterator().next());
    }

    /**
     * Get schema name.
     *
     * @return schema name
     */
    public Optional<String> getSchemaName() {
        return schemaNames.isEmpty() ? Optional.empty() : Optional.of(schemaNames.iterator().next());
    }
}
