package com.dc.parser.model.context.statement.ddl;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.ddl.AlterRemoteServiceBindingStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import lombok.Getter;

/**
 * Alter remote service binding statement context.
 */
@Getter
public final class AlterRemoteServiceBindingStatementContext extends CommonSQLStatementContext {

    public AlterRemoteServiceBindingStatementContext(final AlterRemoteServiceBindingStatement sqlStatement, final String currentDatabaseName) {
        super(sqlStatement);

        // 构造鉴权模型
        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final AlterRemoteServiceBindingStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_ALTER);
        sqlAuthModel.setType("REMOTE_SERVICE_BINDING");
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public AlterRemoteServiceBindingStatement getSqlStatement() {
        return (AlterRemoteServiceBindingStatement) super.getSqlStatement();
    }
}
