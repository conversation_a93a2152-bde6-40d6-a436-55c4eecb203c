package com.dc.parser.model.context.statement.dml;

import com.dc.parser.model.context.statement.CommonSQLStatementContext;
import com.dc.parser.model.statement.dml.CopyTestcaseStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;

public final class CopyTestcaseStatementContext extends CommonSQLStatementContext {

    public CopyTestcaseStatementContext(CopyTestcaseStatement sqlStatement, String currentDatabaseName) {
        super(sqlStatement);

        extractSqlAuthModel(sqlStatement, currentDatabaseName);
    }

    public void extractSqlAuthModel(final CopyTestcaseStatement sqlStatement, final String currentDatabaseName) {
        SqlAuthModel sqlAuthModel = new SqlAuthModel();
        sqlAuthModel.setOperation(SqlConstant.KEY_COPY);
        sqlAuthModel.setType(SqlConstant.KEY_TESTCASE);
        sqlAuthModel.setSchemaName(currentDatabaseName);
        addSqlAuthModel(sqlAuthModel);
    }

    @Override
    public CopyTestcaseStatement getSqlStatement() {
        return (CopyTestcaseStatement) super.getSqlStatement();
    }
}
