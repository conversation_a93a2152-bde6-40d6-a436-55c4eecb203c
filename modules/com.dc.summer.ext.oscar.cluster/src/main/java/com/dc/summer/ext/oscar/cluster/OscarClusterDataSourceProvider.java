package com.dc.summer.ext.oscar.cluster;


import com.dc.summer.DBException;
import com.dc.summer.ext.oscar.OscarDataSourceProvider;
import com.dc.summer.ext.oscar.cluster.model.OscarClusterDataSource;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;


public class OscarClusterDataSourceProvider extends OscarDataSourceProvider {

}
