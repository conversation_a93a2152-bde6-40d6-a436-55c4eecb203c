package com.dc.parser.ext.dm.statement.ddl;

import com.dc.parser.ext.dm.statement.DMStatement;
import com.dc.parser.model.segment.ddl.dblink.DBLinkSegment;
import com.dc.parser.model.statement.ddl.CreateDatabaseLinkStatement;

/**
 * DM create database link statement.
 */
public final class DMCreateDatabaseLinkStatement extends CreateDatabaseLinkStatement implements DMStatement {

    public DMCreateDatabaseLinkStatement(DBLinkSegment dbLinkSegment) {
        super(dbLinkSegment);
    }
}
