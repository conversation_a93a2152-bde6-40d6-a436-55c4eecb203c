package com.dc.parser.ext.spark.statement.ddl;

import com.dc.parser.model.enums.NamespaceType;
import com.dc.parser.ext.spark.statement.SparkStatement;
import com.dc.parser.model.statement.ddl.DropDatabaseStatement;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class SparkDropDatabaseStatement extends DropDatabaseStatement implements SparkStatement {

    private NamespaceType namespaceType;

    private boolean restrict;

    private boolean cascade;


}
