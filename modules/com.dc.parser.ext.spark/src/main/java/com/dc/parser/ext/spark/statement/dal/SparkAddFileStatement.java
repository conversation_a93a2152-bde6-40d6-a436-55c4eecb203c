package com.dc.parser.ext.spark.statement.dal;

import com.dc.parser.ext.spark.statement.SparkStatement;
import com.dc.parser.model.statement.dal.AddFileStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;

@Setter
public final class SparkAddFileStatement extends AddFileStatement implements SparkStatement {
    @Getter
    private final List<String> fileOrDirectoryNames = new LinkedList<>();
}
