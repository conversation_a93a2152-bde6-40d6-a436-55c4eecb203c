package com.dc.parser.ext.spark.statement.ddl;

import com.dc.parser.ext.spark.statement.SparkStatement;
import com.dc.parser.model.segment.ddl.bucket.BucketSegment;
import com.dc.parser.model.segment.ddl.cluster.ClusterBySegment;
import com.dc.parser.model.segment.ddl.cluster.SkewSegment;
import com.dc.parser.model.segment.ddl.collation.CollationSegment;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.partition.PartitionSegment;
import com.dc.parser.model.segment.generic.CommentSegment;
import com.dc.parser.model.segment.generic.LocationSegment;
import com.dc.parser.model.segment.generic.RowFormatSegment;
import com.dc.parser.model.segment.generic.StoredSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.ReplaceTableStatement;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Setter
@Getter
public final class SparkReplaceTableStatement extends ReplaceTableStatement implements SparkStatement {

    private boolean createOr;

    @Getter(AccessLevel.NONE)
    private SimpleTableSegment table;

    private final Collection<ColumnDefinitionSegment> columnDefinitionSegments = new LinkedList<>();

    private final Collection<ConstraintDefinitionSegment> constraintDefinitionSegments = new LinkedList<>();

    @Getter(AccessLevel.NONE)
    private SimpleTableSegment tableProvider;


    //--createTableClauses---------------start---------------------
    //TODO value将来需要替换为expression
    private Map<String, String> options;

    private final List<PartitionSegment> partitionSegments = new LinkedList<>();

    private List<SkewSegment> skewSegments;

    private List<ClusterBySegment> clusterBySegments;

    private List<BucketSegment> bucketSegments;

    private List<RowFormatSegment> rowFormatSegments;

    private List<StoredSegment> storedSegments;

    private List<LocationSegment> locationSegments;

    private List<CommentSegment> commentSegments;

    private List<CollationSegment> collationSegments;

    private final Map<String, String> tblProperties = new LinkedHashMap<>();
    //-------------------------------end-------------------------

    //TODO 还缺少一个query属性，待处理


    //methods

    public Optional<SimpleTableSegment> getTable() {
        return Optional.ofNullable(table);
    }

    public Optional<SimpleTableSegment> getTableProvider() {
        return Optional.ofNullable(tableProvider);
    }

}
