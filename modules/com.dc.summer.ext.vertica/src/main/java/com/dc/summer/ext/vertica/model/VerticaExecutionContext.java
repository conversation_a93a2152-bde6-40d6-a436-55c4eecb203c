package com.dc.summer.ext.vertica.model;

import com.dc.summer.ext.generic.model.GenericExecutionContext;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class VerticaExecutionContext extends GenericExecutionContext {


    public VerticaExecutionContext(JDBCRemoteInstance instance, String purpose) {
        super(instance, purpose);
    }

    @Override
    protected List<Map<String, Object>> initContextBootstrap(DBRProgressMonitor monitor, boolean autoCommit) throws DBCException {
        getBootstrapSettings().setInitQueries(Collections.singleton("SELECT session_id as PID FROM v_monitor.current_session"));
        List<Map<String, Object>> list = execContextBootstrap(monitor, autoCommit);
        if (list.size() > 0) {
            super.setProcessId(String.valueOf(list.get(0).get("PID")));
        }
        return list;
    }
}
