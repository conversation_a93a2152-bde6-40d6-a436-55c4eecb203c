package com.dc.parser.ext.vertica.segment.expr;

import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.Collection;

@RequiredArgsConstructor
@Setter
@Getter
public class ArrayExpressionSegment implements ExpressionSegment {
    private final int startIndex;
    private final int stopIndex;
    private final String text;

    private final Collection<ExpressionSegment> expressions;

}
