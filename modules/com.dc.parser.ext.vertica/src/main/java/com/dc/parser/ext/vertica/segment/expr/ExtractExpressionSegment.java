package com.dc.parser.ext.vertica.segment.expr;

import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Setter
@Getter
public class ExtractExpressionSegment implements ExpressionSegment {
    private final int startIndex;

    private final int stopIndex;
    private final ExpressionSegment expr;
    private final String text;

}
