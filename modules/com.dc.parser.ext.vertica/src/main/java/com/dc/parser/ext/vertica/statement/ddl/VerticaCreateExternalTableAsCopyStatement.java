package com.dc.parser.ext.vertica.statement.ddl;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Vertica create external table as copy statement.
 */
@Setter
@Getter
public final class VerticaCreateExternalTableAsCopyStatement extends CreateTableStatement implements VerticaStatement {
    private IdentifierValue source;

    public Optional<IdentifierValue> getSource() {
        return Optional.ofNullable(source);
    }
}
