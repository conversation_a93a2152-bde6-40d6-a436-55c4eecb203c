package com.dc.parser.ext.vertica.statement.ddl;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.statement.ddl.AlterSchemaStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * vertica alter schema statement.
 */
@Setter
@Getter
public final class VerticaAlterSchemaStatement extends AlterSchemaStatement implements VerticaStatement {
    private Collection<DatabaseSegment> schemas = new LinkedList<>();

}
