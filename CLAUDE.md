# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Summer is a database management tool developed for the DataCaptain project. It provides database connection, execution, result set operations, and related business functions through APIs. The project is built on top of DBeaver's Eclipse RCP framework and has been converted from C-end application to B-end services.

### Key Technologies
- Java 11
- Spring Boot 2.7.18
- Maven multi-module architecture
- ANTLR4 for SQL parsing
- Eclipse RCP framework adaptation
- Various database drivers and connections

## Build Commands

### Prerequisites
- Java 11 or higher
- Maven 3.6+
- Git client

### Core Build Commands
```bash
# Compile the entire project
mvn compile

# Clean and compile
mvn clean compile

# Package with encryption (production)
mvn package -Dxjar.password=data-captain

# Generate ANTLR4 parsers (automatic via profile when GenerateAntlr4.java exists)
# The project automatically downloads and updates ANTLR4 grammar files from remote repository

# Version management
mvn versions:set -DnewVersion=1.0
mvn versions:commit
```

### ANTLR4 Grammar Generation
The project has an automated system for managing ANTLR4 grammar files:
- `GenerateAntlr4.java` automatically clones/pulls grammar files from `*********************:614ac62ae43534781c03c36e/DC-Java/antlr4.git`
- Grammar files are distributed to appropriate parser modules under `modules/com.dc.parser.ext.*`
- Maven ANTLR4 plugin generates Java classes in `src/main/java/${artifactId}.parser.autogen`

### Testing
- No standard test framework configuration found in main pom.xml
- Individual modules may have their own test configurations

## Project Architecture

### Three-Tier Module Structure

1. **Base Layer** (`base/`): Global utilities and Spring Boot foundations
   - `com.dc.springboot.core`: Core API functionality, components, configurations
   - `com.dc.springboot.base`: Basic interface features (encryption, logging, graceful shutdown)
   - `com.dc.springboot.auth`: Interface authentication (AKSK)
   - `com.dc.repository.mysql`: Persistence layer with MyBatis Plus
   - `com.dc.repository.redis`: Cache layer with pub/sub
   - `com.dc.utils`: Global utilities
   - `com.dc.infra`: Parsing tools

2. **Modules Layer** (`modules/`): Pluggable execution and parsing functionality
   - `com.dc.parser.ext.*`: Database-specific parser extensions (Oracle, MySQL, PostgreSQL, etc.)
   - `com.dc.parser.model`: Core parsing functionality, workflows, interfaces
   - `com.dc.summer.ext.*`: Database-specific execution extensions
   - `com.dc.summer.model`: Core execution models and workflows
   - `com.dc.summer.exec`: External execution entry points
   - `com.dc.summer.parser.*`: SQL parsing and splitting tools
   - `com.dc.summer.registry`: Plugin registration based on plugin.xml

3. **Product Layer** (`product/`): Specific business applications
   - `com.dc.summer`: User execution application (single connection)
     - `dc-summer-pe`: Main startup application
     - `dc-summer-pa`: Ping An customized version
   - `com.dc.iceage`: Internal execution application (connection pool)
     - `dc-iceage-pe`: Main startup application
   - `com.dc.job`: XXL Job based task scheduling
     - `dc-job-admin`: Scheduler application
     - `dc-job`: Task executor application
   - `com.dc.broker`: Python library proxy execution application

### Key Design Patterns
- Eclipse RCP plugin architecture with OSGi-style registration
- Chain of responsibility pattern for SQL processing (ChainBuilder)
- Factory pattern for database-specific implementations
- Spring Boot auto-configuration for modular assembly

## Application Startup

### Environment Variables (Recommended for Local Development)
```bash
export java_env=dev
export dc_summer_port=8088
export dc_iceage_port=8008
export dc_proxy_port=8008
export logback=classpath:logback.xml
export log_level=DEBUG
```

### Spring Boot Configuration
- JVM arguments required: `--add-opens java.base/jdk.internal.vm.annotation=ALL-UNNAMED --add-opens java.base/jdk.internal.perf=ALL-UNNAMED`
- Configuration files: `application.yml`, `application-dev.yml` for each service
- Nacos configuration center support available (disabled by default in `bootstrap.yml`)

### Service Startup Scripts
Each application includes shell scripts in `src/main/assembly/bin/`:
- `startup.sh`: Start the service
- `shutdown.sh`: Stop the service
- `logger-info.sh`, `logger-debug.sh`: Log level management

### Main Applications and Ports
- `dc-summer-pe`: Port 8088 (database execution service)
- `dc-iceage-pe`: Port 8008 (internal execution with connection pool)
- `dc-job-admin`: Task scheduler admin
- `dc-job`: Task executor
- `dc-broker`: Python library proxy

## Key Entry Points

### Summer Application
- Entry: `com.dc.summer.boot.SummerApplication` (Spring Boot)
- Initialization: `Runner.init()` method
- Main controller: `ExecuteController` (REST API)
- Connection management: `DataSourceConnectionHandler`
- Business logic: `ExecuteHandler`

### DBeaver Integration
- Platform initialization: `DBWorkbench.getPlatform()`
- Custom implementations for configuration, data source registration, driver management
- Conversion from Eclipse RCP desktop to server-side services

## Database Support

The project supports extensive database types through dedicated modules:
- **Relational**: Oracle, MySQL, PostgreSQL, SQL Server, DB2, etc.
- **Big Data**: Hive, Spark, Impala, ClickHouse, etc.
- **Cloud**: AWS Athena, Google BigQuery, Snowflake, etc.
- **NoSQL**: MongoDB, Redis, Elasticsearch, etc.
- **Specialty**: GIS databases, time series, graph databases

Each database has corresponding parser (`com.dc.parser.ext.*`) and executor (`com.dc.summer.ext.*`) modules.

## Monitoring and Operations

### Health Checks
- Spring Actuator endpoints available at `/monitor/*`
- Graceful shutdown support

### Logging
- Logback configuration in `logback-spring.xml`
- SQL execution logging to `logs/sql/` and `logs/life-cycle/`
- Application logs: `dc-summer-pe.log`, `dc-iceage-pe.log`

### Configuration Management
- Local configuration files (default)
- Nacos configuration center support (configurable)
- Environment-specific profiles (`application-dev.yml`, `application-env.yml`)

## Commit Message Standards

Follow conventional commits format:
```
<type>(<scope>): <subject>

<body>

<footer>
```

**Types**: feat, fix, docs, style, refactor, perf, test, chore, revert, build, ci

**Scopes**: Use camelCase module names, `base` for framework changes, `misc` for miscellaneous, `all` for wide refactoring.

## Development Workflow

### Branch Naming
- Format: `<type>_<module>_<YYYYMMDD>`
- Types: `feature`, `bugfix`, `refactor`
- Example: `feature_parser_20230117`

### CI/CD
- Manual packaging: Maven with `-Dxjar.password=data-captain`
- Automated builds: Jenkins at http://*************:8080
- Deployment: Automated deployment system at http://*************:8000

## Important Notes

- The project uses custom ANTLR4 grammar management - do not manually modify generated parser files
- Eclipse RCP dependencies are carefully managed - avoid version conflicts
- Database drivers are externally managed in `drivers/` directory
- Connection pooling vs single connection is a key architectural decision between iceage and summer products